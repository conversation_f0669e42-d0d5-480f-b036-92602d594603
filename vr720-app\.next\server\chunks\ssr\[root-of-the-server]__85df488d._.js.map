{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/components/Camera/MobileCameraCapture.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport { Camera, RotateCcw, Download, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';\n\ninterface MobileCameraCaptureProps {\n  onPhotoCapture: (photoDataUrl: string) => void;\n  onClose: () => void;\n}\n\nexport const MobileCameraCapture: React.FC<MobileCameraCaptureProps> = ({\n  onPhotoCapture,\n  onClose\n}) => {\n  const [stream, setStream] = useState<MediaStream | null>(null);\n  const [isConnecting, setIsConnecting] = useState(false);\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [capturedPhoto, setCapturedPhoto] = useState<string>('');\n  const [error, setError] = useState<string>('');\n  const [connectionStep, setConnectionStep] = useState<string>('');\n  \n  const videoRef = useRef<HTMLVideoElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  // 启动相机\n  const startCamera = useCallback(async () => {\n    setIsConnecting(true);\n    setError('');\n    setConnectionStep('Checking device support...');\n\n    try {\n      // Check basic support\n      if (!navigator.mediaDevices?.getUserMedia) {\n        throw new Error('Your browser does not support camera functionality. Please use Chrome, Safari, or Firefox');\n      }\n\n      setConnectionStep('Checking permissions...');\n      \n      // 简化的约束配置，优先兼容性\n      const constraints = [\n        // 最基本的配置\n        { video: true },\n        // 指定后置摄像头\n        { video: { facingMode: 'environment' } },\n        // 指定前置摄像头作为备选\n        { video: { facingMode: 'user' } }\n      ];\n\n      let cameraStream = null;\n      let lastError = null;\n\n      for (let i = 0; i < constraints.length; i++) {\n        try {\n          setConnectionStep(`Connecting to camera (${i + 1}/${constraints.length})...`);\n\n          cameraStream = await navigator.mediaDevices.getUserMedia(constraints[i]);\n\n          if (cameraStream && cameraStream.active) {\n            console.log(`Camera connected successfully with config ${i + 1}`);\n            break;\n          }\n        } catch (err: any) {\n          console.log(`Config ${i + 1} failed:`, err);\n          lastError = err;\n          if (cameraStream) {\n            cameraStream.getTracks().forEach(track => track.stop());\n          }\n        }\n      }\n\n      if (!cameraStream || !cameraStream.active) {\n        throw lastError || new Error('Unable to connect to camera');\n      }\n\n      setConnectionStep('Setting up video stream...');\n\n      // 设置视频元素\n      if (videoRef.current) {\n        videoRef.current.srcObject = cameraStream;\n        \n        // Wait for metadata to load\n        const metadataPromise = new Promise<void>((resolve, reject) => {\n          if (!videoRef.current) {\n            reject(new Error('Video element not available'));\n            return;\n          }\n\n          videoRef.current.onloadedmetadata = () => {\n            console.log('Video metadata loaded successfully');\n            resolve();\n          };\n\n          videoRef.current.onerror = (error) => {\n            console.error('Video loading error:', error);\n            reject(new Error('Video loading failed'));\n          };\n\n          // Timeout handling\n          setTimeout(() => {\n            reject(new Error('Video loading timeout'));\n          }, 10000);\n        });\n\n        await metadataPromise;\n\n        setConnectionStep('Starting video playback...');\n\n        // Try to play video\n        try {\n          await videoRef.current.play();\n          console.log('Video playback successful');\n        } catch (playError) {\n          console.log('Auto-play failed, user interaction required:', playError);\n          // Mobile devices may require user interaction to play\n        }\n\n        setStream(cameraStream);\n        setIsStreaming(true);\n        setConnectionStep('');\n      }\n\n    } catch (err: any) {\n      console.error('Camera startup failed:', err);\n\n      let errorMessage = 'Camera connection failed: ';\n\n      if (err.name === 'NotAllowedError') {\n        errorMessage += 'Permission denied. Please allow camera access';\n      } else if (err.name === 'NotFoundError') {\n        errorMessage += 'No camera device found';\n      } else if (err.name === 'NotSupportedError') {\n        errorMessage += 'Camera not supported on this device';\n      } else if (err.name === 'NotReadableError') {\n        errorMessage += 'Camera is being used by another application';\n      } else {\n        errorMessage += err.message || 'Unknown error';\n      }\n\n      setError(errorMessage);\n    } finally {\n      setIsConnecting(false);\n    }\n  }, []);\n\n  // 停止相机\n  const stopCamera = useCallback(() => {\n    if (stream) {\n      stream.getTracks().forEach(track => track.stop());\n      setStream(null);\n      setIsStreaming(false);\n    }\n  }, [stream]);\n\n  // 拍照\n  const takePhoto = useCallback(() => {\n    if (!videoRef.current || !canvasRef.current || !isStreaming) return;\n\n    const video = videoRef.current;\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    if (!ctx) return;\n\n    // 设置画布尺寸\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n\n    // 绘制视频帧到画布\n    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // 获取图片数据\n    const photoDataUrl = canvas.toDataURL('image/jpeg', 0.9);\n    setCapturedPhoto(photoDataUrl);\n  }, [isStreaming]);\n\n  // 确认照片\n  const confirmPhoto = useCallback(() => {\n    if (capturedPhoto) {\n      onPhotoCapture(capturedPhoto);\n      stopCamera();\n      onClose();\n    }\n  }, [capturedPhoto, onPhotoCapture, stopCamera, onClose]);\n\n  // 重新拍照\n  const retakePhoto = useCallback(() => {\n    setCapturedPhoto('');\n  }, []);\n\n  // 组件卸载时清理\n  useEffect(() => {\n    return () => {\n      if (stream) {\n        stream.getTracks().forEach(track => track.stop());\n      }\n    };\n  }, [stream]);\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 flex flex-col\">\n      {/* Header control bar */}\n      <div className=\"flex items-center justify-between p-4 bg-black/80 text-white\">\n        <button\n          onClick={onClose}\n          className=\"text-white hover:text-gray-300\"\n        >\n          <RotateCcw size={24} />\n        </button>\n        <h2 className=\"text-lg font-medium\">Take Photo</h2>\n        <div className=\"w-6\" /> {/* Spacer for centering */}\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"flex-1 relative\">\n        {/* 相机预览 */}\n        {isStreaming && !capturedPhoto && (\n          <video\n            ref={videoRef}\n            autoPlay\n            playsInline\n            muted\n            className=\"w-full h-full object-cover\"\n          />\n        )}\n\n        {/* Captured photo preview */}\n        {capturedPhoto && (\n          <img\n            src={capturedPhoto}\n            alt=\"Captured photo\"\n            className=\"w-full h-full object-cover\"\n          />\n        )}\n\n        {/* Connection status */}\n        {isConnecting && (\n          <div className=\"absolute inset-0 bg-black flex flex-col items-center justify-center text-white\">\n            <Loader2 size={48} className=\"animate-spin mb-4\" />\n            <p className=\"text-lg mb-2\">Connecting to camera...</p>\n            {connectionStep && (\n              <p className=\"text-sm text-gray-300\">{connectionStep}</p>\n            )}\n          </div>\n        )}\n\n        {/* 错误状态 */}\n        {error && (\n          <div className=\"absolute inset-0 bg-black flex flex-col items-center justify-center text-white p-6\">\n            <AlertCircle size={48} className=\"text-red-500 mb-4\" />\n            <p className=\"text-lg mb-4 text-center\">{error}</p>\n            <button\n              onClick={startCamera}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg\"\n            >\n              重试\n            </button>\n          </div>\n        )}\n\n        {/* Initial state */}\n        {!isConnecting && !isStreaming && !error && (\n          <div className=\"absolute inset-0 bg-black flex flex-col items-center justify-center text-white\">\n            <Camera size={64} className=\"mb-6 text-gray-400\" />\n            <h3 className=\"text-xl mb-2\">Ready to Capture</h3>\n            <p className=\"text-gray-300 mb-6 text-center px-6\">\n              Tap the button below to start using the camera\n            </p>\n            <button\n              onClick={startCamera}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg\"\n            >\n              Start Camera\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* 底部控制栏 */}\n      <div className=\"p-6 bg-black/80\">\n        {isStreaming && !capturedPhoto && (\n          <div className=\"flex justify-center\">\n            <button\n              onClick={takePhoto}\n              className=\"w-16 h-16 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors\"\n            >\n              <div className=\"w-12 h-12 bg-gray-800 rounded-full\" />\n            </button>\n          </div>\n        )}\n\n        {capturedPhoto && (\n          <div className=\"flex justify-center space-x-4\">\n            <button\n              onClick={retakePhoto}\n              className=\"bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg\"\n            >\n              Retake\n            </button>\n            <button\n              onClick={confirmPhoto}\n              className=\"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center\"\n            >\n              <CheckCircle size={20} className=\"mr-2\" />\n              Use Photo\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* 隐藏的画布用于拍照 */}\n      <canvas ref={canvasRef} className=\"hidden\" />\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAUO,MAAM,sBAA0D,CAAC,EACtE,cAAc,EACd,OAAO,EACR;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,gBAAgB;QAChB,SAAS;QACT,kBAAkB;QAElB,IAAI;YACF,sBAAsB;YACtB,IAAI,CAAC,UAAU,YAAY,EAAE,cAAc;gBACzC,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAElB,gBAAgB;YAChB,MAAM,cAAc;gBAClB,SAAS;gBACT;oBAAE,OAAO;gBAAK;gBACd,UAAU;gBACV;oBAAE,OAAO;wBAAE,YAAY;oBAAc;gBAAE;gBACvC,cAAc;gBACd;oBAAE,OAAO;wBAAE,YAAY;oBAAO;gBAAE;aACjC;YAED,IAAI,eAAe;YACnB,IAAI,YAAY;YAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;gBAC3C,IAAI;oBACF,kBAAkB,CAAC,sBAAsB,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY,MAAM,CAAC,IAAI,CAAC;oBAE5E,eAAe,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE;oBAEvE,IAAI,gBAAgB,aAAa,MAAM,EAAE;wBACvC,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,IAAI,GAAG;wBAChE;oBACF;gBACF,EAAE,OAAO,KAAU;oBACjB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE;oBACvC,YAAY;oBACZ,IAAI,cAAc;wBAChB,aAAa,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;oBACtD;gBACF;YACF;YAEA,IAAI,CAAC,gBAAgB,CAAC,aAAa,MAAM,EAAE;gBACzC,MAAM,aAAa,IAAI,MAAM;YAC/B;YAEA,kBAAkB;YAElB,SAAS;YACT,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;gBAE7B,4BAA4B;gBAC5B,MAAM,kBAAkB,IAAI,QAAc,CAAC,SAAS;oBAClD,IAAI,CAAC,SAAS,OAAO,EAAE;wBACrB,OAAO,IAAI,MAAM;wBACjB;oBACF;oBAEA,SAAS,OAAO,CAAC,gBAAgB,GAAG;wBAClC,QAAQ,GAAG,CAAC;wBACZ;oBACF;oBAEA,SAAS,OAAO,CAAC,OAAO,GAAG,CAAC;wBAC1B,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,OAAO,IAAI,MAAM;oBACnB;oBAEA,mBAAmB;oBACnB,WAAW;wBACT,OAAO,IAAI,MAAM;oBACnB,GAAG;gBACL;gBAEA,MAAM;gBAEN,kBAAkB;gBAElB,oBAAoB;gBACpB,IAAI;oBACF,MAAM,SAAS,OAAO,CAAC,IAAI;oBAC3B,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,WAAW;oBAClB,QAAQ,GAAG,CAAC,gDAAgD;gBAC5D,sDAAsD;gBACxD;gBAEA,UAAU;gBACV,eAAe;gBACf,kBAAkB;YACpB;QAEF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YAExC,IAAI,eAAe;YAEnB,IAAI,IAAI,IAAI,KAAK,mBAAmB;gBAClC,gBAAgB;YAClB,OAAO,IAAI,IAAI,IAAI,KAAK,iBAAiB;gBACvC,gBAAgB;YAClB,OAAO,IAAI,IAAI,IAAI,KAAK,qBAAqB;gBAC3C,gBAAgB;YAClB,OAAO,IAAI,IAAI,IAAI,KAAK,oBAAoB;gBAC1C,gBAAgB;YAClB,OAAO;gBACL,gBAAgB,IAAI,OAAO,IAAI;YACjC;YAEA,SAAS;QACX,SAAU;YACR,gBAAgB;QAClB;IACF,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,QAAQ;YACV,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAC9C,UAAU;YACV,eAAe;QACjB;IACF,GAAG;QAAC;KAAO;IAEX,KAAK;IACL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,aAAa;QAE7D,MAAM,QAAQ,SAAS,OAAO;QAC9B,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,CAAC,KAAK;QAEV,SAAS;QACT,OAAO,KAAK,GAAG,MAAM,UAAU;QAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;QAEjC,WAAW;QACX,IAAI,SAAS,CAAC,OAAO,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;QAEtD,SAAS;QACT,MAAM,eAAe,OAAO,SAAS,CAAC,cAAc;QACpD,iBAAiB;IACnB,GAAG;QAAC;KAAY;IAEhB,OAAO;IACP,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,eAAe;YACjB,eAAe;YACf;YACA;QACF;IACF,GAAG;QAAC;QAAe;QAAgB;QAAY;KAAQ;IAEvD,OAAO;IACP,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,iBAAiB;IACnB,GAAG,EAAE;IAEL,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,QAAQ;gBACV,OAAO,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YAChD;QACF;IACF,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;;;;;;kCAEnB,8OAAC;wBAAG,WAAU;kCAAsB;;;;;;kCACpC,8OAAC;wBAAI,WAAU;;;;;;oBAAQ;;;;;;;0BAIzB,8OAAC;gBAAI,WAAU;;oBAEZ,eAAe,CAAC,+BACf,8OAAC;wBACC,KAAK;wBACL,QAAQ;wBACR,WAAW;wBACX,KAAK;wBACL,WAAU;;;;;;oBAKb,+BACC,8OAAC;wBACC,KAAK;wBACL,KAAI;wBACJ,WAAU;;;;;;oBAKb,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6MAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC7B,8OAAC;gCAAE,WAAU;0CAAe;;;;;;4BAC3B,gCACC,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;oBAM3C,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CACjC,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;0CACzC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;oBAOJ,CAAC,gBAAgB,CAAC,eAAe,CAAC,uBACjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uMAAA,CAAA,SAAM;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC5B,8OAAC;gCAAG,WAAU;0CAAe;;;;;;0CAC7B,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;oBACZ,eAAe,CAAC,+BACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;oBAKpB,+BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,qNAAA,CAAA,cAAW;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;0BAQlD,8OAAC;gBAAO,KAAK;gBAAW,WAAU;;;;;;;;;;;;AAGxC", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/components/VR/MobileVRViewer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { ArrowLeft } from 'lucide-react';\n\ninterface MobileVRViewerProps {\n  panoramaUrl: string;\n  title?: string;\n  description?: string;\n  onClose: () => void;\n}\n\nexport const MobileVRViewer: React.FC<MobileVRViewerProps> = ({\n  panoramaUrl,\n  title = \"VR720° Mobile Tour\",\n  description = \"Mobile-optimized 720° virtual reality experience\",\n  onClose\n}) => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n\n  // A-Frame移动端优化VR场景\n  useEffect(() => {\n    if (typeof window !== 'undefined' && panoramaUrl && containerRef.current) {\n      setIsLoading(true);\n      \n      // 创建移动端优化的A-Frame VR720场景\n      const sceneHTML = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=no\">\n          <script src=\"https://aframe.io/releases/1.4.0/aframe.min.js\"></script>\n          <style>\n            body { \n              margin: 0; \n              overflow: hidden; \n              background: #000;\n              touch-action: none;\n              -webkit-user-select: none;\n              -moz-user-select: none;\n              -ms-user-select: none;\n              user-select: none;\n            }\n            #vr-scene { \n              width: 100vw; \n              height: 100vh; \n              position: fixed;\n              top: 0;\n              left: 0;\n            }\n            .mobile-controls {\n              position: fixed;\n              top: 10px;\n              left: 10px;\n              right: 10px;\n              z-index: 1000;\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            }\n            .control-group {\n              display: flex;\n              gap: 8px;\n            }\n            .mobile-btn {\n              background: rgba(0,0,0,0.8);\n              color: white;\n              border: none;\n              padding: 12px;\n              border-radius: 50%;\n              cursor: pointer;\n              backdrop-filter: blur(10px);\n              font-size: 14px;\n              width: 44px;\n              height: 44px;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              touch-action: manipulation;\n            }\n            .mobile-btn:active {\n              background: rgba(0,0,0,0.9);\n              transform: scale(0.95);\n            }\n            .vr720-badge {\n              position: fixed;\n              bottom: 20px;\n              left: 50%;\n              transform: translateX(-50%);\n              background: linear-gradient(45deg, #ff6b6b, #4ecdc4);\n              color: white;\n              padding: 8px 20px;\n              border-radius: 20px;\n              font-weight: bold;\n              font-size: 12px;\n              z-index: 1000;\n              animation: pulse 3s infinite;\n            }\n            @keyframes pulse {\n              0% { transform: translateX(-50%) scale(1); }\n              50% { transform: translateX(-50%) scale(1.05); }\n              100% { transform: translateX(-50%) scale(1); }\n            }\n            .mobile-info {\n              position: fixed;\n              bottom: 60px;\n              left: 50%;\n              transform: translateX(-50%);\n              background: rgba(0,0,0,0.8);\n              color: white;\n              padding: 8px 16px;\n              border-radius: 15px;\n              text-align: center;\n              backdrop-filter: blur(10px);\n              z-index: 1000;\n              font-size: 11px;\n              max-width: 90vw;\n            }\n            .loading-overlay {\n              position: fixed;\n              top: 0;\n              left: 0;\n              width: 100vw;\n              height: 100vh;\n              background: #000;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              z-index: 2000;\n              flex-direction: column;\n            }\n            .spinner {\n              width: 40px;\n              height: 40px;\n              border: 3px solid rgba(255,255,255,0.3);\n              border-top: 3px solid #4ecdc4;\n              border-radius: 50%;\n              animation: spin 1s linear infinite;\n              margin-bottom: 20px;\n            }\n            @keyframes spin {\n              0% { transform: rotate(0deg); }\n              100% { transform: rotate(360deg); }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"mobile-controls\">\n            <div class=\"control-group\">\n              <button class=\"mobile-btn\" onclick=\"window.parent.postMessage('back', '*')\" title=\"返回\">\n                ←\n              </button>\n              <button class=\"mobile-btn\" onclick=\"toggleInfo()\" title=\"信息\">\n                ℹ️\n              </button>\n            </div>\n            <div class=\"control-group\">\n              <button class=\"mobile-btn\" onclick=\"toggle720Mode()\" title=\"720°模式\">\n                🔄\n              </button>\n              <button class=\"mobile-btn\" onclick=\"window.parent.postMessage('share', '*')\" title=\"分享\">\n                📤\n              </button>\n            </div>\n          </div>\n          \n          <div class=\"vr720-badge\" id=\"mode-badge\">\n            📱 VR720° MOBILE\n          </div>\n          \n          <a-scene \n            id=\"vr-scene\" \n            embedded \n            style=\"height: 100vh; width: 100vw;\"\n            vr-mode-ui=\"enabled: true\"\n            background=\"color: #000\"\n            device-orientation-permission-ui=\"enabled: false\"\n          >\n            <a-assets>\n              <img id=\"panorama\" src=\"${panoramaUrl}\" crossorigin=\"anonymous\">\n            </a-assets>\n            \n            <!-- 移动端优化的720°天空球 -->\n            <a-sky \n              id=\"mobile-sky\"\n              src=\"#panorama\" \n              rotation=\"0 -90 0\"\n              animation=\"property: rotation; to: 0 630 0; loop: true; dur: 150000; easing: linear\"\n            ></a-sky>\n            \n            <!-- 移动端VR相机 -->\n            <a-camera \n              id=\"mobile-camera\"\n              look-controls=\"enabled: true; reverseMouseDrag: false; touchEnabled: true; magicWindowTrackingEnabled: true\"\n              wasd-controls=\"enabled: false\"\n              position=\"0 1.6 0\"\n              fov=\"80\"\n            >\n              <a-cursor \n                color=\"#4ecdc4\" \n                opacity=\"0.8\"\n                geometry=\"primitive: ring; radiusInner: 0.015; radiusOuter: 0.025\"\n                material=\"color: #4ecdc4; shader: flat\"\n                animation=\"property: scale; to: 1.3 1.3 1.3; loop: true; dir: alternate; dur: 1500\"\n              ></a-cursor>\n            </a-camera>\n            \n            <!-- 环境光 -->\n            <a-light type=\"ambient\" color=\"#505050\"></a-light>\n            <a-light type=\"directional\" position=\"1 1 1\" color=\"#ffffff\" intensity=\"0.3\"></a-light>\n            \n            <!-- 移动端标题 -->\n            <a-text\n              value=\"${title}\"\n              position=\"0 3 -3\"\n              align=\"center\"\n              color=\"#4ecdc4\"\n              opacity=\"0.9\"\n              font=\"size: 24; weight: bold\"\n              animation=\"property: opacity; to: 0.6; loop: true; dir: alternate; dur: 4000\"\n            ></a-text>\n            \n            <!-- 720°移动端指示器 -->\n            <a-ring\n              position=\"0 -1.5 -2\"\n              color=\"#ff6b6b\"\n              radius-inner=\"0.3\"\n              radius-outer=\"0.4\"\n              opacity=\"0.8\"\n              animation=\"property: rotation; to: 0 0 720; loop: true; dur: 12000; easing: linear\"\n            ></a-ring>\n            \n            <a-text\n              value=\"720° Mobile VR\"\n              position=\"0 -2 -2\"\n              align=\"center\"\n              color=\"#ff6b6b\"\n              font=\"size: 14; weight: bold\"\n              opacity=\"0.9\"\n            ></a-text>\n          </a-scene>\n          \n          <div class=\"mobile-info\" id=\"info-panel\" style=\"display: none;\">\n            <div style=\"font-weight: bold; margin-bottom: 4px;\">${title}</div>\n            <div style=\"font-size: 10px; opacity: 0.8;\">${description}</div>\n            <div style=\"font-size: 10px; margin-top: 4px; color: #4ecdc4;\">\n              👆 拖拽查看 • 📱 倾斜设备 • 🥽 VR模式\n            </div>\n          </div>\n\n          <div class=\"loading-overlay\" id=\"loading\" style=\"display: flex;\">\n            <div class=\"spinner\"></div>\n            <div style=\"color: white; text-align: center;\">\n              <div style=\"font-size: 16px; font-weight: bold; margin-bottom: 8px;\">Loading VR720°</div>\n              <div style=\"font-size: 12px; opacity: 0.8;\">Mobile VR Experience</div>\n            </div>\n          </div>\n\n          <script>\n            let is720Mode = true;\n            let showInfo = false;\n            \n            function toggle720Mode() {\n              const sky = document.querySelector('#mobile-sky');\n              const badge = document.querySelector('#mode-badge');\n              \n              if (is720Mode) {\n                // 切换到360°模式\n                sky.setAttribute('animation', 'property: rotation; to: 0 270 0; loop: true; dur: 80000; easing: linear');\n                badge.textContent = '📱 VR360° MOBILE';\n                badge.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';\n                is720Mode = false;\n              } else {\n                // 切换到720°模式\n                sky.setAttribute('animation', 'property: rotation; to: 0 630 0; loop: true; dur: 150000; easing: linear');\n                badge.textContent = '📱 VR720° MOBILE';\n                badge.style.background = 'linear-gradient(45deg, #ff6b6b, #4ecdc4)';\n                is720Mode = true;\n              }\n            }\n            \n            function toggleInfo() {\n              const panel = document.querySelector('#info-panel');\n              showInfo = !showInfo;\n              panel.style.display = showInfo ? 'block' : 'none';\n            }\n            \n            // 监听父窗口消息\n            window.addEventListener('message', function(event) {\n              if (event.data === 'back') {\n                window.parent.postMessage('back', '*');\n              } else if (event.data === 'share') {\n                window.parent.postMessage('share', '*');\n              } else if (event.data === 'toggleInfo') {\n                toggleInfo();\n              } else if (event.data === 'toggle720') {\n                toggle720Mode();\n              }\n            });\n            \n            // 移动端优化\n            document.addEventListener('touchstart', function(e) {\n              // 阻止默认的触摸行为\n              if (e.target.closest('.mobile-btn')) {\n                e.preventDefault();\n              }\n            }, { passive: false });\n            \n            // 隐藏加载界面\n            setTimeout(() => {\n              document.querySelector('#loading').style.display = 'none';\n            }, 3000);\n            \n            // 设备方向变化处理\n            window.addEventListener('orientationchange', function() {\n              setTimeout(() => {\n                const scene = document.querySelector('a-scene');\n                if (scene) {\n                  scene.resize();\n                }\n              }, 500);\n            });\n            \n            // 移动端性能优化\n            if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {\n              // 移动设备特定优化\n              const scene = document.querySelector('a-scene');\n              scene.setAttribute('renderer', 'antialias: false; colorManagement: true; sortObjects: true; physicallyCorrectLights: true; maxCanvasWidth: 1920; maxCanvasHeight: 1920');\n            }\n          </script>\n        </body>\n        </html>\n      `;\n\n      // 创建iframe - 修复按钮点击问题\n      const iframe = document.createElement('iframe');\n      iframe.style.width = '100%';\n      iframe.style.height = '100%';\n      iframe.style.border = 'none';\n      iframe.style.position = 'fixed';\n      iframe.style.top = '0';\n      iframe.style.left = '0';\n      iframe.style.zIndex = '100'; // 降低z-index，让外部按钮可以点击\n      iframe.style.pointerEvents = 'auto';\n      iframe.srcdoc = sceneHTML;\n\n      // 监听iframe加载完成\n      iframe.onload = () => {\n        setTimeout(() => {\n          setIsLoading(false);\n        }, 1000);\n      };\n      \n      // 监听iframe消息\n      const handleMessage = (event: MessageEvent) => {\n        if (event.data === 'back') {\n          onClose();\n        } else if (event.data === 'share') {\n          // 移动端分享功能\n          if (navigator.share) {\n            navigator.share({\n              title: title,\n              text: description,\n              url: window.location.href\n            }).catch(console.error);\n          } else {\n            // 复制链接到剪贴板\n            navigator.clipboard?.writeText(window.location.href).then(() => {\n              alert('链接已复制到剪贴板！');\n            }).catch(() => {\n              alert('分享功能不可用');\n            });\n          }\n        }\n      };\n      \n      window.addEventListener('message', handleMessage);\n      \n      containerRef.current.appendChild(iframe);\n      \n      return () => {\n        window.removeEventListener('message', handleMessage);\n        if (containerRef.current && iframe) {\n          containerRef.current.removeChild(iframe);\n        }\n      };\n    }\n  }, [panoramaUrl, title, description, onClose]);\n\n\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 overflow-hidden\">\n      <div ref={containerRef} className=\"w-full h-full\" />\n\n      {/* 移动端加载指示器 */}\n      {isLoading && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-black z-50\">\n          <div className=\"text-white text-center\">\n            <div className=\"relative mb-6\">\n              <div className=\"animate-spin rounded-full h-16 w-16 border-b-4 border-gradient-to-r from-pink-500 to-blue-500 mx-auto\"></div>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"text-2xl\">📱</div>\n              </div>\n            </div>\n            <p className=\"text-lg font-bold bg-gradient-to-r from-pink-500 to-blue-500 bg-clip-text text-transparent\">\n              Loading Mobile VR720°\n            </p>\n            <p className=\"text-sm text-gray-400 mt-2\">Optimized for mobile devices</p>\n            <div className=\"mt-4 flex items-center justify-center space-x-2\">\n              <div className=\"w-2 h-2 bg-pink-500 rounded-full animate-pulse\"></div>\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\" style={{animationDelay: '0.2s'}}></div>\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\" style={{animationDelay: '0.4s'}}></div>\n            </div>\n            <div className=\"mt-4 text-xs text-gray-500\">\n              Touch • Gyroscope • VR Ready\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 移动端控制按钮层 - 确保在iframe之上 */}\n      <div className=\"absolute inset-0 pointer-events-none z-50\">\n        {/* 顶部控制栏 */}\n        <div className=\"absolute top-0 left-0 right-0 p-4 pointer-events-none\">\n          <div className=\"flex justify-between items-center\">\n            {/* 左侧按钮组 */}\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={onClose}\n                className=\"pointer-events-auto bg-black/80 text-white p-3 rounded-full backdrop-blur-sm shadow-lg active:scale-95 transition-transform\"\n                style={{ minWidth: '48px', minHeight: '48px' }}\n              >\n                <ArrowLeft size={20} />\n              </button>\n              <button\n                onClick={() => {\n                  // 发送信息切换消息到iframe\n                  const iframe = containerRef.current?.querySelector('iframe');\n                  if (iframe?.contentWindow) {\n                    iframe.contentWindow.postMessage('toggleInfo', window.location.origin);\n                  }\n                }}\n                className=\"pointer-events-auto bg-black/80 text-white p-3 rounded-full backdrop-blur-sm shadow-lg active:scale-95 transition-transform\"\n                style={{ minWidth: '48px', minHeight: '48px' }}\n              >\n                ℹ️\n              </button>\n            </div>\n\n            {/* 右侧按钮组 */}\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => {\n                  // 发送720°模式切换消息到iframe\n                  const iframe = containerRef.current?.querySelector('iframe');\n                  if (iframe?.contentWindow) {\n                    iframe.contentWindow.postMessage('toggle720', window.location.origin);\n                  }\n                }}\n                className=\"pointer-events-auto bg-black/80 text-white p-3 rounded-full backdrop-blur-sm shadow-lg active:scale-95 transition-transform\"\n                style={{ minWidth: '48px', minHeight: '48px' }}\n              >\n                🔄\n              </button>\n              <button\n                onClick={() => {\n                  // 移动端分享功能\n                  if (navigator.share) {\n                    navigator.share({\n                      title: title,\n                      text: description,\n                      url: window.location.href\n                    }).catch(console.error);\n                  } else {\n                    navigator.clipboard?.writeText(window.location.href).then(() => {\n                      alert('链接已复制到剪贴板！');\n                    }).catch(() => {\n                      alert('分享功能不可用');\n                    });\n                  }\n                }}\n                className=\"pointer-events-auto bg-black/80 text-white p-3 rounded-full backdrop-blur-sm shadow-lg active:scale-95 transition-transform\"\n                style={{ minWidth: '48px', minHeight: '48px' }}\n              >\n                📤\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 底部状态指示器 */}\n        <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 pointer-events-none\">\n          <div className=\"bg-black/80 text-white px-4 py-2 rounded-full backdrop-blur-sm text-sm\">\n            📱 VR720° Mobile - 拖拽查看全景\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYO,MAAM,iBAAgD,CAAC,EAC5D,WAAW,EACX,QAAQ,oBAAoB,EAC5B,cAAc,kDAAkD,EAChE,OAAO,EACR;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAG3C,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAA0E;;QA6W1E;IACF,GAAG;QAAC;QAAa;QAAO;QAAa;KAAQ;IAI7C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;;;;;YAGjC,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAW;;;;;;;;;;;;;;;;;sCAG9B,8OAAC;4BAAE,WAAU;sCAA6F;;;;;;sCAG1G,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAC1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAC,gBAAgB;oCAAM;;;;;;8CAC9F,8OAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAC,gBAAgB;oCAAM;;;;;;;;;;;;sCAEjG,8OAAC;4BAAI,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAQlD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAO;gDAAE,UAAU;gDAAQ,WAAW;4CAAO;sDAE7C,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;;;;;;sDAEnB,8OAAC;4CACC,SAAS;gDACP,kBAAkB;gDAClB,MAAM,SAAS,aAAa,OAAO,EAAE,cAAc;gDACnD,IAAI,QAAQ,eAAe;oDACzB,OAAO,aAAa,CAAC,WAAW,CAAC,cAAc,OAAO,QAAQ,CAAC,MAAM;gDACvE;4CACF;4CACA,WAAU;4CACV,OAAO;gDAAE,UAAU;gDAAQ,WAAW;4CAAO;sDAC9C;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;gDACP,sBAAsB;gDACtB,MAAM,SAAS,aAAa,OAAO,EAAE,cAAc;gDACnD,IAAI,QAAQ,eAAe;oDACzB,OAAO,aAAa,CAAC,WAAW,CAAC,aAAa,OAAO,QAAQ,CAAC,MAAM;gDACtE;4CACF;4CACA,WAAU;4CACV,OAAO;gDAAE,UAAU;gDAAQ,WAAW;4CAAO;sDAC9C;;;;;;sDAGD,8OAAC;4CACC,SAAS;gDACP,UAAU;gDACV,IAAI,UAAU,KAAK,EAAE;oDACnB,UAAU,KAAK,CAAC;wDACd,OAAO;wDACP,MAAM;wDACN,KAAK,OAAO,QAAQ,CAAC,IAAI;oDAC3B,GAAG,KAAK,CAAC,QAAQ,KAAK;gDACxB,OAAO;oDACL,UAAU,SAAS,EAAE,UAAU,OAAO,QAAQ,CAAC,IAAI,EAAE,KAAK;wDACxD,MAAM;oDACR,GAAG,MAAM;wDACP,MAAM;oDACR;gDACF;4CACF;4CACA,WAAU;4CACV,OAAO;gDAAE,UAAU;gDAAQ,WAAW;4CAAO;sDAC9C;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAyE;;;;;;;;;;;;;;;;;;;;;;;AAOlG", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/utils/panoramaStitcher.ts"], "sourcesContent": ["/**\n * Panorama Stitching Utilities\n * This is a simplified implementation for demonstration purposes.\n * In a real application, you would use more sophisticated computer vision algorithms.\n */\n\nexport interface StitchingOptions {\n  outputWidth?: number;\n  outputHeight?: number;\n  blendMode?: 'linear' | 'multiband';\n  seamFinding?: boolean;\n}\n\nexport interface StitchingResult {\n  panoramaUrl: string;\n  success: boolean;\n  error?: string;\n  metadata: {\n    inputImages: number;\n    outputDimensions: { width: number; height: number };\n    processingTime: number;\n  };\n}\n\n/**\n * Create a panorama from multiple images\n * This is a simplified version that creates a horizontal panorama\n */\nexport async function stitchImagesToPanorama(\n  imageUrls: string[],\n  options: StitchingOptions = {}\n): Promise<StitchingResult> {\n  const startTime = Date.now();\n  \n  try {\n    if (imageUrls.length === 0) {\n      throw new Error('No images provided for stitching');\n    }\n\n    // If only one image, convert it to panoramic format\n    if (imageUrls.length === 1) {\n      return createSingleImagePanorama(imageUrls[0], options);\n    }\n\n    // Load all images\n    const images = await Promise.all(\n      imageUrls.map(url => loadImage(url))\n    );\n\n    // Calculate output dimensions\n    const outputWidth = options.outputWidth || 2048;\n    const outputHeight = options.outputHeight || 1024;\n\n    // Create canvas for stitching\n    const canvas = document.createElement('canvas');\n    canvas.width = outputWidth;\n    canvas.height = outputHeight;\n    const ctx = canvas.getContext('2d');\n\n    if (!ctx) {\n      throw new Error('Failed to create canvas context');\n    }\n\n    // Clear canvas with black background\n    ctx.fillStyle = '#000000';\n    ctx.fillRect(0, 0, outputWidth, outputHeight);\n\n    // Simple horizontal stitching\n    await stitchImagesHorizontally(ctx, images, outputWidth, outputHeight);\n\n    // Convert to data URL\n    const panoramaUrl = canvas.toDataURL('image/jpeg', 0.9);\n\n    const processingTime = Date.now() - startTime;\n\n    return {\n      panoramaUrl,\n      success: true,\n      metadata: {\n        inputImages: imageUrls.length,\n        outputDimensions: { width: outputWidth, height: outputHeight },\n        processingTime\n      }\n    };\n\n  } catch (error) {\n    return {\n      panoramaUrl: '',\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      metadata: {\n        inputImages: imageUrls.length,\n        outputDimensions: { width: 0, height: 0 },\n        processingTime: Date.now() - startTime\n      }\n    };\n  }\n}\n\n/**\n * Convert a single image to panoramic format\n */\nasync function createSingleImagePanorama(\n  imageUrl: string,\n  options: StitchingOptions\n): Promise<StitchingResult> {\n  const startTime = Date.now();\n  \n  try {\n    const image = await loadImage(imageUrl);\n    \n    // Calculate panoramic dimensions (2:1 aspect ratio)\n    const outputWidth = options.outputWidth || 2048;\n    const outputHeight = options.outputHeight || outputWidth / 2;\n\n    const canvas = document.createElement('canvas');\n    canvas.width = outputWidth;\n    canvas.height = outputHeight;\n    const ctx = canvas.getContext('2d');\n\n    if (!ctx) {\n      throw new Error('Failed to create canvas context');\n    }\n\n    // Fill with black background\n    ctx.fillStyle = '#000000';\n    ctx.fillRect(0, 0, outputWidth, outputHeight);\n\n    // Calculate scaling to fit height and center horizontally\n    const scale = outputHeight / image.height;\n    const scaledWidth = image.width * scale;\n    const x = (outputWidth - scaledWidth) / 2;\n\n    // Draw the image centered\n    ctx.drawImage(image, x, 0, scaledWidth, outputHeight);\n\n    // If the image is narrower than the canvas, repeat it to fill\n    if (scaledWidth < outputWidth) {\n      // Repeat on the left\n      let leftX = x - scaledWidth;\n      while (leftX > -scaledWidth) {\n        ctx.drawImage(image, leftX, 0, scaledWidth, outputHeight);\n        leftX -= scaledWidth;\n      }\n\n      // Repeat on the right\n      let rightX = x + scaledWidth;\n      while (rightX < outputWidth) {\n        ctx.drawImage(image, rightX, 0, scaledWidth, outputHeight);\n        rightX += scaledWidth;\n      }\n    }\n\n    const panoramaUrl = canvas.toDataURL('image/jpeg', 0.9);\n    const processingTime = Date.now() - startTime;\n\n    return {\n      panoramaUrl,\n      success: true,\n      metadata: {\n        inputImages: 1,\n        outputDimensions: { width: outputWidth, height: outputHeight },\n        processingTime\n      }\n    };\n\n  } catch (error) {\n    return {\n      panoramaUrl: imageUrl, // Fallback to original image\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      metadata: {\n        inputImages: 1,\n        outputDimensions: { width: 0, height: 0 },\n        processingTime: Date.now() - startTime\n      }\n    };\n  }\n}\n\n/**\n * Stitch images horizontally with blending\n */\nasync function stitchImagesHorizontally(\n  ctx: CanvasRenderingContext2D,\n  images: HTMLImageElement[],\n  outputWidth: number,\n  outputHeight: number\n): Promise<void> {\n  const imageWidth = outputWidth / images.length;\n  const overlapWidth = imageWidth * 0.1; // 10% overlap\n\n  for (let i = 0; i < images.length; i++) {\n    const image = images[i];\n    const x = i * (imageWidth - overlapWidth);\n    \n    // Scale image to fit\n    const scale = outputHeight / image.height;\n    const scaledWidth = Math.min(image.width * scale, imageWidth + overlapWidth);\n\n    // Draw image\n    ctx.drawImage(image, x, 0, scaledWidth, outputHeight);\n\n    // Apply blending for overlap regions\n    if (i > 0) {\n      applyBlending(ctx, x, 0, overlapWidth, outputHeight);\n    }\n  }\n}\n\n/**\n * Apply simple linear blending for overlap regions\n */\nfunction applyBlending(\n  ctx: CanvasRenderingContext2D,\n  x: number,\n  y: number,\n  width: number,\n  height: number\n): void {\n  const imageData = ctx.getImageData(x, y, width, height);\n  const data = imageData.data;\n\n  for (let i = 0; i < width; i++) {\n    const alpha = i / width; // Linear blend factor\n    \n    for (let j = 0; j < height; j++) {\n      const pixelIndex = (j * width + i) * 4;\n      \n      // Apply alpha blending\n      data[pixelIndex + 3] = Math.round(data[pixelIndex + 3] * alpha);\n    }\n  }\n\n  ctx.putImageData(imageData, x, y);\n}\n\n/**\n * Load an image from URL\n */\nfunction loadImage(url: string): Promise<HTMLImageElement> {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.crossOrigin = 'anonymous';\n    \n    img.onload = () => resolve(img);\n    img.onerror = () => reject(new Error(`Failed to load image: ${url}`));\n    \n    img.src = url;\n  });\n}\n\n/**\n * Validate if images are suitable for panorama stitching\n */\nexport function validateImagesForStitching(imageUrls: string[]): {\n  valid: boolean;\n  warnings: string[];\n  recommendations: string[];\n} {\n  const warnings: string[] = [];\n  const recommendations: string[] = [];\n\n  if (imageUrls.length === 0) {\n    warnings.push('No images provided');\n    return { valid: false, warnings, recommendations };\n  }\n\n  if (imageUrls.length === 1) {\n    recommendations.push('Single image will be converted to panoramic format');\n    recommendations.push('For better results, capture multiple overlapping photos');\n  }\n\n  if (imageUrls.length > 10) {\n    warnings.push('Large number of images may result in slower processing');\n    recommendations.push('Consider using 3-8 images for optimal results');\n  }\n\n  recommendations.push('Ensure images have 30-50% overlap for best stitching');\n  recommendations.push('Use consistent lighting and camera settings');\n  recommendations.push('Keep the camera level during capture');\n\n  return {\n    valid: true,\n    warnings,\n    recommendations\n  };\n}\n\n/**\n * Get optimal panorama dimensions based on input images\n */\nexport function getOptimalPanoramaDimensions(imageCount: number): {\n  width: number;\n  height: number;\n} {\n  // Standard panoramic aspect ratios\n  const aspectRatio = 2; // 2:1 for equirectangular panoramas\n  \n  let baseWidth: number;\n  \n  if (imageCount === 1) {\n    baseWidth = 2048;\n  } else if (imageCount <= 3) {\n    baseWidth = 3072;\n  } else if (imageCount <= 6) {\n    baseWidth = 4096;\n  } else {\n    baseWidth = 6144;\n  }\n\n  return {\n    width: baseWidth,\n    height: baseWidth / aspectRatio\n  };\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AAwBM,eAAe,uBACpB,SAAmB,EACnB,UAA4B,CAAC,CAAC;IAE9B,MAAM,YAAY,KAAK,GAAG;IAE1B,IAAI;QACF,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,OAAO,0BAA0B,SAAS,CAAC,EAAE,EAAE;QACjD;QAEA,kBAAkB;QAClB,MAAM,SAAS,MAAM,QAAQ,GAAG,CAC9B,UAAU,GAAG,CAAC,CAAA,MAAO,UAAU;QAGjC,8BAA8B;QAC9B,MAAM,cAAc,QAAQ,WAAW,IAAI;QAC3C,MAAM,eAAe,QAAQ,YAAY,IAAI;QAE7C,8BAA8B;QAC9B,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM;QAClB;QAEA,qCAAqC;QACrC,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,aAAa;QAEhC,8BAA8B;QAC9B,MAAM,yBAAyB,KAAK,QAAQ,aAAa;QAEzD,sBAAsB;QACtB,MAAM,cAAc,OAAO,SAAS,CAAC,cAAc;QAEnD,MAAM,iBAAiB,KAAK,GAAG,KAAK;QAEpC,OAAO;YACL;YACA,SAAS;YACT,UAAU;gBACR,aAAa,UAAU,MAAM;gBAC7B,kBAAkB;oBAAE,OAAO;oBAAa,QAAQ;gBAAa;gBAC7D;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,aAAa;YACb,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,UAAU;gBACR,aAAa,UAAU,MAAM;gBAC7B,kBAAkB;oBAAE,OAAO;oBAAG,QAAQ;gBAAE;gBACxC,gBAAgB,KAAK,GAAG,KAAK;YAC/B;QACF;IACF;AACF;AAEA;;CAEC,GACD,eAAe,0BACb,QAAgB,EAChB,OAAyB;IAEzB,MAAM,YAAY,KAAK,GAAG;IAE1B,IAAI;QACF,MAAM,QAAQ,MAAM,UAAU;QAE9B,oDAAoD;QACpD,MAAM,cAAc,QAAQ,WAAW,IAAI;QAC3C,MAAM,eAAe,QAAQ,YAAY,IAAI,cAAc;QAE3D,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,IAAI,CAAC,KAAK;YACR,MAAM,IAAI,MAAM;QAClB;QAEA,6BAA6B;QAC7B,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,aAAa;QAEhC,0DAA0D;QAC1D,MAAM,QAAQ,eAAe,MAAM,MAAM;QACzC,MAAM,cAAc,MAAM,KAAK,GAAG;QAClC,MAAM,IAAI,CAAC,cAAc,WAAW,IAAI;QAExC,0BAA0B;QAC1B,IAAI,SAAS,CAAC,OAAO,GAAG,GAAG,aAAa;QAExC,8DAA8D;QAC9D,IAAI,cAAc,aAAa;YAC7B,qBAAqB;YACrB,IAAI,QAAQ,IAAI;YAChB,MAAO,QAAQ,CAAC,YAAa;gBAC3B,IAAI,SAAS,CAAC,OAAO,OAAO,GAAG,aAAa;gBAC5C,SAAS;YACX;YAEA,sBAAsB;YACtB,IAAI,SAAS,IAAI;YACjB,MAAO,SAAS,YAAa;gBAC3B,IAAI,SAAS,CAAC,OAAO,QAAQ,GAAG,aAAa;gBAC7C,UAAU;YACZ;QACF;QAEA,MAAM,cAAc,OAAO,SAAS,CAAC,cAAc;QACnD,MAAM,iBAAiB,KAAK,GAAG,KAAK;QAEpC,OAAO;YACL;YACA,SAAS;YACT,UAAU;gBACR,aAAa;gBACb,kBAAkB;oBAAE,OAAO;oBAAa,QAAQ;gBAAa;gBAC7D;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,aAAa;YACb,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,UAAU;gBACR,aAAa;gBACb,kBAAkB;oBAAE,OAAO;oBAAG,QAAQ;gBAAE;gBACxC,gBAAgB,KAAK,GAAG,KAAK;YAC/B;QACF;IACF;AACF;AAEA;;CAEC,GACD,eAAe,yBACb,GAA6B,EAC7B,MAA0B,EAC1B,WAAmB,EACnB,YAAoB;IAEpB,MAAM,aAAa,cAAc,OAAO,MAAM;IAC9C,MAAM,eAAe,aAAa,KAAK,cAAc;IAErD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,QAAQ,MAAM,CAAC,EAAE;QACvB,MAAM,IAAI,IAAI,CAAC,aAAa,YAAY;QAExC,qBAAqB;QACrB,MAAM,QAAQ,eAAe,MAAM,MAAM;QACzC,MAAM,cAAc,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,OAAO,aAAa;QAE/D,aAAa;QACb,IAAI,SAAS,CAAC,OAAO,GAAG,GAAG,aAAa;QAExC,qCAAqC;QACrC,IAAI,IAAI,GAAG;YACT,cAAc,KAAK,GAAG,GAAG,cAAc;QACzC;IACF;AACF;AAEA;;CAEC,GACD,SAAS,cACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,KAAa,EACb,MAAc;IAEd,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG,GAAG,OAAO;IAChD,MAAM,OAAO,UAAU,IAAI;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC9B,MAAM,QAAQ,IAAI,OAAO,sBAAsB;QAE/C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,MAAM,aAAa,CAAC,IAAI,QAAQ,CAAC,IAAI;YAErC,uBAAuB;YACvB,IAAI,CAAC,aAAa,EAAE,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG;QAC3D;IACF;IAEA,IAAI,YAAY,CAAC,WAAW,GAAG;AACjC;AAEA;;CAEC,GACD,SAAS,UAAU,GAAW;IAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,WAAW,GAAG;QAElB,IAAI,MAAM,GAAG,IAAM,QAAQ;QAC3B,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM,CAAC,sBAAsB,EAAE,KAAK;QAEnE,IAAI,GAAG,GAAG;IACZ;AACF;AAKO,SAAS,2BAA2B,SAAmB;IAK5D,MAAM,WAAqB,EAAE;IAC7B,MAAM,kBAA4B,EAAE;IAEpC,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,SAAS,IAAI,CAAC;QACd,OAAO;YAAE,OAAO;YAAO;YAAU;QAAgB;IACnD;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,gBAAgB,IAAI,CAAC;QACrB,gBAAgB,IAAI,CAAC;IACvB;IAEA,IAAI,UAAU,MAAM,GAAG,IAAI;QACzB,SAAS,IAAI,CAAC;QACd,gBAAgB,IAAI,CAAC;IACvB;IAEA,gBAAgB,IAAI,CAAC;IACrB,gBAAgB,IAAI,CAAC;IACrB,gBAAgB,IAAI,CAAC;IAErB,OAAO;QACL,OAAO;QACP;QACA;IACF;AACF;AAKO,SAAS,6BAA6B,UAAkB;IAI7D,mCAAmC;IACnC,MAAM,cAAc,GAAG,oCAAoC;IAE3D,IAAI;IAEJ,IAAI,eAAe,GAAG;QACpB,YAAY;IACd,OAAO,IAAI,cAAc,GAAG;QAC1B,YAAY;IACd,OAAO,IAAI,cAAc,GAAG;QAC1B,YAAY;IACd,OAAO;QACL,YAAY;IACd;IAEA,OAAO;QACL,OAAO;QACP,QAAQ,YAAY;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/components/Upload/PhotoUploader.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useCallback } from 'react';\nimport {\n  Upload,\n  Camera,\n  X,\n  Check,\n  Image as ImageIcon,\n  Eye,\n  Download,\n  RotateCw,\n  Trash2,\n  AlertTriangle,\n  Info\n} from 'lucide-react';\nimport { stitchImagesToPanorama, validateImagesForStitching, getOptimalPanoramaDimensions } from '@/utils/panoramaStitcher';\n\ninterface PhotoUploaderProps {\n  onPhotosUploaded: (photos: string[]) => void;\n  onPanoramaCreated: (panoramaUrl: string) => void;\n  onClose: () => void;\n  maxPhotos?: number;\n}\n\ninterface UploadedPhoto {\n  id: string;\n  url: string;\n  name: string;\n  size: number;\n}\n\nexport const PhotoUploader: React.FC<PhotoUploaderProps> = ({\n  onPhotosUploaded,\n  onPanoramaCreated,\n  onClose,\n  maxPhotos = 10\n}) => {\n  const [uploadedPhotos, setUploadedPhotos] = useState<UploadedPhoto[]>([]);\n  const [isDragging, setIsDragging] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [isCreatingPanorama, setIsCreatingPanorama] = useState(false);\n  const [panoramaProgress, setPanoramaProgress] = useState(0);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Handle file selection\n  const handleFileSelect = useCallback((files: FileList | null) => {\n    if (!files) return;\n\n    setIsProcessing(true);\n    const newPhotos: UploadedPhoto[] = [];\n\n    Array.from(files).forEach((file, index) => {\n      if (file.type.startsWith('image/') && uploadedPhotos.length + newPhotos.length < maxPhotos) {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          const url = e.target?.result as string;\n          newPhotos.push({\n            id: `${Date.now()}-${index}`,\n            url,\n            name: file.name,\n            size: file.size\n          });\n\n          // If all files are processed\n          if (newPhotos.length === Math.min(files.length, maxPhotos - uploadedPhotos.length)) {\n            setUploadedPhotos(prev => [...prev, ...newPhotos]);\n            setIsProcessing(false);\n          }\n        };\n        reader.readAsDataURL(file);\n      }\n    });\n  }, [uploadedPhotos.length, maxPhotos]);\n\n  // Handle drag and drop\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n    handleFileSelect(e.dataTransfer.files);\n  }, [handleFileSelect]);\n\n  // Handle file input change\n  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    handleFileSelect(e.target.files);\n  }, [handleFileSelect]);\n\n  // Remove photo\n  const removePhoto = useCallback((id: string) => {\n    setUploadedPhotos(prev => prev.filter(photo => photo.id !== id));\n  }, []);\n\n  // Create panorama from photos\n  const createPanorama = useCallback(async () => {\n    if (uploadedPhotos.length === 0) return;\n\n    setIsCreatingPanorama(true);\n    setPanoramaProgress(0);\n\n    try {\n      // Validate images for stitching\n      const photoUrls = uploadedPhotos.map(photo => photo.url);\n      const validation = validateImagesForStitching(photoUrls);\n\n      if (!validation.valid) {\n        throw new Error('Images are not suitable for panorama creation');\n      }\n\n      setPanoramaProgress(25);\n\n      // Stitch images into panorama\n      const result = await stitchImagesToPanorama(photoUrls, {\n        outputWidth: 4096,\n        outputHeight: 2048\n      });\n\n      setPanoramaProgress(75);\n\n      if (!result.success) {\n        throw new Error(result.error || 'Failed to create panorama');\n      }\n\n      setPanoramaProgress(100);\n\n      // Return both individual photos and the panorama\n      onPhotosUploaded(photoUrls);\n      onPanoramaCreated(result.panoramaUrl);\n\n      setTimeout(() => {\n        onClose();\n      }, 500);\n\n    } catch (error) {\n      console.error('Panorama creation failed:', error);\n      alert('Failed to create panorama: ' + (error instanceof Error ? error.message : 'Unknown error'));\n    } finally {\n      setIsCreatingPanorama(false);\n      setPanoramaProgress(0);\n    }\n  }, [uploadedPhotos, onPhotosUploaded, onPanoramaCreated, onClose]);\n\n  // Format file size\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Upload Photos</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-500 hover:text-gray-700 p-1\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-4 max-h-[70vh] overflow-y-auto\">\n          {/* Upload Area */}\n          <div\n            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n              isDragging \n                ? 'border-blue-500 bg-blue-50' \n                : 'border-gray-300 hover:border-gray-400'\n            }`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <div className=\"flex flex-col items-center\">\n              <Upload size={48} className=\"text-gray-400 mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                Upload Property Photos\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                Drag and drop photos here, or click to select files\n              </p>\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center\"\n              >\n                <Camera size={20} className=\"mr-2\" />\n                Select Photos\n              </button>\n              <p className=\"text-sm text-gray-500 mt-2\">\n                Maximum {maxPhotos} photos • JPG, PNG, WebP\n              </p>\n            </div>\n          </div>\n\n          {/* Hidden file input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            multiple\n            accept=\"image/*\"\n            onChange={handleInputChange}\n            className=\"hidden\"\n          />\n\n          {/* Processing indicator */}\n          {isProcessing && (\n            <div className=\"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n              <div className=\"flex items-center\">\n                <RotateCw size={20} className=\"text-blue-600 animate-spin mr-2\" />\n                <span className=\"text-blue-800\">Processing photos...</span>\n              </div>\n            </div>\n          )}\n\n          {/* Panorama creation progress */}\n          {isCreatingPanorama && (\n            <div className=\"mt-4 p-4 bg-green-50 border border-green-200 rounded-lg\">\n              <div className=\"flex items-center mb-2\">\n                <Eye size={20} className=\"text-green-600 mr-2\" />\n                <span className=\"text-green-800 font-medium\">Creating 360° Panorama...</span>\n              </div>\n              <div className=\"w-full bg-green-200 rounded-full h-2\">\n                <div\n                  className=\"bg-green-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${panoramaProgress}%` }}\n                />\n              </div>\n              <p className=\"text-sm text-green-700 mt-1\">{panoramaProgress}% complete</p>\n            </div>\n          )}\n\n          {/* Uploaded Photos */}\n          {uploadedPhotos.length > 0 && (\n            <div className=\"mt-6\">\n              <h4 className=\"text-lg font-medium text-gray-900 mb-4\">\n                Uploaded Photos ({uploadedPhotos.length}/{maxPhotos})\n              </h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                {uploadedPhotos.map((photo) => (\n                  <div key={photo.id} className=\"relative group\">\n                    <div className=\"aspect-square bg-gray-100 rounded-lg overflow-hidden\">\n                      <img\n                        src={photo.url}\n                        alt={photo.name}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                    \n                    {/* Photo overlay */}\n                    <div className=\"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center\">\n                      <div className=\"flex space-x-2\">\n                        <button\n                          onClick={() => {\n                            const link = document.createElement('a');\n                            link.download = photo.name;\n                            link.href = photo.url;\n                            link.click();\n                          }}\n                          className=\"p-2 bg-white/20 rounded-full hover:bg-white/30 text-white\"\n                          title=\"Download\"\n                        >\n                          <Download size={16} />\n                        </button>\n                        <button\n                          onClick={() => removePhoto(photo.id)}\n                          className=\"p-2 bg-red-500/80 rounded-full hover:bg-red-500 text-white\"\n                          title=\"Remove\"\n                        >\n                          <Trash2 size={16} />\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Photo info */}\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm font-medium text-gray-900 truncate\">\n                        {photo.name}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        {formatFileSize(photo.size)}\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Instructions */}\n          <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n            <h5 className=\"font-medium text-gray-900 mb-2\">Tips for Best Results:</h5>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Take photos from multiple angles around the room</li>\n              <li>• Ensure good lighting and avoid shadows</li>\n              <li>• Keep the camera level and steady</li>\n              <li>• Overlap photos by 30-50% for better stitching</li>\n              <li>• Use landscape orientation for wider coverage</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50\">\n          <div className=\"text-sm text-gray-600\">\n            {uploadedPhotos.length} of {maxPhotos} photos uploaded\n          </div>\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-700 hover:text-gray-900\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={createPanorama}\n              disabled={uploadedPhotos.length === 0 || isCreatingPanorama}\n              className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg flex items-center\"\n            >\n              {isCreatingPanorama ? (\n                <>\n                  <RotateCw size={20} className=\"mr-2 animate-spin\" />\n                  Creating...\n                </>\n              ) : (\n                <>\n                  <Eye size={20} className=\"mr-2\" />\n                  Create 360° Panorama\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAhBA;;;;;AAgCO,MAAM,gBAA8C,CAAC,EAC1D,gBAAgB,EAChB,iBAAiB,EACjB,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,wBAAwB;IACxB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,CAAC,OAAO;QAEZ,gBAAgB;QAChB,MAAM,YAA6B,EAAE;QAErC,MAAM,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC,MAAM;YAC/B,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,aAAa,eAAe,MAAM,GAAG,UAAU,MAAM,GAAG,WAAW;gBAC1F,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,MAAM,MAAM,EAAE,MAAM,EAAE;oBACtB,UAAU,IAAI,CAAC;wBACb,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,OAAO;wBAC5B;wBACA,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;oBACjB;oBAEA,6BAA6B;oBAC7B,IAAI,UAAU,MAAM,KAAK,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,YAAY,eAAe,MAAM,GAAG;wBAClF,kBAAkB,CAAA,OAAQ;mCAAI;mCAAS;6BAAU;wBACjD,gBAAgB;oBAClB;gBACF;gBACA,OAAO,aAAa,CAAC;YACvB;QACF;IACF,GAAG;QAAC,eAAe,MAAM;QAAE;KAAU;IAErC,uBAAuB;IACvB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,cAAc;QACd,iBAAiB,EAAE,YAAY,CAAC,KAAK;IACvC,GAAG;QAAC;KAAiB;IAErB,2BAA2B;IAC3B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,iBAAiB,EAAE,MAAM,CAAC,KAAK;IACjC,GAAG;QAAC;KAAiB;IAErB,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC9D,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,eAAe,MAAM,KAAK,GAAG;QAEjC,sBAAsB;QACtB,oBAAoB;QAEpB,IAAI;YACF,gCAAgC;YAChC,MAAM,YAAY,eAAe,GAAG,CAAC,CAAA,QAAS,MAAM,GAAG;YACvD,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,6BAA0B,AAAD,EAAE;YAE9C,IAAI,CAAC,WAAW,KAAK,EAAE;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,oBAAoB;YAEpB,8BAA8B;YAC9B,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE,WAAW;gBACrD,aAAa;gBACb,cAAc;YAChB;YAEA,oBAAoB;YAEpB,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,oBAAoB;YAEpB,iDAAiD;YACjD,iBAAiB;YACjB,kBAAkB,OAAO,WAAW;YAEpC,WAAW;gBACT;YACF,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,gCAAgC,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,eAAe;QACjG,SAAU;YACR,sBAAsB;YACtB,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAgB;QAAkB;QAAmB;KAAQ;IAEjE,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,6LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,+BACA,yCACJ;4BACF,YAAY;4BACZ,aAAa;4BACb,QAAQ;sCAER,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC5B,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,SAAS,IAAM,aAAa,OAAO,EAAE;wCACrC,WAAU;;0DAEV,8OAAC,uMAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;kDAGvC,8OAAC;wCAAE,WAAU;;4CAA6B;4CAC/B;4CAAU;;;;;;;;;;;;;;;;;;sCAMzB,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAQ;4BACR,QAAO;4BACP,UAAU;4BACV,WAAU;;;;;;wBAIX,8BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;wBAMrC,oCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iMAAA,CAAA,MAAG;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDACzB,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;8CAE/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,iBAAiB,CAAC,CAAC;wCAAC;;;;;;;;;;;8CAG3C,8OAAC;oCAAE,WAAU;;wCAA+B;wCAAiB;;;;;;;;;;;;;wBAKhE,eAAe,MAAM,GAAG,mBACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAyC;wCACnC,eAAe,MAAM;wCAAC;wCAAE;wCAAU;;;;;;;8CAEtD,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;4CAAmB,WAAU;;8DAC5B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAK,MAAM,GAAG;wDACd,KAAK,MAAM,IAAI;wDACf,WAAU;;;;;;;;;;;8DAKd,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS;oEACP,MAAM,OAAO,SAAS,aAAa,CAAC;oEACpC,KAAK,QAAQ,GAAG,MAAM,IAAI;oEAC1B,KAAK,IAAI,GAAG,MAAM,GAAG;oEACrB,KAAK,KAAK;gEACZ;gEACA,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,2MAAA,CAAA,WAAQ;oEAAC,MAAM;;;;;;;;;;;0EAElB,8OAAC;gEACC,SAAS,IAAM,YAAY,MAAM,EAAE;gEACnC,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,2MAAA,CAAA,SAAM;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;8DAMpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,MAAM,IAAI;;;;;;sEAEb,8OAAC;4DAAE,WAAU;sEACV,eAAe,MAAM,IAAI;;;;;;;;;;;;;2CAxCtB,MAAM,EAAE;;;;;;;;;;;;;;;;sCAkD1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;8BAMV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCACZ,eAAe,MAAM;gCAAC;gCAAK;gCAAU;;;;;;;sCAExC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU,eAAe,MAAM,KAAK,KAAK;oCACzC,WAAU;8CAET,mCACC;;0DACE,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAsB;;qEAItD;;0DACE,8OAAC,iMAAA,CAAA,MAAG;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpD", "debugId": null}}, {"offset": {"line": 1653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/utils/localImageManager.ts"], "sourcesContent": ["/**\n * Local Image Manager for VR720 Panoramic Images\n * Handles local panoramic images from the s1234全景效果图 folder\n */\n\nexport interface PanoramicImage {\n  id: string;\n  name: string;\n  category: string;\n  description: string;\n  localPath: string;\n  publicPath: string;\n  thumbnail?: string;\n  metadata: {\n    width: number;\n    height: number;\n    fileSize: string;\n    format: string;\n    isEquirectangular: boolean;\n  };\n}\n\n/**\n * Sample panoramic images from the s1234全景效果图 collection\n * These would be copied to the public folder for web access\n */\nexport const PANORAMIC_IMAGES: PanoramicImage[] = [\n  // Living Rooms / 客厅\n  {\n    id: 'living-room-1',\n    name: 'Modern Living Room',\n    category: 'Living Room',\n    description: 'Spacious modern living room with contemporary design',\n    localPath: 'fanli/客厅/淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg',\n    publicPath: '/panoramas/living-room-modern-1.jpg',\n    thumbnail: '/thumbnails/living-room-modern-1-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.5MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n  {\n    id: 'living-room-2',\n    name: 'Luxury Living Room',\n    category: 'Living Room',\n    description: 'Elegant luxury living room with premium furnishing',\n    localPath: 'fanli/客厅/淘宝店铺：三老爹设计素材基地-淘宝网 (5).jpg',\n    publicPath: '/panoramas/living-room-luxury-2.jpg',\n    thumbnail: '/thumbnails/living-room-luxury-2-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '3.1MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n  {\n    id: 'living-room-3',\n    name: 'Cozy Living Room',\n    category: 'Living Room',\n    description: 'Warm and cozy living room with comfortable seating',\n    localPath: 'fanli/客厅/淘宝店铺：三老爹设计素材基地-淘宝网 (10).jpg',\n    publicPath: '/panoramas/living-room-cozy-3.jpg',\n    thumbnail: '/thumbnails/living-room-cozy-3-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.8MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n  \n  // Bedrooms / 卧室\n  {\n    id: 'bedroom-1',\n    name: 'Master Bedroom',\n    category: 'Bedroom',\n    description: 'Spacious master bedroom with elegant design',\n    localPath: 'fanli/卧室/淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg',\n    publicPath: '/panoramas/bedroom-master-1.jpg',\n    thumbnail: '/thumbnails/bedroom-master-1-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.8MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n  {\n    id: 'bedroom-2',\n    name: 'Guest Bedroom',\n    category: 'Bedroom',\n    description: 'Comfortable guest bedroom with natural lighting',\n    localPath: 'fanli/卧室/淘宝店铺：三老爹设计素材基地-淘宝网 (5).jpg',\n    publicPath: '/panoramas/bedroom-guest-2.jpg',\n    thumbnail: '/thumbnails/bedroom-guest-2-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.3MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n\n  // Kitchen / 厨房\n  {\n    id: 'kitchen-1',\n    name: 'Modern Kitchen',\n    category: 'Kitchen',\n    description: 'Contemporary kitchen with premium appliances',\n    localPath: 'fanli/厨房/淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg',\n    publicPath: '/panoramas/kitchen-modern-1.jpg',\n    thumbnail: '/thumbnails/kitchen-modern-1-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.7MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n  {\n    id: 'kitchen-2',\n    name: 'Open Kitchen',\n    category: 'Kitchen',\n    description: 'Open concept kitchen with dining area',\n    localPath: 'fanli/厨房/淘宝店铺：三老爹设计素材基地-淘宝网 (3).jpg',\n    publicPath: '/panoramas/kitchen-open-2.jpg',\n    thumbnail: '/thumbnails/kitchen-open-2-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.9MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n\n  // Bathroom / 浴室\n  {\n    id: 'bathroom-1',\n    name: 'Master Bathroom',\n    category: 'Bathroom',\n    description: 'Luxurious master bathroom with modern fixtures',\n    localPath: 'fanli/卫浴/淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg',\n    publicPath: '/panoramas/bathroom-master-1.jpg',\n    thumbnail: '/thumbnails/bathroom-master-1-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.4MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n  {\n    id: 'bathroom-2',\n    name: 'Guest Bathroom',\n    category: 'Bathroom',\n    description: 'Elegant guest bathroom with contemporary design',\n    localPath: 'fanli/卫浴/淘宝店铺：三老爹设计素材基地-淘宝网 (3).jpg',\n    publicPath: '/panoramas/bathroom-guest-2.jpg',\n    thumbnail: '/thumbnails/bathroom-guest-2-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.2MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n\n  // Dining Room / 餐厅\n  {\n    id: 'dining-1',\n    name: 'Formal Dining Room',\n    category: 'Dining Room',\n    description: 'Elegant formal dining room with chandelier',\n    localPath: 'F:/BaiduNetdiskDownload/s1234全景效果图/餐厅/正式餐厅1.jpg',\n    publicPath: '/panoramas/dining-formal-1.jpg',\n    thumbnail: '/thumbnails/dining-formal-1-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.6MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n\n  // Office / 书房\n  {\n    id: 'office-1',\n    name: 'Home Office',\n    category: 'Office',\n    description: 'Modern home office with built-in shelving',\n    localPath: 'F:/BaiduNetdiskDownload/s1234全景效果图/书房/家庭办公室1.jpg',\n    publicPath: '/panoramas/office-home-1.jpg',\n    thumbnail: '/thumbnails/office-home-1-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '2.2MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n\n  // Exterior / 外景\n  {\n    id: 'exterior-1',\n    name: 'Front Entrance',\n    category: 'Exterior',\n    description: 'Grand front entrance with landscaping',\n    localPath: 'F:/BaiduNetdiskDownload/s1234全景效果图/外景/前门入口1.jpg',\n    publicPath: '/panoramas/exterior-entrance-1.jpg',\n    thumbnail: '/thumbnails/exterior-entrance-1-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '3.2MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  },\n  {\n    id: 'exterior-2',\n    name: 'Backyard Garden',\n    category: 'Exterior',\n    description: 'Beautiful backyard with garden and patio',\n    localPath: 'F:/BaiduNetdiskDownload/s1234全景效果图/外景/后院花园2.jpg',\n    publicPath: '/panoramas/exterior-garden-2.jpg',\n    thumbnail: '/thumbnails/exterior-garden-2-thumb.jpg',\n    metadata: {\n      width: 4096,\n      height: 2048,\n      fileSize: '3.5MB',\n      format: 'JPEG',\n      isEquirectangular: true\n    }\n  }\n];\n\n/**\n * Get panoramic images by category\n */\nexport function getPanoramicImagesByCategory(category: string): PanoramicImage[] {\n  return PANORAMIC_IMAGES.filter(img => img.category === category);\n}\n\n/**\n * Get all available categories\n */\nexport function getPanoramicCategories(): string[] {\n  const categories = new Set(PANORAMIC_IMAGES.map(img => img.category));\n  return Array.from(categories);\n}\n\n/**\n * Get random panoramic images for demo\n */\nexport function getRandomPanoramicImages(count: number = 4): PanoramicImage[] {\n  const shuffled = [...PANORAMIC_IMAGES].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n}\n\n/**\n * Instructions for setting up local panoramic images\n */\nexport const SETUP_INSTRUCTIONS = {\n  title: \"Setting Up Local Panoramic Images\",\n  steps: [\n    {\n      step: 1,\n      title: \"Create Public Directory\",\n      description: \"Create 'panoramas' and 'thumbnails' folders in the public directory\",\n      command: \"mkdir public/panoramas public/thumbnails\"\n    },\n    {\n      step: 2,\n      title: \"Copy Panoramic Images\",\n      description: \"Copy selected panoramic images from your local folder to public/panoramas/\",\n      example: \"Copy F:/BaiduNetdiskDownload/s1234全景效果图/客厅/现代客厅1.jpg to public/panoramas/living-room-modern-1.jpg\"\n    },\n    {\n      step: 3,\n      title: \"Generate Thumbnails\",\n      description: \"Create thumbnail versions (300x150) of each panoramic image\",\n      note: \"You can use image editing software or online tools to resize images\"\n    },\n    {\n      step: 4,\n      title: \"Update Image Paths\",\n      description: \"Ensure all publicPath values in PANORAMIC_IMAGES match your copied files\"\n    }\n  ],\n  notes: [\n    \"Panoramic images should be in equirectangular format (2:1 aspect ratio)\",\n    \"Recommended resolution: 4096x2048 or higher for best VR experience\",\n    \"File formats: JPEG, PNG, or WebP\",\n    \"Keep file sizes reasonable for web loading (under 5MB per image)\"\n  ]\n};\n\n/**\n * Validate if an image is suitable for panoramic viewing\n */\nexport function validatePanoramicImage(width: number, height: number): {\n  isValid: boolean;\n  aspectRatio: number;\n  recommendation: string;\n} {\n  const aspectRatio = width / height;\n  const isEquirectangular = Math.abs(aspectRatio - 2) < 0.1; // Allow 10% tolerance\n  \n  return {\n    isValid: isEquirectangular,\n    aspectRatio,\n    recommendation: isEquirectangular \n      ? \"Perfect for 360° VR viewing\"\n      : `Aspect ratio is ${aspectRatio.toFixed(2)}:1. For best VR experience, use 2:1 ratio images.`\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAuBM,MAAM,mBAAqC;IAChD,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IAEA,eAAe;IACf;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IAEA,cAAc;IACd;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,aAAa;QACb,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;YACR,OAAO;YACP,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;QACrB;IACF;CACD;AAKM,SAAS,6BAA6B,QAAgB;IAC3D,OAAO,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;AACzD;AAKO,SAAS;IACd,MAAM,aAAa,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;IACnE,OAAO,MAAM,IAAI,CAAC;AACpB;AAKO,SAAS,yBAAyB,QAAgB,CAAC;IACxD,MAAM,WAAW;WAAI;KAAiB,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACnE,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAKO,MAAM,qBAAqB;IAChC,OAAO;IACP,OAAO;QACL;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IACD,OAAO;QACL;QACA;QACA;QACA;KACD;AACH;AAKO,SAAS,uBAAuB,KAAa,EAAE,MAAc;IAKlE,MAAM,cAAc,QAAQ;IAC5B,MAAM,oBAAoB,KAAK,GAAG,CAAC,cAAc,KAAK,KAAK,sBAAsB;IAEjF,OAAO;QACL,SAAS;QACT;QACA,gBAAgB,oBACZ,gCACA,CAAC,gBAAgB,EAAE,YAAY,OAAO,CAAC,GAAG,iDAAiD,CAAC;IAClG;AACF", "debugId": null}}, {"offset": {"line": 1943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/components/Gallery/PanoramicGallery.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Eye, \n  Grid, \n  X, \n  Play,\n  Image as ImageIcon,\n  Home,\n  ChevronRight\n} from 'lucide-react';\nimport { PANORAMIC_IMAGES, getPanoramicCategories, getPanoramicImagesByCategory } from '@/utils/localImageManager';\nimport { MobileVRViewer } from '@/components/VR/MobileVRViewer';\n\ninterface PanoramicGalleryProps {\n  onClose: () => void;\n  onImageSelect?: (panoramaUrl: string, title: string) => void;\n}\n\nexport const PanoramicGallery: React.FC<PanoramicGalleryProps> = ({\n  onClose,\n  onImageSelect\n}) => {\n  const [selectedCategory, setSelectedCategory] = useState<string>('All');\n  const [showVRViewer, setShowVRViewer] = useState(false);\n  const [selectedPanorama, setSelectedPanorama] = useState<string>('');\n  const [selectedTitle, setSelectedTitle] = useState<string>('');\n\n  const categories = ['All', ...getPanoramicCategories()];\n  const filteredImages = selectedCategory === 'All' \n    ? PANORAMIC_IMAGES \n    : getPanoramicImagesByCategory(selectedCategory);\n\n  // Handle VR viewing\n  const handleViewVR = (panoramaUrl: string, title: string) => {\n    setSelectedPanorama(panoramaUrl);\n    setSelectedTitle(title);\n    setShowVRViewer(true);\n  };\n\n  // Handle image selection\n  const handleSelectImage = (panoramaUrl: string, title: string) => {\n    if (onImageSelect) {\n      onImageSelect(panoramaUrl, title);\n      onClose();\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Panoramic Gallery</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-500 hover:text-gray-700 p-1\"\n          >\n            <X size={24} />\n          </button>\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"p-4 border-b border-gray-200\">\n          <div className=\"flex flex-wrap gap-2\">\n            {categories.map((category) => (\n              <button\n                key={category}\n                onClick={() => setSelectedCategory(category)}\n                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                  selectedCategory === category\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                }`}\n              >\n                {category}\n              </button>\n            ))}\n          </div>\n          <p className=\"text-sm text-gray-600 mt-2\">\n            {filteredImages.length} panoramic images available\n          </p>\n        </div>\n\n        {/* Image Grid */}\n        <div className=\"p-4 max-h-[60vh] overflow-y-auto\">\n          {filteredImages.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <ImageIcon size={48} className=\"mx-auto text-gray-400 mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Images Found</h3>\n              <p className=\"text-gray-600 mb-4\">\n                No panoramic images found in the selected category.\n              </p>\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto\">\n                <h4 className=\"font-medium text-yellow-800 mb-2\">Setup Required</h4>\n                <p className=\"text-sm text-yellow-700\">\n                  Please copy your panoramic images to the public/panoramas folder. \n                  See setup-local-images.md for detailed instructions.\n                </p>\n              </div>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {filteredImages.map((image) => (\n                <div key={image.id} className=\"bg-gray-50 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow\">\n                  {/* Image Preview */}\n                  <div className=\"aspect-video bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center relative\">\n                    <img\n                      src={image.thumbnail || image.publicPath}\n                      alt={image.name}\n                      className=\"w-full h-full object-cover\"\n                      onError={(e) => {\n                        // Fallback to a placeholder if image fails to load\n                        const target = e.target as HTMLImageElement;\n                        target.style.display = 'none';\n                      }}\n                    />\n                    <div className=\"absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 hover:opacity-100\">\n                      <div className=\"flex space-x-2\">\n                        <button\n                          onClick={() => handleViewVR(image.publicPath, image.name)}\n                          className=\"bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full\"\n                          title=\"View in VR\"\n                        >\n                          <Eye size={20} />\n                        </button>\n                        {onImageSelect && (\n                          <button\n                            onClick={() => handleSelectImage(image.publicPath, image.name)}\n                            className=\"bg-green-600 hover:bg-green-700 text-white p-2 rounded-full\"\n                            title=\"Select Image\"\n                          >\n                            <ChevronRight size={20} />\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                    \n                    {/* Category Badge */}\n                    <div className=\"absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs\">\n                      {image.category}\n                    </div>\n                  </div>\n\n                  {/* Image Info */}\n                  <div className=\"p-4\">\n                    <h3 className=\"font-medium text-gray-900 mb-1\">{image.name}</h3>\n                    <p className=\"text-sm text-gray-600 mb-2\">{image.description}</p>\n                    \n                    {/* Metadata */}\n                    <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                      <span>{image.metadata.width}x{image.metadata.height}</span>\n                      <span>{image.metadata.fileSize}</span>\n                    </div>\n                    \n                    {/* Action Buttons */}\n                    <div className=\"flex space-x-2 mt-3\">\n                      <button\n                        onClick={() => handleViewVR(image.publicPath, image.name)}\n                        className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm flex items-center justify-center\"\n                      >\n                        <Play size={14} className=\"mr-1\" />\n                        VR View\n                      </button>\n                      {onImageSelect && (\n                        <button\n                          onClick={() => handleSelectImage(image.publicPath, image.name)}\n                          className=\"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-3 rounded text-sm flex items-center justify-center\"\n                        >\n                          <Home size={14} className=\"mr-1\" />\n                          Use\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50\">\n          <div className=\"text-sm text-gray-600\">\n            High-quality 360° panoramic images for VR tours\n          </div>\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-gray-700 hover:text-gray-900\"\n            >\n              Close\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* VR Viewer */}\n      {showVRViewer && selectedPanorama && (\n        <MobileVRViewer\n          panoramaUrl={selectedPanorama}\n          title={selectedTitle}\n          description=\"Mobile-optimized 720° panoramic view\"\n          onClose={() => setShowVRViewer(false)}\n        />\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAbA;;;;;;AAoBO,MAAM,mBAAoD,CAAC,EAChE,OAAO,EACP,aAAa,EACd;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,aAAa;QAAC;WAAU,CAAA,GAAA,iIAAA,CAAA,yBAAsB,AAAD;KAAI;IACvD,MAAM,iBAAiB,qBAAqB,QACxC,iIAAA,CAAA,mBAAgB,GAChB,CAAA,GAAA,iIAAA,CAAA,+BAA4B,AAAD,EAAE;IAEjC,oBAAoB;IACpB,MAAM,eAAe,CAAC,aAAqB;QACzC,oBAAoB;QACpB,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,yBAAyB;IACzB,MAAM,oBAAoB,CAAC,aAAqB;QAC9C,IAAI,eAAe;YACjB,cAAc,aAAa;YAC3B;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,6LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,2DAA2D,EACrE,qBAAqB,WACjB,2BACA,+CACJ;kDAED;uCARI;;;;;;;;;;0CAYX,8OAAC;gCAAE,WAAU;;oCACV,eAAe,MAAM;oCAAC;;;;;;;;;;;;;kCAK3B,8OAAC;wBAAI,WAAU;kCACZ,eAAe,MAAM,KAAK,kBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qMAAA,CAAA,QAAS;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;;;;;;iDAO3C,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;oCAAmB,WAAU;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAK,MAAM,SAAS,IAAI,MAAM,UAAU;oDACxC,KAAK,MAAM,IAAI;oDACf,WAAU;oDACV,SAAS,CAAC;wDACR,mDAAmD;wDACnD,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oDACzB;;;;;;8DAEF,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,aAAa,MAAM,UAAU,EAAE,MAAM,IAAI;gEACxD,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,iMAAA,CAAA,MAAG;oEAAC,MAAM;;;;;;;;;;;4DAEZ,+BACC,8OAAC;gEACC,SAAS,IAAM,kBAAkB,MAAM,UAAU,EAAE,MAAM,IAAI;gEAC7D,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,uNAAA,CAAA,eAAY;oEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;8DAO5B,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ;;;;;;;;;;;;sDAKnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAkC,MAAM,IAAI;;;;;;8DAC1D,8OAAC;oDAAE,WAAU;8DAA8B,MAAM,WAAW;;;;;;8DAG5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAM,MAAM,QAAQ,CAAC,KAAK;gEAAC;gEAAE,MAAM,QAAQ,CAAC,MAAM;;;;;;;sEACnD,8OAAC;sEAAM,MAAM,QAAQ,CAAC,QAAQ;;;;;;;;;;;;8DAIhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,aAAa,MAAM,UAAU,EAAE,MAAM,IAAI;4DACxD,WAAU;;8EAEV,8OAAC,mMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAS;;;;;;;wDAGpC,+BACC,8OAAC;4DACC,SAAS,IAAM,kBAAkB,MAAM,UAAU,EAAE,MAAM,IAAI;4DAC7D,WAAU;;8EAEV,8OAAC,mMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAS;;;;;;;;;;;;;;;;;;;;mCAjEnC,MAAM,EAAE;;;;;;;;;;;;;;;kCA8E1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YAQN,gBAAgB,kCACf,8OAAC,0IAAA,CAAA,iBAAc;gBACb,aAAa;gBACb,OAAO;gBACP,aAAY;gBACZ,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 2379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/components/VR/HouseVRTour.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport {\n  X,\n  Home,\n  ArrowLeft,\n  ArrowRight,\n  MapPin,\n  Eye,\n  Navigation,\n  Maximize2,\n  Minimize2,\n  RotateCcw,\n  Info,\n  Map,\n  Play,\n  Pause\n} from 'lucide-react';\n\n// 房间连接点接口\nexport interface HotSpot {\n  id: string;\n  x: number; // 在全景图中的x坐标 (0-1)\n  y: number; // 在全景图中的y坐标 (0-1)\n  targetRoomId: string; // 目标房间ID\n  title: string; // 连接点标题\n  description?: string; // 连接点描述\n  icon?: string; // 图标类型\n}\n\n// 房间接口\nexport interface Room {\n  id: string;\n  name: string;\n  type: 'living_room' | 'bedroom' | 'kitchen' | 'bathroom' | 'dining_room' | 'office' | 'other';\n  panoramaUrl: string;\n  thumbnail?: string;\n  hotSpots: HotSpot[]; // 该房间的连接点\n  description?: string;\n  area?: number; // 面积（平方米）\n}\n\n// 房屋接口\nexport interface House {\n  id: string;\n  name: string;\n  address: string;\n  description?: string;\n  rooms: Room[];\n  startRoomId?: string; // 默认起始房间\n}\n\ninterface HouseVRTourProps {\n  house: House;\n  onClose: () => void;\n  startRoomId?: string;\n}\n\nconst HouseVRTour: React.FC<HouseVRTourProps> = ({\n  house,\n  onClose,\n  startRoomId\n}) => {\n  const [currentRoomId, setCurrentRoomId] = useState<string>(\n    startRoomId || house.startRoomId || house.rooms[0]?.id || ''\n  );\n  const [showRoomList, setShowRoomList] = useState(false);\n  const [showFloorPlan, setShowFloorPlan] = useState(false);\n  const [showInfo, setShowInfo] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [is720Mode, setIs720Mode] = useState(true);\n\n  const containerRef = useRef<HTMLDivElement>(null);\n  const currentRoom = house.rooms.find(room => room.id === currentRoomId);\n\n  // 房间类型图标映射\n  const getRoomIcon = (type: Room['type']) => {\n    const icons = {\n      living_room: '🛋️',\n      bedroom: '🛏️',\n      kitchen: '🍳',\n      bathroom: '🚿',\n      dining_room: '🍽️',\n      office: '💼',\n      other: '🏠'\n    };\n    return icons[type] || '🏠';\n  };\n\n  // 房间类型中文名称\n  const getRoomTypeName = (type: Room['type']) => {\n    const names = {\n      living_room: '客厅',\n      bedroom: '卧室',\n      kitchen: '厨房',\n      bathroom: '浴室',\n      dining_room: '餐厅',\n      office: '书房',\n      other: '其他'\n    };\n    return names[type] || '房间';\n  };\n\n  // 处理房间切换\n  const navigateToRoom = (roomId: string) => {\n    if (roomId === currentRoomId) return;\n\n    setIsLoading(true);\n    setCurrentRoomId(roomId);\n\n    // 重新创建VR场景\n    setTimeout(() => {\n      setIsLoading(false);\n    }, 800);\n  };\n\n  // 检测设备性能\n  const detectDevicePerformance = () => {\n    const userAgent = navigator.userAgent;\n    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n    const isLowEnd = /Android.*4\\.|iPhone.*OS [5-9]_|iPad.*OS [5-9]_/i.test(userAgent);\n\n    // 检测GPU性息\n    const canvas = document.createElement('canvas');\n    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');\n    const debugInfo = gl?.getExtension('WEBGL_debug_renderer_info');\n    const renderer = debugInfo ? gl?.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : '';\n    const isLowEndGPU = /Adreno [0-4]|Mali-[0-4]|PowerVR SGX/i.test(renderer || '');\n\n    return {\n      isMobile,\n      isLowEnd: isLowEnd || isLowEndGPU,\n      maxTextureSize: gl?.getParameter(gl.MAX_TEXTURE_SIZE) || 2048\n    };\n  };\n\n  // 创建VR720场景\n  useEffect(() => {\n    if (typeof window !== 'undefined' && currentRoom && containerRef.current) {\n      setIsLoading(true);\n\n      const deviceInfo = detectDevicePerformance();\n\n      // 根据设备性能调整设置\n      const performanceSettings = {\n        // 移动端优化设置\n        antialias: !deviceInfo.isMobile,\n        logarithmicDepthBuffer: !deviceInfo.isLowEnd,\n        precision: deviceInfo.isLowEnd ? 'mediump' : 'highp',\n        maxCanvasWidth: deviceInfo.isLowEnd ? 1024 : 1920,\n        maxCanvasHeight: deviceInfo.isLowEnd ? 1024 : 1920,\n        // 动画设置\n        animationDuration: deviceInfo.isLowEnd ?\n          (is720Mode ? 200000 : 120000) : // 低端设备更慢的动画\n          (is720Mode ? 150000 : 80000),   // 正常设备\n        // 帧率设置\n        targetFrameRate: deviceInfo.isLowEnd ? 30 : 60\n      };\n\n      // 生成连接点的A-Frame元素\n      const generateHotSpots = () => {\n        return currentRoom.hotSpots.map((hotSpot, index) => {\n          const targetRoom = house.rooms.find(room => room.id === hotSpot.targetRoomId);\n          if (!targetRoom) return '';\n\n          // 将2D坐标转换为3D球面坐标\n          const phi = (hotSpot.x - 0.5) * Math.PI * 2; // 水平角度\n          const theta = (0.5 - hotSpot.y) * Math.PI; // 垂直角度\n          const radius = 8; // 球面半径\n\n          const x = radius * Math.sin(theta) * Math.cos(phi);\n          const y = radius * Math.cos(theta);\n          const z = radius * Math.sin(theta) * Math.sin(phi);\n\n          return `\n            <!-- 连接点 ${index + 1}: ${hotSpot.title} -->\n            <a-entity\n              id=\"hotspot-${hotSpot.id}\"\n              position=\"${x.toFixed(2)} ${y.toFixed(2)} ${z.toFixed(2)}\"\n              class=\"hotspot\"\n              data-target-room=\"${hotSpot.targetRoomId}\"\n              data-title=\"${hotSpot.title}\"\n            >\n              <!-- 连接点球体 -->\n              <a-sphere\n                radius=\"0.3\"\n                color=\"#4ecdc4\"\n                opacity=\"0.9\"\n                animation=\"${deviceInfo.isLowEnd ?\n                  'property: scale; to: 1.1 1.1 1.1; loop: true; dir: alternate; dur: 3000' :\n                  'property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 2000'}\"\n                material=\"shader: flat\"\n              ></a-sphere>\n\n              ${!deviceInfo.isLowEnd ? `\n              <!-- 连接点光环 (仅高性能设备) -->\n              <a-ring\n                radius-inner=\"0.4\"\n                radius-outer=\"0.6\"\n                color=\"#ff6b6b\"\n                opacity=\"0.5\"\n                animation=\"property: rotation; to: 0 0 360; loop: true; dur: 6000; easing: linear\"\n                position=\"0 0 0.1\"\n              ></a-ring>\n              ` : ''}\n\n              <!-- 连接点图标 -->\n              <a-text\n                value=\"🚪\"\n                position=\"0 0 0.35\"\n                align=\"center\"\n                color=\"#ffffff\"\n                font=\"size: 8\"\n                look-at=\"[camera]\"\n              ></a-text>\n\n              <!-- 连接点标签 -->\n              <a-text\n                value=\"${hotSpot.title}\"\n                position=\"0 -0.8 0\"\n                align=\"center\"\n                color=\"#ffffff\"\n                font=\"size: 4; weight: bold\"\n                look-at=\"[camera]\"\n                material=\"shader: msdf\"\n                geometry=\"primitive: plane; width: auto; height: auto\"\n                background=\"color: rgba(0,0,0,0.8); opacity: 0.8\"\n              ></a-text>\n            </a-entity>\n          `;\n        }).join('');\n      };\n\n      // 创建VR720场景HTML\n      const sceneHTML = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=no\">\n          <script src=\"https://aframe.io/releases/1.4.0/aframe.min.js\"></script>\n          <style>\n            body {\n              margin: 0;\n              overflow: hidden;\n              background: #000;\n              touch-action: none;\n              -webkit-user-select: none;\n              user-select: none;\n            }\n            #vr-scene {\n              width: 100vw;\n              height: 100vh;\n              position: fixed;\n              top: 0;\n              left: 0;\n            }\n            .loading-overlay {\n              position: fixed;\n              top: 0;\n              left: 0;\n              width: 100vw;\n              height: 100vh;\n              background: #000;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n              z-index: 2000;\n              flex-direction: column;\n            }\n            .spinner {\n              width: 40px;\n              height: 40px;\n              border: 3px solid rgba(255,255,255,0.3);\n              border-top: 3px solid #4ecdc4;\n              border-radius: 50%;\n              animation: spin 1s linear infinite;\n              margin-bottom: 20px;\n            }\n            @keyframes spin {\n              0% { transform: rotate(0deg); }\n              100% { transform: rotate(360deg); }\n            }\n          </style>\n        </head>\n        <body>\n          <a-scene\n            id=\"vr-scene\"\n            embedded\n            style=\"height: 100vh; width: 100vw;\"\n            vr-mode-ui=\"enabled: true\"\n            background=\"color: #000\"\n            device-orientation-permission-ui=\"enabled: false\"\n            cursor=\"rayOrigin: mouse\"\n            renderer=\"antialias: ${performanceSettings.antialias};\n                     logarithmicDepthBuffer: ${performanceSettings.logarithmicDepthBuffer};\n                     precision: ${performanceSettings.precision};\n                     maxCanvasWidth: ${performanceSettings.maxCanvasWidth};\n                     maxCanvasHeight: ${performanceSettings.maxCanvasHeight};\n                     colorManagement: true;\n                     sortObjects: true;\n                     physicallyCorrectLights: false\"\n            stats=\"false\"\n          >\n            <a-assets>\n              <img id=\"panorama\" src=\"${currentRoom.panoramaUrl}\" crossorigin=\"anonymous\">\n            </a-assets>\n\n            <!-- VR720天空球 -->\n            <a-sky\n              id=\"room-sky\"\n              src=\"#panorama\"\n              rotation=\"0 -90 0\"\n              animation=\"property: rotation; to: 0 ${is720Mode ? '630' : '270'} 0; loop: true; dur: ${performanceSettings.animationDuration}; easing: linear\"\n              material=\"side: back; npot: true\"\n            ></a-sky>\n\n            <!-- VR相机 -->\n            <a-camera\n              id=\"main-camera\"\n              look-controls=\"enabled: true;\n                           reverseMouseDrag: false;\n                           touchEnabled: true;\n                           magicWindowTrackingEnabled: true;\n                           pointerLockEnabled: false;\n                           mouseSensitivity: ${deviceInfo.isLowEnd ? 0.3 : 0.5};\n                           touchSensitivity: ${deviceInfo.isLowEnd ? 8 : 15}\"\n              wasd-controls=\"enabled: false\"\n              position=\"0 1.6 0\"\n              fov=\"${deviceInfo.isMobile ? '75' : '80'}\"\n            >\n              ${!deviceInfo.isLowEnd ? `\n              <a-cursor\n                color=\"#4ecdc4\"\n                opacity=\"0.6\"\n                geometry=\"primitive: ring; radiusInner: 0.015; radiusOuter: 0.025\"\n                material=\"color: #4ecdc4; shader: flat\"\n                animation=\"property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 2000\"\n              ></a-cursor>\n              ` : ''}\n            </a-camera>\n\n            <!-- 环境光 -->\n            <a-light type=\"ambient\" color=\"#606060\"></a-light>\n            ${!deviceInfo.isLowEnd ? '<a-light type=\"directional\" position=\"1 1 1\" color=\"#ffffff\" intensity=\"0.2\"></a-light>' : ''}\n\n            <!-- 房间标题 -->\n            <a-text\n              value=\"${currentRoom.name} - ${house.name}\"\n              position=\"0 3.5 -4\"\n              align=\"center\"\n              color=\"#4ecdc4\"\n              opacity=\"0.9\"\n              font=\"size: ${deviceInfo.isMobile ? '16' : '20'}; weight: bold\"\n              ${!deviceInfo.isLowEnd ? 'animation=\"property: opacity; to: 0.7; loop: true; dir: alternate; dur: 5000\"' : ''}\n            ></a-text>\n\n            ${currentRoom.description && !deviceInfo.isLowEnd ? `\n            <!-- 房间信息 -->\n            <a-text\n              value=\"${currentRoom.description}\"\n              position=\"0 3 -4\"\n              align=\"center\"\n              color=\"#ffffff\"\n              opacity=\"0.7\"\n              font=\"size: ${deviceInfo.isMobile ? '10' : '12'}\"\n            ></a-text>\n            ` : ''}\n\n            ${!deviceInfo.isLowEnd ? `\n            <!-- VR720指示器 -->\n            <a-ring\n              position=\"0 -2 -3\"\n              color=\"#ff6b6b\"\n              radius-inner=\"0.3\"\n              radius-outer=\"0.4\"\n              opacity=\"0.6\"\n              animation=\"property: rotation; to: 0 0 ${is720Mode ? '720' : '360'}; loop: true; dur: ${is720Mode ? '15000' : '10000'}; easing: linear\"\n            ></a-ring>\n\n            <a-text\n              value=\"${is720Mode ? 'VR720°' : 'VR360°'} House Tour\"\n              position=\"0 -2.5 -3\"\n              align=\"center\"\n              color=\"#ff6b6b\"\n              font=\"size: 8; weight: bold\"\n              opacity=\"0.8\"\n            ></a-text>\n            ` : ''}\n\n            <!-- 连接点 -->\n            ${generateHotSpots()}\n          </a-scene>\n\n          <div class=\"loading-overlay\" id=\"loading\" style=\"display: flex;\">\n            <div class=\"spinner\"></div>\n            <div style=\"color: white; text-align: center;\">\n              <div style=\"font-size: 16px; font-weight: bold; margin-bottom: 8px;\">Loading ${currentRoom.name}</div>\n              <div style=\"font-size: 12px; opacity: 0.8;\">${house.name} - VR720° House Tour</div>\n            </div>\n          </div>\n\n          <script>\n            // 设备性能检测\n            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n            const isLowEnd = ${deviceInfo.isLowEnd};\n\n            // 性能监控\n            let frameCount = 0;\n            let lastTime = performance.now();\n            let fps = 60;\n\n            function monitorPerformance() {\n              frameCount++;\n              const currentTime = performance.now();\n\n              if (currentTime - lastTime >= 1000) {\n                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));\n                frameCount = 0;\n                lastTime = currentTime;\n\n                // 如果帧率过低，自动降低质量\n                if (fps < 20 && !isLowEnd) {\n                  console.log('Low FPS detected, reducing quality...');\n                  const scene = document.querySelector('a-scene');\n                  if (scene) {\n                    scene.setAttribute('renderer', 'antialias: false; precision: mediump; maxCanvasWidth: 1024; maxCanvasHeight: 1024');\n                  }\n                }\n              }\n\n              if (!isLowEnd) {\n                requestAnimationFrame(monitorPerformance);\n              }\n            }\n\n            // 连接点点击处理\n            document.addEventListener('DOMContentLoaded', function() {\n              // 启动性能监控\n              if (!isLowEnd) {\n                monitorPerformance();\n              }\n\n              const hotspots = document.querySelectorAll('.hotspot');\n\n              hotspots.forEach(hotspot => {\n                hotspot.addEventListener('click', function() {\n                  const targetRoomId = this.getAttribute('data-target-room');\n                  const title = this.getAttribute('data-title');\n\n                  // 发送房间切换消息到父窗口\n                  window.parent.postMessage({\n                    type: 'navigate',\n                    roomId: targetRoomId,\n                    title: title\n                  }, '*');\n                });\n\n                // 鼠标悬停效果（仅高性能设备）\n                if (!isLowEnd) {\n                  hotspot.addEventListener('mouseenter', function() {\n                    const sphere = this.querySelector('a-sphere');\n                    if (sphere) {\n                      sphere.setAttribute('animation', 'property: scale; to: 1.5 1.5 1.5; dur: 300');\n                      sphere.setAttribute('color', '#ff6b6b');\n                    }\n                  });\n\n                  hotspot.addEventListener('mouseleave', function() {\n                    const sphere = this.querySelector('a-sphere');\n                    if (sphere) {\n                      sphere.setAttribute('animation', 'property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 2000');\n                      sphere.setAttribute('color', '#4ecdc4');\n                    }\n                  });\n                }\n              });\n            });\n\n            // 隐藏加载界面\n            const hideLoading = () => {\n              const loadingEl = document.querySelector('#loading');\n              if (loadingEl) {\n                loadingEl.style.display = 'none';\n              }\n            };\n\n            // 等待场景完全加载\n            const scene = document.querySelector('a-scene');\n            if (scene) {\n              scene.addEventListener('loaded', () => {\n                setTimeout(hideLoading, isLowEnd ? 1000 : 500);\n              });\n            } else {\n              setTimeout(hideLoading, isLowEnd ? 3000 : 2000);\n            }\n\n            // 移动端特定优化\n            if (isMobile) {\n              // 禁用右键菜单\n              document.addEventListener('contextmenu', e => e.preventDefault());\n\n              // 防止页面滚动\n              document.addEventListener('touchmove', e => e.preventDefault(), { passive: false });\n\n              // 优化触摸响应\n              document.addEventListener('touchstart', e => e.preventDefault(), { passive: false });\n            }\n\n            // 监听父窗口消息\n            window.addEventListener('message', function(event) {\n              if (event.data === 'toggle720') {\n                const sky = document.querySelector('#room-sky');\n                const ring = document.querySelector('a-ring');\n                const text = document.querySelector('a-text[value*=\"VR\"]');\n\n                const currentAnimation = sky.getAttribute('animation');\n                const isCurrently720 = currentAnimation.to.includes('630');\n\n                // 根据设备性能调整动画时长\n                const duration720 = isLowEnd ? 200000 : 150000;\n                const duration360 = isLowEnd ? 120000 : 80000;\n                const ringDuration720 = isLowEnd ? 15000 : 12000;\n                const ringDuration360 = isLowEnd ? 10000 : 8000;\n\n                if (isCurrently720) {\n                  // 切换到360°\n                  sky.setAttribute('animation', \\`property: rotation; to: 0 270 0; loop: true; dur: \\${duration360}; easing: linear\\`);\n                  if (ring) {\n                    ring.setAttribute('animation', \\`property: rotation; to: 0 0 360; loop: true; dur: \\${ringDuration360}; easing: linear\\`);\n                  }\n                  if (text) {\n                    text.setAttribute('value', 'VR360° House Tour');\n                  }\n                } else {\n                  // 切换到720°\n                  sky.setAttribute('animation', \\`property: rotation; to: 0 630 0; loop: true; dur: \\${duration720}; easing: linear\\`);\n                  if (ring) {\n                    ring.setAttribute('animation', \\`property: rotation; to: 0 0 720; loop: true; dur: \\${ringDuration720}; easing: linear\\`);\n                  }\n                  if (text) {\n                    text.setAttribute('value', 'VR720° House Tour');\n                  }\n                }\n              }\n            });\n          </script>\n        </body>\n        </html>\n      `;\n\n      // 创建iframe\n      const iframe = document.createElement('iframe');\n      iframe.style.width = '100%';\n      iframe.style.height = '100%';\n      iframe.style.border = 'none';\n      iframe.style.position = 'absolute';\n      iframe.style.top = '0';\n      iframe.style.left = '0';\n      iframe.srcdoc = sceneHTML;\n\n      // 监听iframe消息\n      const handleMessage = (event: MessageEvent) => {\n        if (event.data?.type === 'navigate') {\n          navigateToRoom(event.data.roomId);\n        }\n      };\n\n      window.addEventListener('message', handleMessage);\n\n      // 监听iframe加载完成\n      iframe.onload = () => {\n        setTimeout(() => {\n          setIsLoading(false);\n        }, 1000);\n      };\n\n      containerRef.current.appendChild(iframe);\n\n      return () => {\n        window.removeEventListener('message', handleMessage);\n        if (containerRef.current && iframe) {\n          containerRef.current.removeChild(iframe);\n        }\n      };\n    }\n  }, [currentRoom, house, is720Mode]);\n\n  // 切换720/360模式\n  const toggle720Mode = () => {\n    setIs720Mode(!is720Mode);\n\n    // 发送消息到iframe\n    const iframe = containerRef.current?.querySelector('iframe');\n    if (iframe?.contentWindow) {\n      iframe.contentWindow.postMessage('toggle720', '*');\n    }\n  };\n\n  // 创建平面图组件\n  const FloorPlan = () => (\n    <div className=\"absolute top-20 right-4 bg-black/90 text-white p-4 rounded-lg max-w-sm z-20\">\n      <h3 className=\"text-lg font-medium mb-3 text-center\">房间平面图</h3>\n      <div className=\"grid grid-cols-2 gap-2\">\n        {house.rooms.map((room, index) => (\n          <button\n            key={room.id}\n            onClick={() => navigateToRoom(room.id)}\n            className={`p-3 rounded-lg text-sm transition-all ${\n              room.id === currentRoomId\n                ? 'bg-blue-600 text-white scale-105'\n                : 'bg-gray-700 hover:bg-gray-600 text-gray-300'\n            }`}\n          >\n            <div className=\"text-lg mb-1\">{getRoomIcon(room.type)}</div>\n            <div className=\"font-medium\">{room.name}</div>\n            <div className=\"text-xs opacity-75\">\n              {room.area ? `${room.area}㎡` : getRoomTypeName(room.type)}\n            </div>\n          </button>\n        ))}\n      </div>\n      <div className=\"mt-3 text-xs text-gray-400 text-center\">\n        点击房间快速切换\n      </div>\n    </div>\n  );\n\n  if (!currentRoom) {\n    return (\n      <div className=\"fixed inset-0 bg-black flex items-center justify-center z-50\">\n        <div className=\"text-white text-center\">\n          <p className=\"text-xl mb-4\">房间未找到</p>\n          <button\n            onClick={onClose}\n            className=\"bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg\"\n          >\n            返回\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50\">\n      {/* VR720场景容器 */}\n      <div ref={containerRef} className=\"w-full h-full relative\" />\n\n      {/* 加载状态 */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/90 flex items-center justify-center z-30\">\n          <div className=\"text-white text-center\">\n            <div className=\"relative mb-6\">\n              <div className=\"animate-spin rounded-full h-16 w-16 border-b-4 border-gradient-to-r from-pink-500 to-blue-500 mx-auto\"></div>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"text-2xl\">🏠</div>\n              </div>\n            </div>\n            <p className=\"text-lg font-bold bg-gradient-to-r from-pink-500 to-blue-500 bg-clip-text text-transparent\">\n              Loading {currentRoom?.name}\n            </p>\n            <p className=\"text-sm text-gray-400 mt-2\">{house.name} - VR720° House Tour</p>\n            <div className=\"mt-4 flex items-center justify-center space-x-2\">\n              <div className=\"w-2 h-2 bg-pink-500 rounded-full animate-pulse\"></div>\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\" style={{animationDelay: '0.2s'}}></div>\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\" style={{animationDelay: '0.4s'}}></div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 顶部控制栏 */}\n      <div className=\"absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-4 z-20 pointer-events-none\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={onClose}\n              className=\"pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n            >\n              <X size={20} />\n            </button>\n\n            <div className=\"text-white\">\n              <h2 className=\"text-lg font-medium\">{house.name}</h2>\n              <p className=\"text-sm text-gray-300\">\n                {getRoomIcon(currentRoom?.type || 'other')} {currentRoom?.name}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => setShowInfo(!showInfo)}\n              className=\"pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n            >\n              <Info size={20} />\n            </button>\n\n            <button\n              onClick={() => setShowFloorPlan(!showFloorPlan)}\n              className=\"pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n            >\n              <Map size={20} />\n            </button>\n\n            <button\n              onClick={toggle720Mode}\n              className=\"pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n              title={is720Mode ? '切换到360°' : '切换到720°'}\n            >\n              {is720Mode ? <Pause size={20} /> : <Play size={20} />}\n            </button>\n\n            <button\n              onClick={() => setShowRoomList(!showRoomList)}\n              className=\"pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n            >\n              <Home size={20} />\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 房间信息面板 */}\n      {showInfo && currentRoom && (\n        <div className=\"absolute top-20 left-4 bg-black/90 text-white p-4 rounded-lg max-w-sm z-20\">\n          <h3 className=\"text-lg font-medium mb-2\">{currentRoom.name}</h3>\n          <p className=\"text-sm text-gray-300 mb-2\">\n            类型: {getRoomTypeName(currentRoom.type)}\n          </p>\n          {currentRoom.area && (\n            <p className=\"text-sm text-gray-300 mb-2\">\n              面积: {currentRoom.area}㎡\n            </p>\n          )}\n          {currentRoom.description && (\n            <p className=\"text-sm text-gray-300 mb-3\">\n              {currentRoom.description}\n            </p>\n          )}\n          <div className=\"text-xs text-gray-400\">\n            <p>• 拖拽旋转视角</p>\n            <p>• 点击蓝色球体切换房间</p>\n            <p>• 当前模式: {is720Mode ? 'VR720°' : 'VR360°'}</p>\n          </div>\n        </div>\n      )}\n\n      {/* 平面图面板 */}\n      {showFloorPlan && <FloorPlan />}\n\n      {/* 房间列表面板 */}\n      {showRoomList && (\n        <div className=\"absolute top-20 right-4 bg-black/90 text-white p-4 rounded-lg max-w-xs z-20\">\n          <h3 className=\"text-lg font-medium mb-3\">房间导航</h3>\n          <div className=\"space-y-2\">\n            {house.rooms.map((room) => (\n              <button\n                key={room.id}\n                onClick={() => navigateToRoom(room.id)}\n                className={`w-full text-left p-3 rounded-lg transition-all ${\n                  room.id === currentRoomId\n                    ? 'bg-blue-600 text-white scale-105'\n                    : 'bg-gray-700 hover:bg-gray-600 text-gray-300'\n                }`}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-lg\">{getRoomIcon(room.type)}</span>\n                  <div>\n                    <p className=\"font-medium\">{room.name}</p>\n                    <p className=\"text-xs opacity-75\">\n                      {getRoomTypeName(room.type)}\n                      {room.hotSpots.length > 0 && ` • ${room.hotSpots.length}个连接点`}\n                    </p>\n                  </div>\n                </div>\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 底部导航栏 */}\n      <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 z-20 pointer-events-none\">\n        <div className=\"flex items-center justify-center space-x-3 mb-4\">\n          {house.rooms.map((room) => (\n            <button\n              key={room.id}\n              onClick={() => navigateToRoom(room.id)}\n              className={`pointer-events-auto w-14 h-14 rounded-full flex items-center justify-center text-lg transition-all ${\n                room.id === currentRoomId\n                  ? 'bg-blue-600 text-white scale-110 shadow-lg'\n                  : 'bg-black/70 text-gray-300 hover:bg-black/90 hover:scale-105'\n              }`}\n              title={room.name}\n            >\n              {getRoomIcon(room.type)}\n            </button>\n          ))}\n        </div>\n\n        {/* 状态指示器 */}\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center bg-black/70 text-white px-4 py-2 rounded-full text-sm\">\n            <div className={`w-2 h-2 rounded-full mr-2 ${is720Mode ? 'bg-green-500' : 'bg-blue-500'}`}></div>\n            {is720Mode ? 'VR720°' : 'VR360°'} House Tour • 拖拽旋转 • 点击球体切换房间\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HouseVRTour;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AA2DA,MAAM,cAA0C,CAAC,EAC/C,KAAK,EACL,OAAO,EACP,WAAW,EACZ;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,eAAe,MAAM,WAAW,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM;IAE5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,cAAc,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEzD,WAAW;IACX,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ;YACZ,aAAa;YACb,SAAS;YACT,SAAS;YACT,UAAU;YACV,aAAa;YACb,QAAQ;YACR,OAAO;QACT;QACA,OAAO,KAAK,CAAC,KAAK,IAAI;IACxB;IAEA,WAAW;IACX,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ;YACZ,aAAa;YACb,SAAS;YACT,SAAS;YACT,UAAU;YACV,aAAa;YACb,QAAQ;YACR,OAAO;QACT;QACA,OAAO,KAAK,CAAC,KAAK,IAAI;IACxB;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,IAAI,WAAW,eAAe;QAE9B,aAAa;QACb,iBAAiB;QAEjB,WAAW;QACX,WAAW;YACT,aAAa;QACf,GAAG;IACL;IAEA,SAAS;IACT,MAAM,0BAA0B;QAC9B,MAAM,YAAY,UAAU,SAAS;QACrC,MAAM,WAAW,2DAA2D,IAAI,CAAC;QACjF,MAAM,WAAW,kDAAkD,IAAI,CAAC;QAExE,UAAU;QACV,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,MAAM,KAAK,OAAO,UAAU,CAAC,YAAY,OAAO,UAAU,CAAC;QAC3D,MAAM,YAAY,IAAI,aAAa;QACnC,MAAM,WAAW,YAAY,IAAI,aAAa,UAAU,uBAAuB,IAAI;QACnF,MAAM,cAAc,uCAAuC,IAAI,CAAC,YAAY;QAE5E,OAAO;YACL;YACA,UAAU,YAAY;YACtB,gBAAgB,IAAI,aAAa,GAAG,gBAAgB,KAAK;QAC3D;IACF;IAEA,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAA0E;;QA+b1E;IACF,GAAG;QAAC;QAAa;QAAO;KAAU;IAElC,cAAc;IACd,MAAM,gBAAgB;QACpB,aAAa,CAAC;QAEd,cAAc;QACd,MAAM,SAAS,aAAa,OAAO,EAAE,cAAc;QACnD,IAAI,QAAQ,eAAe;YACzB,OAAO,aAAa,CAAC,WAAW,CAAC,aAAa;QAChD;IACF;IAEA,UAAU;IACV,MAAM,YAAY,kBAChB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;4BAEC,SAAS,IAAM,eAAe,KAAK,EAAE;4BACrC,WAAW,CAAC,sCAAsC,EAChD,KAAK,EAAE,KAAK,gBACR,qCACA,+CACJ;;8CAEF,8OAAC;oCAAI,WAAU;8CAAgB,YAAY,KAAK,IAAI;;;;;;8CACpD,8OAAC;oCAAI,WAAU;8CAAe,KAAK,IAAI;;;;;;8CACvC,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,KAAK,IAAI;;;;;;;2BAXrD,KAAK,EAAE;;;;;;;;;;8BAgBlB,8OAAC;oBAAI,WAAU;8BAAyC;;;;;;;;;;;;IAM5D,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;;;;;YAGjC,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAW;;;;;;;;;;;;;;;;;sCAG9B,8OAAC;4BAAE,WAAU;;gCAA6F;gCAC/F,aAAa;;;;;;;sCAExB,8OAAC;4BAAE,WAAU;;gCAA8B,MAAM,IAAI;gCAAC;;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;oCAAiD,OAAO;wCAAC,gBAAgB;oCAAM;;;;;;8CAC9F,8OAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAC,gBAAgB;oCAAM;;;;;;;;;;;;;;;;;;;;;;;0BAOvG,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,6LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;8CAGX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuB,MAAM,IAAI;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;;gDACV,YAAY,aAAa,QAAQ;gDAAS;gDAAE,aAAa;;;;;;;;;;;;;;;;;;;sCAKhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,YAAY,CAAC;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,mMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAGd,8OAAC;oCACC,SAAS,IAAM,iBAAiB,CAAC;oCACjC,WAAU;8CAEV,cAAA,8OAAC,iMAAA,CAAA,MAAG;wCAAC,MAAM;;;;;;;;;;;8CAGb,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO,YAAY,YAAY;8CAE9B,0BAAY,8OAAC,qMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,8OAAC,mMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAGjD,8OAAC;oCACC,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;8CAEV,cAAA,8OAAC,mMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOnB,YAAY,6BACX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4B,YAAY,IAAI;;;;;;kCAC1D,8OAAC;wBAAE,WAAU;;4BAA6B;4BACnC,gBAAgB,YAAY,IAAI;;;;;;;oBAEtC,YAAY,IAAI,kBACf,8OAAC;wBAAE,WAAU;;4BAA6B;4BACnC,YAAY,IAAI;4BAAC;;;;;;;oBAGzB,YAAY,WAAW,kBACtB,8OAAC;wBAAE,WAAU;kCACV,YAAY,WAAW;;;;;;kCAG5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;0CAAE;;;;;;0CACH,8OAAC;;oCAAE;oCAAS,YAAY,WAAW;;;;;;;;;;;;;;;;;;;YAMxC,+BAAiB,8OAAC;;;;;YAGlB,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC;gCAEC,SAAS,IAAM,eAAe,KAAK,EAAE;gCACrC,WAAW,CAAC,+CAA+C,EACzD,KAAK,EAAE,KAAK,gBACR,qCACA,+CACJ;0CAEF,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAW,YAAY,KAAK,IAAI;;;;;;sDAChD,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAe,KAAK,IAAI;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDACV,gBAAgB,KAAK,IAAI;wDACzB,KAAK,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,KAAK,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;+BAd9D,KAAK,EAAE;;;;;;;;;;;;;;;;0BAyBtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC;gCAEC,SAAS,IAAM,eAAe,KAAK,EAAE;gCACrC,WAAW,CAAC,mGAAmG,EAC7G,KAAK,EAAE,KAAK,gBACR,+CACA,+DACJ;gCACF,OAAO,KAAK,IAAI;0CAEf,YAAY,KAAK,IAAI;+BATjB,KAAK,EAAE;;;;;;;;;;kCAelB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,0BAA0B,EAAE,YAAY,iBAAiB,eAAe;;;;;;gCACxF,YAAY,WAAW;gCAAS;;;;;;;;;;;;;;;;;;;;;;;;AAM7C;uCAEe", "debugId": null}}, {"offset": {"line": 3065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/components/VR/HouseManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Plus, \n  Edit, \n  Trash2, \n  Save, \n  X, \n  Home,\n  MapPin,\n  Eye,\n  Settings,\n  Upload,\n  Camera,\n  Navigation\n} from 'lucide-react';\nimport { House, Room, HotSpot } from './HouseVRTour';\n\ninterface HouseManagerProps {\n  onClose: () => void;\n  onHouseCreated: (house: House) => void;\n  existingHouse?: House;\n}\n\nconst HouseManager: React.FC<HouseManagerProps> = ({ \n  onClose, \n  onHouseCreated, \n  existingHouse \n}) => {\n  const [house, setHouse] = useState<House>(\n    existingHouse || {\n      id: Date.now().toString(),\n      name: '',\n      address: '',\n      description: '',\n      rooms: [],\n      startRoomId: ''\n    }\n  );\n\n  const [currentStep, setCurrentStep] = useState<'basic' | 'rooms' | 'connections'>('basic');\n  const [selectedRoomId, setSelectedRoomId] = useState<string>('');\n  const [isAddingHotSpot, setIsAddingHotSpot] = useState(false);\n  const [hotSpotPosition, setHotSpotPosition] = useState<{ x: number; y: number }>({ x: 0.5, y: 0.5 });\n\n  // 房间类型选项\n  const roomTypes: Array<{ value: Room['type']; label: string; icon: string }> = [\n    { value: 'living_room', label: '客厅', icon: '🛋️' },\n    { value: 'bedroom', label: '卧室', icon: '🛏️' },\n    { value: 'kitchen', label: '厨房', icon: '🍳' },\n    { value: 'bathroom', label: '浴室', icon: '🚿' },\n    { value: 'dining_room', label: '餐厅', icon: '🍽️' },\n    { value: 'office', label: '书房', icon: '💼' },\n    { value: 'other', label: '其他', icon: '🏠' }\n  ];\n\n  // 添加房间\n  const addRoom = () => {\n    const newRoom: Room = {\n      id: Date.now().toString(),\n      name: `房间 ${house.rooms.length + 1}`,\n      type: 'other',\n      panoramaUrl: '',\n      hotSpots: [],\n      description: ''\n    };\n\n    setHouse(prev => ({\n      ...prev,\n      rooms: [...prev.rooms, newRoom]\n    }));\n  };\n\n  // 更新房间\n  const updateRoom = (roomId: string, updates: Partial<Room>) => {\n    setHouse(prev => ({\n      ...prev,\n      rooms: prev.rooms.map(room => \n        room.id === roomId ? { ...room, ...updates } : room\n      )\n    }));\n  };\n\n  // 删除房间\n  const deleteRoom = (roomId: string) => {\n    setHouse(prev => ({\n      ...prev,\n      rooms: prev.rooms.filter(room => room.id !== roomId),\n      startRoomId: prev.startRoomId === roomId ? '' : prev.startRoomId\n    }));\n\n    // 同时删除其他房间中指向该房间的连接点\n    setHouse(prev => ({\n      ...prev,\n      rooms: prev.rooms.map(room => ({\n        ...room,\n        hotSpots: room.hotSpots.filter(hotSpot => hotSpot.targetRoomId !== roomId)\n      }))\n    }));\n  };\n\n  // 添加连接点\n  const addHotSpot = (roomId: string, targetRoomId: string) => {\n    const targetRoom = house.rooms.find(room => room.id === targetRoomId);\n    if (!targetRoom) return;\n\n    const newHotSpot: HotSpot = {\n      id: Date.now().toString(),\n      x: hotSpotPosition.x,\n      y: hotSpotPosition.y,\n      targetRoomId,\n      title: `前往${targetRoom.name}`,\n      description: `从当前房间前往${targetRoom.name}`\n    };\n\n    updateRoom(roomId, {\n      hotSpots: [...(house.rooms.find(room => room.id === roomId)?.hotSpots || []), newHotSpot]\n    });\n\n    setIsAddingHotSpot(false);\n  };\n\n  // 删除连接点\n  const deleteHotSpot = (roomId: string, hotSpotId: string) => {\n    const room = house.rooms.find(room => room.id === roomId);\n    if (!room) return;\n\n    updateRoom(roomId, {\n      hotSpots: room.hotSpots.filter(hotSpot => hotSpot.id !== hotSpotId)\n    });\n  };\n\n  // 处理全景图上传\n  const handlePanoramaUpload = (roomId: string, file: File) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      const dataUrl = e.target?.result as string;\n      updateRoom(roomId, { panoramaUrl: dataUrl });\n    };\n    reader.readAsDataURL(file);\n  };\n\n  // 保存房屋\n  const saveHouse = () => {\n    if (!house.name.trim()) {\n      alert('请输入房屋名称');\n      return;\n    }\n\n    if (house.rooms.length === 0) {\n      alert('请至少添加一个房间');\n      return;\n    }\n\n    const roomsWithoutPanorama = house.rooms.filter(room => !room.panoramaUrl);\n    if (roomsWithoutPanorama.length > 0) {\n      alert('请为所有房间上传全景图');\n      return;\n    }\n\n    // 如果没有设置起始房间，默认使用第一个房间\n    if (!house.startRoomId && house.rooms.length > 0) {\n      setHouse(prev => ({ ...prev, startRoomId: prev.rooms[0].id }));\n    }\n\n    onHouseCreated(house);\n    onClose();\n  };\n\n  const selectedRoom = house.rooms.find(room => room.id === selectedRoomId);\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        {/* 头部 */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <div>\n            <h2 className=\"text-xl font-bold text-gray-900\">\n              {existingHouse ? '编辑' : '创建'}全屋漫游\n            </h2>\n            <p className=\"text-sm text-gray-600 mt-1\">\n              设置房间和连接点，创建沉浸式VR体验\n            </p>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center\"\n          >\n            <X size={20} />\n          </button>\n        </div>\n\n        {/* 步骤导航 */}\n        <div className=\"flex border-b border-gray-200\">\n          <button\n            onClick={() => setCurrentStep('basic')}\n            className={`flex-1 py-3 px-4 text-sm font-medium ${\n              currentStep === 'basic'\n                ? 'text-blue-600 border-b-2 border-blue-600'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            基本信息\n          </button>\n          <button\n            onClick={() => setCurrentStep('rooms')}\n            className={`flex-1 py-3 px-4 text-sm font-medium ${\n              currentStep === 'rooms'\n                ? 'text-blue-600 border-b-2 border-blue-600'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            房间设置\n          </button>\n          <button\n            onClick={() => setCurrentStep('connections')}\n            className={`flex-1 py-3 px-4 text-sm font-medium ${\n              currentStep === 'connections'\n                ? 'text-blue-600 border-b-2 border-blue-600'\n                : 'text-gray-600 hover:text-gray-900'\n            }`}\n          >\n            连接点设置\n          </button>\n        </div>\n\n        {/* 内容区域 */}\n        <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n          {currentStep === 'basic' && (\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  房屋名称 *\n                </label>\n                <input\n                  type=\"text\"\n                  value={house.name}\n                  onChange={(e) => setHouse(prev => ({ ...prev, name: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"例如：豪华别墅、现代公寓等\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  地址\n                </label>\n                <input\n                  type=\"text\"\n                  value={house.address}\n                  onChange={(e) => setHouse(prev => ({ ...prev, address: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"房屋地址\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  描述\n                </label>\n                <textarea\n                  value={house.description}\n                  onChange={(e) => setHouse(prev => ({ ...prev, description: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                  rows={3}\n                  placeholder=\"房屋描述信息\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  起始房间\n                </label>\n                <select\n                  value={house.startRoomId}\n                  onChange={(e) => setHouse(prev => ({ ...prev, startRoomId: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                >\n                  <option value=\"\">选择起始房间</option>\n                  {house.rooms.map(room => (\n                    <option key={room.id} value={room.id}>\n                      {room.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          )}\n\n          {currentStep === 'rooms' && (\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-medium text-gray-900\">房间管理</h3>\n                <button\n                  onClick={addRoom}\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center\"\n                >\n                  <Plus size={16} className=\"mr-2\" />\n                  添加房间\n                </button>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {house.rooms.map((room) => (\n                  <div key={room.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <input\n                        type=\"text\"\n                        value={room.name}\n                        onChange={(e) => updateRoom(room.id, { name: e.target.value })}\n                        className=\"font-medium text-gray-900 bg-transparent border-none outline-none\"\n                      />\n                      <button\n                        onClick={() => deleteRoom(room.id)}\n                        className=\"text-red-600 hover:text-red-700\"\n                      >\n                        <Trash2 size={16} />\n                      </button>\n                    </div>\n\n                    <div className=\"space-y-3\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                          房间类型\n                        </label>\n                        <select\n                          value={room.type}\n                          onChange={(e) => updateRoom(room.id, { type: e.target.value as Room['type'] })}\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded\"\n                        >\n                          {roomTypes.map(type => (\n                            <option key={type.value} value={type.value}>\n                              {type.icon} {type.label}\n                            </option>\n                          ))}\n                        </select>\n                      </div>\n\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                          全景图 *\n                        </label>\n                        {room.panoramaUrl ? (\n                          <div className=\"relative\">\n                            <img\n                              src={room.panoramaUrl}\n                              alt={room.name}\n                              className=\"w-full h-20 object-cover rounded\"\n                            />\n                            <button\n                              onClick={() => updateRoom(room.id, { panoramaUrl: '' })}\n                              className=\"absolute top-1 right-1 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-xs\"\n                            >\n                              <X size={12} />\n                            </button>\n                          </div>\n                        ) : (\n                          <label className=\"block w-full h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-blue-500\">\n                            <input\n                              type=\"file\"\n                              accept=\"image/*\"\n                              className=\"hidden\"\n                              onChange={(e) => {\n                                const file = e.target.files?.[0];\n                                if (file) handlePanoramaUpload(room.id, file);\n                              }}\n                            />\n                            <div className=\"text-center\">\n                              <Upload size={20} className=\"mx-auto text-gray-400 mb-1\" />\n                              <p className=\"text-xs text-gray-600\">上传全景图</p>\n                            </div>\n                          </label>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {currentStep === 'connections' && (\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-medium text-gray-900\">连接点设置</h3>\n              \n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* 房间列表 */}\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">选择房间</h4>\n                  <div className=\"space-y-2\">\n                    {house.rooms.map((room) => (\n                      <button\n                        key={room.id}\n                        onClick={() => setSelectedRoomId(room.id)}\n                        className={`w-full text-left p-3 rounded-lg border ${\n                          selectedRoomId === room.id\n                            ? 'border-blue-500 bg-blue-50'\n                            : 'border-gray-200 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"flex items-center justify-between\">\n                          <span className=\"font-medium\">{room.name}</span>\n                          <span className=\"text-sm text-gray-500\">\n                            {room.hotSpots.length} 个连接点\n                          </span>\n                        </div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* 连接点编辑 */}\n                {selectedRoom && (\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 mb-3\">\n                      {selectedRoom.name} - 连接点\n                    </h4>\n                    \n                    {selectedRoom.panoramaUrl ? (\n                      <div className=\"relative\">\n                        <img\n                          src={selectedRoom.panoramaUrl}\n                          alt={selectedRoom.name}\n                          className=\"w-full h-48 object-cover rounded-lg cursor-crosshair\"\n                          onClick={(e) => {\n                            if (isAddingHotSpot) {\n                              const rect = e.currentTarget.getBoundingClientRect();\n                              const x = (e.clientX - rect.left) / rect.width;\n                              const y = (e.clientY - rect.top) / rect.height;\n                              setHotSpotPosition({ x, y });\n                            }\n                          }}\n                        />\n                        \n                        {/* 显示现有连接点 */}\n                        {selectedRoom.hotSpots.map((hotSpot) => (\n                          <button\n                            key={hotSpot.id}\n                            className=\"absolute w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs transform -translate-x-1/2 -translate-y-1/2\"\n                            style={{\n                              left: `${hotSpot.x * 100}%`,\n                              top: `${hotSpot.y * 100}%`\n                            }}\n                            onClick={() => deleteHotSpot(selectedRoom.id, hotSpot.id)}\n                            title={`删除连接点: ${hotSpot.title}`}\n                          >\n                            <Navigation size={12} />\n                          </button>\n                        ))}\n                        \n                        {/* 新连接点预览 */}\n                        {isAddingHotSpot && (\n                          <div\n                            className=\"absolute w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs transform -translate-x-1/2 -translate-y-1/2 animate-pulse\"\n                            style={{\n                              left: `${hotSpotPosition.x * 100}%`,\n                              top: `${hotSpotPosition.y * 100}%`\n                            }}\n                          >\n                            <Plus size={12} />\n                          </div>\n                        )}\n                      </div>\n                    ) : (\n                      <div className=\"w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center\">\n                        <p className=\"text-gray-500\">请先上传全景图</p>\n                      </div>\n                    )}\n\n                    {/* 连接点操作 */}\n                    <div className=\"mt-4 space-y-3\">\n                      {!isAddingHotSpot ? (\n                        <button\n                          onClick={() => setIsAddingHotSpot(true)}\n                          disabled={!selectedRoom.panoramaUrl}\n                          className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg flex items-center justify-center\"\n                        >\n                          <Plus size={16} className=\"mr-2\" />\n                          添加连接点\n                        </button>\n                      ) : (\n                        <div className=\"space-y-2\">\n                          <p className=\"text-sm text-gray-600\">\n                            点击全景图选择连接点位置，然后选择目标房间\n                          </p>\n                          <div className=\"flex space-x-2\">\n                            {house.rooms\n                              .filter(room => room.id !== selectedRoom.id)\n                              .map(room => (\n                                <button\n                                  key={room.id}\n                                  onClick={() => addHotSpot(selectedRoom.id, room.id)}\n                                  className=\"flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm\"\n                                >\n                                  前往{room.name}\n                                </button>\n                              ))}\n                          </div>\n                          <button\n                            onClick={() => setIsAddingHotSpot(false)}\n                            className=\"w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg\"\n                          >\n                            取消\n                          </button>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* 底部操作栏 */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200\">\n          <button\n            onClick={onClose}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\"\n          >\n            取消\n          </button>\n          \n          <div className=\"flex space-x-3\">\n            {currentStep !== 'basic' && (\n              <button\n                onClick={() => {\n                  if (currentStep === 'rooms') setCurrentStep('basic');\n                  if (currentStep === 'connections') setCurrentStep('rooms');\n                }}\n                className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50\"\n              >\n                上一步\n              </button>\n            )}\n            \n            {currentStep !== 'connections' ? (\n              <button\n                onClick={() => {\n                  if (currentStep === 'basic') setCurrentStep('rooms');\n                  if (currentStep === 'rooms') setCurrentStep('connections');\n                }}\n                className=\"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg\"\n              >\n                下一步\n              </button>\n            ) : (\n              <button\n                onClick={saveHouse}\n                className=\"px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center\"\n              >\n                <Save size={16} className=\"mr-2\" />\n                保存房屋\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default HouseManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAyBA,MAAM,eAA4C,CAAC,EACjD,OAAO,EACP,cAAc,EACd,aAAa,EACd;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/B,iBAAiB;QACf,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,MAAM;QACN,SAAS;QACT,aAAa;QACb,OAAO,EAAE;QACT,aAAa;IACf;IAGF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAClF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QAAE,GAAG;QAAK,GAAG;IAAI;IAElG,SAAS;IACT,MAAM,YAAyE;QAC7E;YAAE,OAAO;YAAe,OAAO;YAAM,MAAM;QAAM;QACjD;YAAE,OAAO;YAAW,OAAO;YAAM,MAAM;QAAM;QAC7C;YAAE,OAAO;YAAW,OAAO;YAAM,MAAM;QAAK;QAC5C;YAAE,OAAO;YAAY,OAAO;YAAM,MAAM;QAAK;QAC7C;YAAE,OAAO;YAAe,OAAO;YAAM,MAAM;QAAM;QACjD;YAAE,OAAO;YAAU,OAAO;YAAM,MAAM;QAAK;QAC3C;YAAE,OAAO;YAAS,OAAO;YAAM,MAAM;QAAK;KAC3C;IAED,OAAO;IACP,MAAM,UAAU;QACd,MAAM,UAAgB;YACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM,CAAC,GAAG,EAAE,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG;YACpC,MAAM;YACN,aAAa;YACb,UAAU,EAAE;YACZ,aAAa;QACf;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,OAAO;uBAAI,KAAK,KAAK;oBAAE;iBAAQ;YACjC,CAAC;IACH;IAEA,OAAO;IACP,MAAM,aAAa,CAAC,QAAgB;QAClC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OACpB,KAAK,EAAE,KAAK,SAAS;wBAAE,GAAG,IAAI;wBAAE,GAAG,OAAO;oBAAC,IAAI;YAEnD,CAAC;IACH;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAC7C,aAAa,KAAK,WAAW,KAAK,SAAS,KAAK,KAAK,WAAW;YAClE,CAAC;QAED,qBAAqB;QACrB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC7B,GAAG,IAAI;wBACP,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK;oBACrE,CAAC;YACH,CAAC;IACH;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC,QAAgB;QAClC,MAAM,aAAa,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACxD,IAAI,CAAC,YAAY;QAEjB,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,GAAG,gBAAgB,CAAC;YACpB,GAAG,gBAAgB,CAAC;YACpB;YACA,OAAO,CAAC,EAAE,EAAE,WAAW,IAAI,EAAE;YAC7B,aAAa,CAAC,OAAO,EAAE,WAAW,IAAI,EAAE;QAC1C;QAEA,WAAW,QAAQ;YACjB,UAAU;mBAAK,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,YAAY,EAAE;gBAAG;aAAW;QAC3F;QAEA,mBAAmB;IACrB;IAEA,QAAQ;IACR,MAAM,gBAAgB,CAAC,QAAgB;QACrC,MAAM,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,CAAC,MAAM;QAEX,WAAW,QAAQ;YACjB,UAAU,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D;IACF;IAEA,UAAU;IACV,MAAM,uBAAuB,CAAC,QAAgB;QAC5C,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,EAAE,MAAM,EAAE;YAC1B,WAAW,QAAQ;gBAAE,aAAa;YAAQ;QAC5C;QACA,OAAO,aAAa,CAAC;IACvB;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI;YACtB,MAAM;YACN;QACF;QAEA,IAAI,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG;YAC5B,MAAM;YACN;QACF;QAEA,MAAM,uBAAuB,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,WAAW;QACzE,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,MAAM;YACN;QACF;QAEA,uBAAuB;QACvB,IAAI,CAAC,MAAM,WAAW,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG;YAChD,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,aAAa,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE;gBAAC,CAAC;QAC9D;QAEA,eAAe;QACf;IACF;IAEA,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAE1D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCACX,gBAAgB,OAAO;wCAAK;;;;;;;8CAE/B,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,6LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAW,CAAC,qCAAqC,EAC/C,gBAAgB,UACZ,6CACA,qCACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAW,CAAC,qCAAqC,EAC/C,gBAAgB,UACZ,6CACA,qCACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAW,CAAC,qCAAqC,EAC/C,gBAAgB,gBACZ,6CACA,qCACJ;sCACH;;;;;;;;;;;;8BAMH,8OAAC;oBAAI,WAAU;;wBACZ,gBAAgB,yBACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,MAAM,IAAI;4CACjB,UAAU,CAAC,IAAM,SAAS,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACpE,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,MAAM,OAAO;4CACpB,UAAU,CAAC,IAAM,SAAS,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACvE,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,MAAM,WAAW;4CACxB,UAAU,CAAC,IAAM,SAAS,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC3E,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,MAAM,WAAW;4CACxB,UAAU,CAAC,IAAM,SAAS,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC3E,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,qBACf,8OAAC;wDAAqB,OAAO,KAAK,EAAE;kEACjC,KAAK,IAAI;uDADC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;wBAS7B,gBAAgB,yBACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,mMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDAAS;;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;8CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO,KAAK,IAAI;4DAChB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC;4DAC5D,WAAU;;;;;;sEAEZ,8OAAC;4DACC,SAAS,IAAM,WAAW,KAAK,EAAE;4DACjC,WAAU;sEAEV,cAAA,8OAAC,2MAAA,CAAA,SAAM;gEAAC,MAAM;;;;;;;;;;;;;;;;;8DAIlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,OAAO,KAAK,IAAI;oEAChB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE;4EAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wEAAiB;oEAC5E,WAAU;8EAET,UAAU,GAAG,CAAC,CAAA,qBACb,8OAAC;4EAAwB,OAAO,KAAK,KAAK;;gFACvC,KAAK,IAAI;gFAAC;gFAAE,KAAK,KAAK;;2EADZ,KAAK,KAAK;;;;;;;;;;;;;;;;sEAO7B,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;gEAG/D,KAAK,WAAW,iBACf,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,KAAK,KAAK,WAAW;4EACrB,KAAK,KAAK,IAAI;4EACd,WAAU;;;;;;sFAEZ,8OAAC;4EACC,SAAS,IAAM,WAAW,KAAK,EAAE,EAAE;oFAAE,aAAa;gFAAG;4EACrD,WAAU;sFAEV,cAAA,8OAAC,6LAAA,CAAA,IAAC;gFAAC,MAAM;;;;;;;;;;;;;;;;yFAIb,8OAAC;oEAAM,WAAU;;sFACf,8OAAC;4EACC,MAAK;4EACL,QAAO;4EACP,WAAU;4EACV,UAAU,CAAC;gFACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;gFAChC,IAAI,MAAM,qBAAqB,KAAK,EAAE,EAAE;4EAC1C;;;;;;sFAEF,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,uMAAA,CAAA,SAAM;oFAAC,MAAM;oFAAI,WAAU;;;;;;8FAC5B,8OAAC;oFAAE,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAjEvC,KAAK,EAAE;;;;;;;;;;;;;;;;wBA6ExB,gBAAgB,+BACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC;4DAEC,SAAS,IAAM,kBAAkB,KAAK,EAAE;4DACxC,WAAW,CAAC,uCAAuC,EACjD,mBAAmB,KAAK,EAAE,GACtB,+BACA,yCACJ;sEAEF,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAe,KAAK,IAAI;;;;;;kFACxC,8OAAC;wEAAK,WAAU;;4EACb,KAAK,QAAQ,CAAC,MAAM;4EAAC;;;;;;;;;;;;;2DAXrB,KAAK,EAAE;;;;;;;;;;;;;;;;wCAoBnB,8BACC,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;wDACX,aAAa,IAAI;wDAAC;;;;;;;gDAGpB,aAAa,WAAW,iBACvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAK,aAAa,WAAW;4DAC7B,KAAK,aAAa,IAAI;4DACtB,WAAU;4DACV,SAAS,CAAC;gEACR,IAAI,iBAAiB;oEACnB,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;oEAClD,MAAM,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;oEAC9C,MAAM,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,MAAM;oEAC9C,mBAAmB;wEAAE;wEAAG;oEAAE;gEAC5B;4DACF;;;;;;wDAID,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC1B,8OAAC;gEAEC,WAAU;gEACV,OAAO;oEACL,MAAM,GAAG,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;oEAC3B,KAAK,GAAG,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;gEAC5B;gEACA,SAAS,IAAM,cAAc,aAAa,EAAE,EAAE,QAAQ,EAAE;gEACxD,OAAO,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;0EAEhC,cAAA,8OAAC,+MAAA,CAAA,aAAU;oEAAC,MAAM;;;;;;+DATb,QAAQ,EAAE;;;;;wDAclB,iCACC,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,MAAM,GAAG,gBAAgB,CAAC,GAAG,IAAI,CAAC,CAAC;gEACnC,KAAK,GAAG,gBAAgB,CAAC,GAAG,IAAI,CAAC,CAAC;4DACpC;sEAEA,cAAA,8OAAC,mMAAA,CAAA,OAAI;gEAAC,MAAM;;;;;;;;;;;;;;;;yEAKlB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;8DAKjC,8OAAC;oDAAI,WAAU;8DACZ,CAAC,gCACA,8OAAC;wDACC,SAAS,IAAM,mBAAmB;wDAClC,UAAU,CAAC,aAAa,WAAW;wDACnC,WAAU;;0EAEV,8OAAC,mMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAAS;;;;;;6EAIrC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EAGrC,8OAAC;gEAAI,WAAU;0EACZ,MAAM,KAAK,CACT,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,aAAa,EAAE,EAC1C,GAAG,CAAC,CAAA,qBACH,8OAAC;wEAEC,SAAS,IAAM,WAAW,aAAa,EAAE,EAAE,KAAK,EAAE;wEAClD,WAAU;;4EACX;4EACI,KAAK,IAAI;;uEAJP,KAAK,EAAE;;;;;;;;;;0EAQpB,8OAAC;gEACC,SAAS,IAAM,mBAAmB;gEAClC,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAcnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAID,8OAAC;4BAAI,WAAU;;gCACZ,gBAAgB,yBACf,8OAAC;oCACC,SAAS;wCACP,IAAI,gBAAgB,SAAS,eAAe;wCAC5C,IAAI,gBAAgB,eAAe,eAAe;oCACpD;oCACA,WAAU;8CACX;;;;;;gCAKF,gBAAgB,8BACf,8OAAC;oCACC,SAAS;wCACP,IAAI,gBAAgB,SAAS,eAAe;wCAC5C,IAAI,gBAAgB,SAAS,eAAe;oCAC9C;oCACA,WAAU;8CACX;;;;;yDAID,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,mMAAA,CAAA,OAAI;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;uCAEe", "debugId": null}}, {"offset": {"line": 4075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/components/VR/MobileOptimizedVR.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { X, Home, Info, Map, Play, Pause } from 'lucide-react';\n\ninterface MobileOptimizedVRProps {\n  panoramaUrl: string;\n  roomName: string;\n  houseName: string;\n  onClose: () => void;\n  is720Mode?: boolean;\n  onToggleMode?: () => void;\n}\n\nconst MobileOptimizedVR: React.FC<MobileOptimizedVRProps> = ({\n  panoramaUrl,\n  roomName,\n  houseName,\n  onClose,\n  is720Mode = true,\n  onToggleMode\n}) => {\n  const [isLoading, setIsLoading] = useState(true);\n  const [showInfo, setShowInfo] = useState(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (typeof window !== 'undefined' && containerRef.current) {\n      setIsLoading(true);\n\n      // 检测设备性能\n      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      const isLowEnd = /Android.*4\\.|iPhone.*OS [5-9]_|iPad.*OS [5-9]_/i.test(navigator.userAgent);\n\n      // 移动端优化的VR场景\n      const sceneHTML = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=no\">\n          <script src=\"https://aframe.io/releases/1.4.0/aframe.min.js\"></script>\n          <style>\n            body { \n              margin: 0; \n              overflow: hidden; \n              background: #000;\n              touch-action: none;\n              -webkit-user-select: none;\n              user-select: none;\n              -webkit-overflow-scrolling: touch;\n            }\n            #vr-scene { \n              width: 100vw; \n              height: 100vh; \n              position: fixed;\n              top: 0;\n              left: 0;\n            }\n            .loading {\n              position: fixed;\n              top: 50%;\n              left: 50%;\n              transform: translate(-50%, -50%);\n              color: white;\n              font-family: Arial, sans-serif;\n              text-align: center;\n              z-index: 1000;\n            }\n            .spinner {\n              width: 30px;\n              height: 30px;\n              border: 2px solid rgba(255,255,255,0.3);\n              border-top: 2px solid #4ecdc4;\n              border-radius: 50%;\n              animation: spin 1s linear infinite;\n              margin: 0 auto 10px;\n            }\n            @keyframes spin {\n              0% { transform: rotate(0deg); }\n              100% { transform: rotate(360deg); }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"loading\" id=\"loading\">\n            <div class=\"spinner\"></div>\n            <div>Loading ${roomName}...</div>\n          </div>\n          \n          <a-scene \n            id=\"vr-scene\" \n            embedded \n            vr-mode-ui=\"enabled: false\"\n            background=\"color: #000\"\n            device-orientation-permission-ui=\"enabled: false\"\n            renderer=\"antialias: false; \n                     logarithmicDepthBuffer: false; \n                     precision: ${isLowEnd ? 'lowp' : 'mediump'}; \n                     maxCanvasWidth: ${isLowEnd ? 768 : 1024}; \n                     maxCanvasHeight: ${isLowEnd ? 768 : 1024};\n                     colorManagement: false;\n                     sortObjects: false;\n                     physicallyCorrectLights: false\"\n            stats=\"false\"\n          >\n            <a-assets>\n              <img id=\"panorama\" src=\"${panoramaUrl}\" crossorigin=\"anonymous\">\n            </a-assets>\n            \n            <!-- 简化的天空球 -->\n            <a-sky \n              id=\"room-sky\"\n              src=\"#panorama\" \n              rotation=\"0 -90 0\"\n              animation=\"property: rotation; to: 0 ${is720Mode ? '630' : '270'} 0; loop: true; dur: ${isLowEnd ? (is720Mode ? 240000 : 150000) : (is720Mode ? 180000 : 100000)}; easing: linear\"\n              material=\"side: back; npot: true\"\n            ></a-sky>\n            \n            <!-- 优化的相机 -->\n            <a-camera \n              id=\"main-camera\"\n              look-controls=\"enabled: true; \n                           reverseMouseDrag: false; \n                           touchEnabled: true; \n                           magicWindowTrackingEnabled: true;\n                           pointerLockEnabled: false;\n                           mouseSensitivity: 0.2;\n                           touchSensitivity: ${isLowEnd ? 4 : 8}\"\n              wasd-controls=\"enabled: false\"\n              position=\"0 1.6 0\"\n              fov=\"75\"\n            ></a-camera>\n            \n            <!-- 简化的环境光 -->\n            <a-light type=\"ambient\" color=\"#666666\"></a-light>\n            \n            <!-- 简化的房间标题 -->\n            <a-text\n              value=\"${roomName}\"\n              position=\"0 3 -4\"\n              align=\"center\"\n              color=\"#4ecdc4\"\n              opacity=\"0.8\"\n              font=\"size: 14; weight: bold\"\n            ></a-text>\n          </a-scene>\n\n          <script>\n            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n            \n            document.addEventListener('DOMContentLoaded', function() {\n              // 移动端优化\n              if (isMobile) {\n                // 禁用右键菜单和滚动\n                document.addEventListener('contextmenu', e => e.preventDefault());\n                document.addEventListener('touchmove', e => e.preventDefault(), { passive: false });\n                \n                // 优化触摸响应\n                let touchStartTime = 0;\n                document.addEventListener('touchstart', e => {\n                  touchStartTime = Date.now();\n                }, { passive: true });\n                \n                document.addEventListener('touchend', e => {\n                  const touchDuration = Date.now() - touchStartTime;\n                  if (touchDuration < 200) {\n                    // 短触摸，可以添加特定操作\n                  }\n                }, { passive: true });\n              }\n              \n              // 隐藏加载界面\n              const scene = document.querySelector('a-scene');\n              const loading = document.querySelector('#loading');\n              \n              const hideLoading = () => {\n                if (loading) {\n                  loading.style.display = 'none';\n                }\n                // 通知父窗口加载完成\n                window.parent.postMessage({ type: 'loaded' }, '*');\n              };\n              \n              if (scene) {\n                scene.addEventListener('loaded', () => {\n                  setTimeout(hideLoading, 800);\n                });\n              } else {\n                setTimeout(hideLoading, 2000);\n              }\n            });\n            \n            // 监听父窗口消息\n            window.addEventListener('message', function(event) {\n              if (event.data === 'toggle720') {\n                const sky = document.querySelector('#room-sky');\n                const currentAnimation = sky.getAttribute('animation');\n                const isCurrently720 = currentAnimation.to.includes('630');\n                \n                const isLowEnd = ${isLowEnd};\n                const duration720 = isLowEnd ? 240000 : 180000;\n                const duration360 = isLowEnd ? 150000 : 100000;\n                \n                if (isCurrently720) {\n                  sky.setAttribute('animation', \\`property: rotation; to: 0 270 0; loop: true; dur: \\${duration360}; easing: linear\\`);\n                } else {\n                  sky.setAttribute('animation', \\`property: rotation; to: 0 630 0; loop: true; dur: \\${duration720}; easing: linear\\`);\n                }\n                \n                // 通知父窗口模式已切换\n                window.parent.postMessage({ \n                  type: 'modeChanged', \n                  is720Mode: !isCurrently720 \n                }, '*');\n              }\n            });\n          </script>\n        </body>\n        </html>\n      `;\n\n      // 创建iframe\n      const iframe = document.createElement('iframe');\n      iframe.style.width = '100%';\n      iframe.style.height = '100%';\n      iframe.style.border = 'none';\n      iframe.style.position = 'absolute';\n      iframe.style.top = '0';\n      iframe.style.left = '0';\n      iframe.srcdoc = sceneHTML;\n\n      // 监听iframe消息\n      const handleMessage = (event: MessageEvent) => {\n        if (event.data?.type === 'loaded') {\n          setIsLoading(false);\n        } else if (event.data?.type === 'modeChanged') {\n          // 处理模式切换\n        }\n      };\n      \n      window.addEventListener('message', handleMessage);\n      \n      containerRef.current.appendChild(iframe);\n      \n      return () => {\n        window.removeEventListener('message', handleMessage);\n        if (containerRef.current && iframe) {\n          containerRef.current.removeChild(iframe);\n        }\n      };\n    }\n  }, [panoramaUrl, roomName, is720Mode]);\n\n  // 切换720/360模式\n  const handleToggleMode = () => {\n    const iframe = containerRef.current?.querySelector('iframe');\n    if (iframe?.contentWindow) {\n      iframe.contentWindow.postMessage('toggle720', '*');\n    }\n    onToggleMode?.();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50\">\n      {/* VR场景容器 */}\n      <div ref={containerRef} className=\"w-full h-full relative\" />\n\n      {/* 加载状态 */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/90 flex items-center justify-center z-30\">\n          <div className=\"text-white text-center\">\n            <div className=\"relative mb-4\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-3 border-blue-500 mx-auto\"></div>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"text-lg\">🏠</div>\n              </div>\n            </div>\n            <p className=\"text-lg font-bold text-blue-400\">Loading {roomName}</p>\n            <p className=\"text-sm text-gray-400 mt-1\">{houseName} - Mobile VR</p>\n          </div>\n        </div>\n      )}\n\n      {/* 简化的控制栏 */}\n      <div className=\"absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-3 z-20 pointer-events-none\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n            >\n              <X size={18} />\n            </button>\n            \n            <div className=\"text-white\">\n              <h2 className=\"text-base font-medium\">{houseName}</h2>\n              <p className=\"text-sm text-gray-300\">🏠 {roomName}</p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => setShowInfo(!showInfo)}\n              className=\"pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n            >\n              <Info size={18} />\n            </button>\n            \n            <button\n              onClick={handleToggleMode}\n              className=\"pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n              title={is720Mode ? '切换到360°' : '切换到720°'}\n            >\n              {is720Mode ? <Pause size={18} /> : <Play size={18} />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 信息面板 */}\n      {showInfo && (\n        <div className=\"absolute top-16 left-3 bg-black/90 text-white p-3 rounded-lg max-w-xs z-20\">\n          <h3 className=\"text-base font-medium mb-2\">{roomName}</h3>\n          <div className=\"text-xs text-gray-400 space-y-1\">\n            <p>• 拖拽旋转视角</p>\n            <p>• 当前模式: {is720Mode ? 'VR720°' : 'VR360°'}</p>\n            <p>• 移动端优化版本</p>\n          </div>\n        </div>\n      )}\n\n      {/* 底部状态指示器 */}\n      <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3 z-20 pointer-events-none\">\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center bg-black/70 text-white px-3 py-1 rounded-full text-xs\">\n            <div className={`w-2 h-2 rounded-full mr-2 ${is720Mode ? 'bg-green-500' : 'bg-blue-500'}`}></div>\n            {is720Mode ? 'VR720°' : 'VR360°'} Mobile • 拖拽旋转视角\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MobileOptimizedVR;\n\nconst duration720 = isLowEnd ? 360000 : 240000;\nconst duration360 = isLowEnd ? 240000 : 120000;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAcA,MAAM,oBAAsD,CAAC,EAC3D,WAAW,EACX,QAAQ,EACR,SAAS,EACT,OAAO,EACP,YAAY,IAAI,EAChB,YAAY,EACb;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAA2D;;QAgO3D;IACF,GAAG;QAAC;QAAa;QAAU;KAAU;IAErC,cAAc;IACd,MAAM,mBAAmB;QACvB,MAAM,SAAS,aAAa,OAAO,EAAE,cAAc;QACnD,IAAI,QAAQ,eAAe;YACzB,OAAO,aAAa,CAAC,WAAW,CAAC,aAAa;QAChD;QACA;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;;;;;YAGjC,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;;gCAAkC;gCAAS;;;;;;;sCACxD,8OAAC;4BAAE,WAAU;;gCAA8B;gCAAU;;;;;;;;;;;;;;;;;;0BAM3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,6LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;8CAGX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI;;;;;;;;;;;;;;;;;;;sCAI7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,YAAY,CAAC;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,mMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAGd,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO,YAAY,YAAY;8CAE9B,0BAAY,8OAAC,qMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,8OAAC,mMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOtD,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAC5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;;oCAAE;oCAAS,YAAY,WAAW;;;;;;;0CACnC,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;0BAMT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,0BAA0B,EAAE,YAAY,iBAAiB,eAAe;;;;;;4BACxF,YAAY,WAAW;4BAAS;;;;;;;;;;;;;;;;;;;;;;;AAM7C;uCAEe;AAEf,MAAM,cAAc,WAAW,SAAS;AACxC,MAAM,cAAc,WAAW,SAAS", "debugId": null}}, {"offset": {"line": 4401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/app/mobile/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Camera, \n  Home, \n  Eye, \n  Settings, \n  Plus, \n  Grid,\n  MapPin,\n  Clock,\n  Star,\n  ChevronRight,\n  Smartphone,\n  Play,\n  Upload,\n  Download,\n  Share2\n} from 'lucide-react';\nimport { MobileCameraCapture } from '@/components/Camera/MobileCameraCapture';\nimport { MobileVRViewer } from '@/components/VR/MobileVRViewer';\nimport { PhotoUploader } from '@/components/Upload/PhotoUploader';\nimport { PanoramicGallery } from '@/components/Gallery/PanoramicGallery';\nimport HouseVRTour, { House as VRHouse, Room } from '@/components/VR/HouseVRTour';\nimport HouseManager from '@/components/VR/HouseManager';\nimport MobileOptimizedVR from '@/components/VR/MobileOptimizedVR';\n\n\ninterface House {\n  id: string;\n  name: string;\n  address: string;\n  rooms: number;\n  photos: number;\n  lastUpdated: string;\n  thumbnail?: string;\n  panoramaUrl?: string;\n  hasVRTour?: boolean;\n}\n\n\n\nexport default function MobileEnglishPage() {\n  const [showCamera, setShowCamera] = useState(false);\n  const [showVRTour, setShowVRTour] = useState(false);\n  const [showUploader, setShowUploader] = useState(false);\n  const [showGallery, setShowGallery] = useState(false);\n  const [selectedPanorama, setSelectedPanorama] = useState<string>('');\n  const [showHouseVRTour, setShowHouseVRTour] = useState(false);\n  const [showHouseManager, setShowHouseManager] = useState(false);\n  const [selectedVRHouse, setSelectedVRHouse] = useState<VRHouse | null>(null);\n  const [showMobileOptimizedVR, setShowMobileOptimizedVR] = useState(false);\n  const [mobileVRConfig, setMobileVRConfig] = useState<{\n    panoramaUrl: string;\n    roomName: string;\n    houseName: string;\n    is720Mode: boolean;\n  } | null>(null);\n  const [vrHouses, setVRHouses] = useState<VRHouse[]>([\n    {\n      id: 'demo-house-1',\n      name: '现代豪华别墅',\n      address: '北京市朝阳区CBD核心区',\n      description: '精装修现代别墅，配备智能家居系统',\n      startRoomId: 'living-room-1',\n      rooms: [\n        {\n          id: 'living-room-1',\n          name: '客厅',\n          type: 'living_room',\n          panoramaUrl: '/panoramas/living-room-modern-1.jpg',\n          description: '宽敞明亮的现代客厅，配备大型落地窗',\n          area: 45,\n          hotSpots: [\n            {\n              id: 'hotspot-1',\n              x: 0.3,\n              y: 0.6,\n              targetRoomId: 'kitchen-1',\n              title: '前往厨房',\n              description: '通往开放式厨房'\n            },\n            {\n              id: 'hotspot-2',\n              x: 0.7,\n              y: 0.5,\n              targetRoomId: 'bedroom-1',\n              title: '前往主卧',\n              description: '通往主卧室'\n            }\n          ]\n        },\n        {\n          id: 'kitchen-1',\n          name: '厨房',\n          type: 'kitchen',\n          panoramaUrl: '/panoramas/kitchen-modern-1.jpg',\n          description: '现代化开放式厨房，配备高端厨具',\n          area: 25,\n          hotSpots: [\n            {\n              id: 'hotspot-3',\n              x: 0.5,\n              y: 0.7,\n              targetRoomId: 'living-room-1',\n              title: '返回客厅',\n              description: '返回客厅区域'\n            },\n            {\n              id: 'hotspot-4',\n              x: 0.8,\n              y: 0.4,\n              targetRoomId: 'dining-room-1',\n              title: '前往餐厅',\n              description: '通往餐厅区域'\n            }\n          ]\n        },\n        {\n          id: 'dining-room-1',\n          name: '餐厅',\n          type: 'dining_room',\n          panoramaUrl: '/panoramas/living-room-luxury-2.jpg',\n          description: '优雅的餐厅空间，适合家庭聚餐',\n          area: 20,\n          hotSpots: [\n            {\n              id: 'hotspot-5',\n              x: 0.2,\n              y: 0.6,\n              targetRoomId: 'kitchen-1',\n              title: '返回厨房',\n              description: '返回厨房区域'\n            }\n          ]\n        },\n        {\n          id: 'bedroom-1',\n          name: '主卧室',\n          type: 'bedroom',\n          panoramaUrl: '/panoramas/bedroom-master-1.jpg',\n          description: '宽敞的主卧室，配备独立卫浴',\n          area: 35,\n          hotSpots: [\n            {\n              id: 'hotspot-6',\n              x: 0.4,\n              y: 0.8,\n              targetRoomId: 'living-room-1',\n              title: '返回客厅',\n              description: '返回客厅区域'\n            },\n            {\n              id: 'hotspot-7',\n              x: 0.9,\n              y: 0.3,\n              targetRoomId: 'bathroom-1',\n              title: '前往主卫',\n              description: '通往主卧卫生间'\n            }\n          ]\n        },\n        {\n          id: 'bathroom-1',\n          name: '主卫生间',\n          type: 'bathroom',\n          panoramaUrl: '/panoramas/bathroom-master-1.jpg',\n          description: '豪华主卫，配备浴缸和独立淋浴',\n          area: 15,\n          hotSpots: [\n            {\n              id: 'hotspot-8',\n              x: 0.1,\n              y: 0.7,\n              targetRoomId: 'bedroom-1',\n              title: '返回主卧',\n              description: '返回主卧室'\n            }\n          ]\n        }\n      ]\n    }\n  ]);\n  const [houses, setHouses] = useState<House[]>([\n    {\n      id: '1',\n      name: 'Modern Living Room',\n      address: 'Beverly Hills, CA',\n      rooms: 5,\n      photos: 12,\n      lastUpdated: '2 hours ago',\n      thumbnail: '/thumbnails/living-room-modern-1-thumb.jpg',\n      panoramaUrl: '/panoramas/living-room-modern-1.jpg',\n      hasVRTour: true\n    },\n    {\n      id: '2',\n      name: 'Luxury Living Room',\n      address: 'Manhattan, NY',\n      rooms: 4,\n      photos: 10,\n      lastUpdated: '4 hours ago',\n      thumbnail: '/thumbnails/living-room-luxury-2-thumb.jpg',\n      panoramaUrl: '/panoramas/living-room-luxury-2.jpg',\n      hasVRTour: true\n    },\n    {\n      id: '3',\n      name: 'Master Bedroom Suite',\n      address: 'San Francisco, CA',\n      rooms: 3,\n      photos: 8,\n      lastUpdated: '1 day ago',\n      thumbnail: '/thumbnails/bedroom-master-1-thumb.jpg',\n      panoramaUrl: '/panoramas/bedroom-master-1.jpg',\n      hasVRTour: true\n    },\n    {\n      id: '4',\n      name: 'Guest Bedroom',\n      address: 'Los Angeles, CA',\n      rooms: 2,\n      photos: 6,\n      lastUpdated: '1 day ago',\n      thumbnail: '/thumbnails/bedroom-guest-2-thumb.jpg',\n      panoramaUrl: '/panoramas/bedroom-guest-2.jpg',\n      hasVRTour: true\n    },\n    {\n      id: '5',\n      name: 'Contemporary Kitchen',\n      address: 'Seattle, WA',\n      rooms: 4,\n      photos: 15,\n      lastUpdated: '2 days ago',\n      thumbnail: '/thumbnails/kitchen-modern-1-thumb.jpg',\n      panoramaUrl: '/panoramas/kitchen-modern-1.jpg',\n      hasVRTour: true\n    },\n    {\n      id: '6',\n      name: 'Open Concept Kitchen',\n      address: 'Portland, OR',\n      rooms: 3,\n      photos: 12,\n      lastUpdated: '2 days ago',\n      thumbnail: '/thumbnails/kitchen-open-2-thumb.jpg',\n      panoramaUrl: '/panoramas/kitchen-open-2.jpg',\n      hasVRTour: true\n    },\n    {\n      id: '7',\n      name: 'Master Bathroom',\n      address: 'Miami Beach, FL',\n      rooms: 6,\n      photos: 20,\n      lastUpdated: '3 days ago',\n      thumbnail: '/thumbnails/bathroom-master-1-thumb.jpg',\n      panoramaUrl: '/panoramas/bathroom-master-1.jpg',\n      hasVRTour: true\n    },\n    {\n      id: '8',\n      name: 'Guest Bathroom',\n      address: 'Austin, TX',\n      rooms: 4,\n      photos: 8,\n      lastUpdated: '3 days ago',\n      thumbnail: '/thumbnails/bathroom-guest-2-thumb.jpg',\n      panoramaUrl: '/panoramas/bathroom-guest-2.jpg',\n      hasVRTour: true\n    }\n  ]);\n\n  const [activeTab, setActiveTab] = useState<'houses' | 'camera' | 'view' | 'settings'>('houses');\n\n  // Loading states\n  const [isLoading, setIsLoading] = useState(false);\n  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());\n\n  // Detect mobile device\n  const [isMobile, setIsMobile] = useState(false);\n\n  useEffect(() => {\n    const checkMobile = () => {\n      const userAgent = navigator.userAgent;\n      const mobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);\n      setIsMobile(mobile);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Handle photo capture\n  const handlePhotoCapture = (photoDataUrl: string) => {\n    console.log('Photo captured:', photoDataUrl.substring(0, 50) + '...');\n    setShowCamera(false);\n\n    // Add new house with captured photo\n    const newHouse: House = {\n      id: Date.now().toString(),\n      name: 'New Property',\n      address: 'Location TBD',\n      rooms: 1,\n      photos: 1,\n      lastUpdated: 'Just now',\n      thumbnail: photoDataUrl,\n      panoramaUrl: photoDataUrl, // Use captured photo as panorama\n      hasVRTour: true\n    };\n\n    setHouses(prev => [newHouse, ...prev]);\n  };\n\n  // Handle photo upload\n  const handlePhotosUploaded = (photoUrls: string[]) => {\n    console.log('Photos uploaded:', photoUrls.length);\n    // Don't close uploader yet, wait for panorama creation\n  };\n\n  // Handle panorama creation\n  const handlePanoramaCreated = (panoramaUrl: string) => {\n    console.log('Panorama created:', panoramaUrl.substring(0, 50) + '...');\n    setShowUploader(false);\n\n    // Create new house with the created panorama\n    const newHouse: House = {\n      id: Date.now().toString(),\n      name: 'New 360° Property',\n      address: 'Location TBD',\n      rooms: Math.ceil(Math.random() * 5) + 1, // Random room count\n      photos: 1, // The panorama counts as 1 photo\n      lastUpdated: 'Just now',\n      thumbnail: panoramaUrl, // Use panorama as thumbnail\n      panoramaUrl: panoramaUrl, // The stitched panorama\n      hasVRTour: true\n    };\n\n    setHouses(prev => [newHouse, ...prev]);\n  };\n\n  // 检测设备类型\n  const isMobileDevice = () => {\n    return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n  };\n\n  // Start VR Tour with loading state\n  const startVRTour = (panoramaUrl: string) => {\n    if (isLoading) return; // Prevent multiple clicks\n\n    setIsLoading(true);\n    setSelectedPanorama(panoramaUrl);\n\n    // 检测是否为移动设备，使用优化的VR组件\n    if (isMobileDevice()) {\n      setMobileVRConfig({\n        panoramaUrl,\n        roomName: 'VR Tour',\n        houseName: 'Property View',\n        is720Mode: true\n      });\n      setTimeout(() => {\n        setShowMobileOptimizedVR(true);\n        setIsLoading(false);\n      }, 300);\n    } else {\n      // 桌面端使用原有组件\n      setTimeout(() => {\n        setShowVRTour(true);\n        setIsLoading(false);\n      }, 300);\n    }\n  };\n\n  // Start House VR Tour\n  const startHouseVRTour = (vrHouse: VRHouse) => {\n    if (isLoading) return;\n\n    setIsLoading(true);\n    setSelectedVRHouse(vrHouse);\n\n    // 移动设备使用优化版本，桌面设备使用完整版本\n    if (isMobileDevice()) {\n      // 移动端使用优化的单房间VR，从起始房间开始\n      const startRoom = vrHouse.rooms.find(room => room.id === vrHouse.startRoomId) || vrHouse.rooms[0];\n      if (startRoom) {\n        setMobileVRConfig({\n          panoramaUrl: startRoom.panoramaUrl,\n          roomName: startRoom.name,\n          houseName: vrHouse.name,\n          is720Mode: true\n        });\n        setTimeout(() => {\n          setShowMobileOptimizedVR(true);\n          setIsLoading(false);\n        }, 300);\n      }\n    } else {\n      // 桌面端使用完整的全屋漫游\n      setTimeout(() => {\n        setShowHouseVRTour(true);\n        setIsLoading(false);\n      }, 300);\n    }\n  };\n\n  // Handle house creation/update\n  const handleHouseCreated = (vrHouse: VRHouse) => {\n    setVRHouses(prev => {\n      const existingIndex = prev.findIndex(h => h.id === vrHouse.id);\n      if (existingIndex >= 0) {\n        // Update existing house\n        const updated = [...prev];\n        updated[existingIndex] = vrHouse;\n        return updated;\n      } else {\n        // Add new house\n        return [vrHouse, ...prev];\n      }\n    });\n  };\n\n  // Quick camera button\n  const QuickCameraButton = () => (\n    <button\n      onClick={() => setShowCamera(true)}\n      className=\"fixed bottom-20 right-4 w-16 h-16 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center z-40 transition-all duration-200 active:scale-95\"\n    >\n      <Camera size={24} />\n    </button>\n  );\n\n  // House card component\n  const HouseCard = ({ house }: { house: House }) => (\n    <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n      {/* Optimized Thumbnail with lazy loading */}\n      <div className=\"h-32 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center relative overflow-hidden\">\n        {house.thumbnail ? (\n          <img\n            src={house.thumbnail}\n            alt={house.name}\n            className={`w-full h-full object-cover transition-opacity duration-300 ${\n              loadingImages.has(house.id) ? 'opacity-0' : 'opacity-100'\n            }`}\n            loading=\"lazy\"\n            decoding=\"async\"\n            onLoad={() => {\n              setLoadingImages(prev => {\n                const newSet = new Set(prev);\n                newSet.delete(house.id);\n                return newSet;\n              });\n            }}\n            onError={() => {\n              setLoadingImages(prev => {\n                const newSet = new Set(prev);\n                newSet.delete(house.id);\n                return newSet;\n              });\n            }}\n          />\n        ) : (\n          <Home size={32} className=\"text-blue-500\" />\n        )}\n\n        {/* Loading indicator */}\n        {loadingImages.has(house.id) && (\n          <div className=\"absolute inset-0 bg-blue-100/50 flex items-center justify-center\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n          </div>\n        )}\n\n        {/* VR Tour Badge */}\n        {house.hasVRTour && (\n          <div className=\"absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center z-10\">\n            <Eye size={12} className=\"mr-1\" />\n            VR Tour\n          </div>\n        )}\n      </div>\n      \n      {/* House info */}\n      <div className=\"p-4\">\n        <div className=\"flex items-start justify-between mb-2\">\n          <h3 className=\"font-medium text-gray-900 text-lg\">{house.name}</h3>\n          <ChevronRight size={20} className=\"text-gray-400 mt-1\" />\n        </div>\n        \n        <div className=\"flex items-center text-gray-600 text-sm mb-2\">\n          <MapPin size={14} className=\"mr-1\" />\n          <span>{house.address}</span>\n        </div>\n        \n        <div className=\"flex items-center justify-between text-sm mb-3\">\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"flex items-center text-gray-600\">\n              <Grid size={14} className=\"mr-1\" />\n              {house.rooms} rooms\n            </span>\n            <span className=\"flex items-center text-gray-600\">\n              <Camera size={14} className=\"mr-1\" />\n              {house.photos} photos\n            </span>\n          </div>\n          <div className=\"flex items-center text-gray-500\">\n            <Clock size={14} className=\"mr-1\" />\n            <span>{house.lastUpdated}</span>\n          </div>\n        </div>\n\n        {/* Action buttons */}\n        <div className=\"flex space-x-2\">\n          {house.hasVRTour && house.panoramaUrl && (\n            <button\n              onClick={() => startVRTour(house.panoramaUrl!)}\n              disabled={isLoading}\n              className={`flex-1 py-2 px-3 rounded text-sm flex items-center justify-center transition-all duration-200 ${\n                isLoading\n                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'\n                  : 'bg-blue-600 hover:bg-blue-700 text-white active:scale-95'\n              }`}\n            >\n              {isLoading ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Loading...\n                </>\n              ) : (\n                <>\n                  <Play size={14} className=\"mr-1\" />\n                  VR Tour\n                </>\n              )}\n            </button>\n          )}\n          <button\n            onClick={() => setShowUploader(true)}\n            disabled={isLoading}\n            className={`flex-1 py-2 px-3 rounded text-sm flex items-center justify-center transition-all duration-200 ${\n              isLoading\n                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 active:scale-95'\n            }`}\n          >\n            <Upload size={14} className=\"mr-1\" />\n            Upload\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n\n  // Bottom navigation\n  const BottomNavigation = () => (\n    <div className=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-30\">\n      <div className=\"flex items-center justify-around\">\n        <button\n          onClick={() => setActiveTab('houses')}\n          className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${\n            activeTab === 'houses' \n              ? 'text-blue-600 bg-blue-50' \n              : 'text-gray-600 hover:text-gray-900'\n          }`}\n        >\n          <Home size={20} />\n          <span className=\"text-xs mt-1\">Properties</span>\n        </button>\n        \n        <button\n          onClick={() => setActiveTab('camera')}\n          className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${\n            activeTab === 'camera' \n              ? 'text-blue-600 bg-blue-50' \n              : 'text-gray-600 hover:text-gray-900'\n          }`}\n        >\n          <Camera size={20} />\n          <span className=\"text-xs mt-1\">Capture</span>\n        </button>\n        \n        <button\n          onClick={() => setActiveTab('view')}\n          className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${\n            activeTab === 'view' \n              ? 'text-blue-600 bg-blue-50' \n              : 'text-gray-600 hover:text-gray-900'\n          }`}\n        >\n          <Eye size={20} />\n          <span className=\"text-xs mt-1\">VR Tours</span>\n        </button>\n        \n        <button\n          onClick={() => setActiveTab('settings')}\n          className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${\n            activeTab === 'settings' \n              ? 'text-blue-600 bg-blue-50' \n              : 'text-gray-600 hover:text-gray-900'\n          }`}\n        >\n          <Settings size={20} />\n          <span className=\"text-xs mt-1\">Settings</span>\n        </button>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 relative\">\n      {/* Global Loading Overlay */}\n      {isLoading && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4\"></div>\n            <p className=\"text-gray-700 font-medium\">Loading VR Tour...</p>\n            <p className=\"text-gray-500 text-sm mt-1\">Please wait</p>\n          </div>\n        </div>\n      )}\n\n      {/* Top status bar */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200 px-4 py-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3\">\n              <Smartphone size={20} className=\"text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-lg font-semibold text-gray-900\">VR720</h1>\n              <p className=\"text-xs text-gray-500\">Real Estate VR Capture</p>\n            </div>\n          </div>\n          \n          {isMobile && (\n            <div className=\"flex items-center text-green-600 text-sm\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full mr-2\"></div>\n              Mobile Device\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Main content area */}\n      <div className=\"pb-20 px-4 py-6\">\n        {activeTab === 'houses' && (\n          <div>\n            {/* Quick actions */}\n            <div className=\"mb-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-3\">Quick Actions</h2>\n              <div className=\"grid grid-cols-2 gap-3\">\n                <button\n                  onClick={() => setShowCamera(true)}\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg flex items-center justify-center transition-colors\"\n                >\n                  <Camera size={20} className=\"mr-2\" />\n                  Capture Now\n                </button>\n                <button\n                  onClick={() => setShowHouseManager(true)}\n                  className=\"bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg flex items-center justify-center transition-colors\"\n                >\n                  <Plus size={20} className=\"mr-2\" />\n                  New House Tour\n                </button>\n              </div>\n            </div>\n\n            {/* VR Houses Section */}\n            {vrHouses.length > 0 && (\n              <div className=\"mb-6\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <h2 className=\"text-lg font-medium text-gray-900\">全屋漫游</h2>\n                  <span className=\"text-sm text-gray-500\">{vrHouses.length} 个项目</span>\n                </div>\n\n                <div className=\"space-y-3\">\n                  {vrHouses.map((vrHouse) => (\n                    <div key={vrHouse.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <div>\n                          <h3 className=\"font-medium text-gray-900\">{vrHouse.name}</h3>\n                          <p className=\"text-sm text-gray-600\">{vrHouse.address}</p>\n                        </div>\n                        <div className=\"text-right\">\n                          <span className=\"text-sm text-gray-500\">{vrHouse.rooms.length} 个房间</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex space-x-2\">\n                        <button\n                          onClick={() => startHouseVRTour(vrHouse)}\n                          disabled={isLoading}\n                          className={`flex-1 py-2 px-3 rounded text-sm flex items-center justify-center transition-all duration-200 ${\n                            isLoading\n                              ? 'bg-gray-400 text-gray-200 cursor-not-allowed'\n                              : 'bg-purple-600 hover:bg-purple-700 text-white active:scale-95'\n                          }`}\n                        >\n                          <Eye size={14} className=\"mr-1\" />\n                          全屋漫游\n                        </button>\n                        <button\n                          onClick={() => {\n                            setSelectedVRHouse(vrHouse);\n                            setShowHouseManager(true);\n                          }}\n                          disabled={isLoading}\n                          className={`flex-1 py-2 px-3 rounded text-sm flex items-center justify-center transition-all duration-200 ${\n                            isLoading\n                              ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\n                              : 'bg-gray-100 hover:bg-gray-200 text-gray-700 active:scale-95'\n                          }`}\n                        >\n                          <Settings size={14} className=\"mr-1\" />\n                          编辑\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Properties list */}\n            <div>\n              <div className=\"flex items-center justify-between mb-3\">\n                <h2 className=\"text-lg font-medium text-gray-900\">My Properties</h2>\n                <span className=\"text-sm text-gray-500\">{houses.length} projects</span>\n              </div>\n              \n              <div className=\"space-y-4\">\n                {houses.map((house) => (\n                  <HouseCard key={house.id} house={house} />\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'camera' && (\n          <div className=\"text-center py-12\">\n            <Camera size={64} className=\"mx-auto text-gray-400 mb-4\" />\n            <h3 className=\"text-xl font-medium text-gray-900 mb-2\">Camera Function</h3>\n            <p className=\"text-gray-600 mb-6\">Capture high-quality real estate photos</p>\n            <button\n              onClick={() => setShowCamera(true)}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg\"\n            >\n              Start Capture\n            </button>\n          </div>\n        )}\n\n        {activeTab === 'view' && (\n          <div>\n            <h2 className=\"text-lg font-medium text-gray-900 mb-4\">VR Tours Available</h2>\n\n            {/* Create House Tour Button */}\n            <div className=\"mb-6\">\n              <button\n                onClick={() => setShowHouseManager(true)}\n                className=\"w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-3 px-4 rounded-lg flex items-center justify-center mb-3\"\n              >\n                <Plus size={20} className=\"mr-2\" />\n                Create House Tour\n              </button>\n              <button\n                onClick={() => setShowGallery(true)}\n                className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-3 px-4 rounded-lg flex items-center justify-center\"\n              >\n                <Grid size={20} className=\"mr-2\" />\n                Browse Panoramic Gallery\n              </button>\n              <p className=\"text-xs text-gray-500 text-center mt-2\">\n                Create multi-room VR tours or explore 360° panoramic images\n              </p>\n            </div>\n\n            {/* House VR Tours */}\n            {vrHouses.length > 0 && (\n              <div className=\"mb-6\">\n                <h3 className=\"text-md font-medium text-gray-900 mb-3\">全屋漫游项目</h3>\n                <div className=\"space-y-3\">\n                  {vrHouses.map((vrHouse) => (\n                    <div key={vrHouse.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{vrHouse.name}</h4>\n                          <p className=\"text-sm text-gray-600\">{vrHouse.address}</p>\n                          <p className=\"text-xs text-gray-500\">{vrHouse.rooms.length} 个房间</p>\n                        </div>\n                        <div className=\"flex space-x-2\">\n                          <button\n                            onClick={() => startHouseVRTour(vrHouse)}\n                            className=\"bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-lg flex items-center text-sm\"\n                          >\n                            <Eye size={14} className=\"mr-1\" />\n                            漫游\n                          </button>\n                          <button\n                            onClick={() => {\n                              setSelectedVRHouse(vrHouse);\n                              setShowHouseManager(true);\n                            }}\n                            className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg flex items-center text-sm\"\n                          >\n                            <Settings size={14} className=\"mr-1\" />\n                            编辑\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Single Room Tours */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 mb-3\">单房间VR</h3>\n              <div className=\"space-y-4\">\n                {houses.filter(house => house.hasVRTour).map((house) => (\n                  <div key={house.id} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h4 className=\"font-medium text-gray-900\">{house.name}</h4>\n                        <p className=\"text-sm text-gray-600\">{house.address}</p>\n                      </div>\n                      <button\n                        onClick={() => startVRTour(house.panoramaUrl!)}\n                        className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center\"\n                      >\n                        <Play size={16} className=\"mr-1\" />\n                        View Tour\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'settings' && (\n          <div className=\"space-y-4\">\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\">\n              <h3 className=\"font-medium text-gray-900 mb-3\">App Settings</h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-700\">Camera Quality</span>\n                  <span className=\"text-blue-600\">High Quality</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-700\">Auto Save</span>\n                  <span className=\"text-green-600\">Enabled</span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-700\">Cloud Sync</span>\n                  <span className=\"text-gray-500\">Not Configured</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Quick capture button */}\n      {activeTab !== 'camera' && <QuickCameraButton />}\n\n      {/* Bottom navigation */}\n      <BottomNavigation />\n\n      {/* Camera component */}\n      {showCamera && (\n        <MobileCameraCapture\n          onPhotoCapture={handlePhotoCapture}\n          onClose={() => setShowCamera(false)}\n        />\n      )}\n\n      {/* VR Tour component */}\n      {showVRTour && selectedPanorama && (\n        <MobileVRViewer\n          panoramaUrl={selectedPanorama}\n          title=\"Property VR Tour\"\n          description=\"Mobile-optimized 720° VR experience\"\n          onClose={() => setShowVRTour(false)}\n        />\n      )}\n\n      {/* Photo Uploader component */}\n      {showUploader && (\n        <PhotoUploader\n          onPhotosUploaded={handlePhotosUploaded}\n          onPanoramaCreated={handlePanoramaCreated}\n          onClose={() => setShowUploader(false)}\n          maxPhotos={10}\n        />\n      )}\n\n      {/* Panoramic Gallery component */}\n      {showGallery && (\n        <PanoramicGallery\n          onClose={() => setShowGallery(false)}\n          onImageSelect={(panoramaUrl, title) => {\n            // Create a new house with the selected panoramic image\n            const newHouse: House = {\n              id: Date.now().toString(),\n              name: title,\n              address: 'Gallery Selection',\n              rooms: Math.ceil(Math.random() * 5) + 1,\n              photos: 1,\n              lastUpdated: 'Just now',\n              thumbnail: panoramaUrl,\n              panoramaUrl: panoramaUrl,\n              hasVRTour: true\n            };\n            setHouses(prev => [newHouse, ...prev]);\n          }}\n        />\n      )}\n\n      {/* House VR Tour component */}\n      {showHouseVRTour && selectedVRHouse && (\n        <HouseVRTour\n          house={selectedVRHouse}\n          onClose={() => {\n            setShowHouseVRTour(false);\n            setSelectedVRHouse(null);\n          }}\n        />\n      )}\n\n      {/* House Manager component */}\n      {showHouseManager && (\n        <HouseManager\n          onClose={() => {\n            setShowHouseManager(false);\n            setSelectedVRHouse(null);\n          }}\n          onHouseCreated={handleHouseCreated}\n          existingHouse={selectedVRHouse || undefined}\n        />\n      )}\n\n      {/* Mobile Optimized VR component */}\n      {showMobileOptimizedVR && mobileVRConfig && (\n        <MobileOptimizedVR\n          panoramaUrl={mobileVRConfig.panoramaUrl}\n          roomName={mobileVRConfig.roomName}\n          houseName={mobileVRConfig.houseName}\n          is720Mode={mobileVRConfig.is720Mode}\n          onClose={() => {\n            setShowMobileOptimizedVR(false);\n            setMobileVRConfig(null);\n          }}\n          onToggleMode={() => {\n            setMobileVRConfig(prev => prev ? {\n              ...prev,\n              is720Mode: !prev.is720Mode\n            } : null);\n          }}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA;;;;;;;;;;;AA2Ce,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAKzC;IACV,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,aAAa;YACb,aAAa;YACb,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,MAAM;oBACN,UAAU;wBACR;4BACE,IAAI;4BACJ,GAAG;4BACH,GAAG;4BACH,cAAc;4BACd,OAAO;4BACP,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,GAAG;4BACH,GAAG;4BACH,cAAc;4BACd,OAAO;4BACP,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,MAAM;oBACN,UAAU;wBACR;4BACE,IAAI;4BACJ,GAAG;4BACH,GAAG;4BACH,cAAc;4BACd,OAAO;4BACP,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,GAAG;4BACH,GAAG;4BACH,cAAc;4BACd,OAAO;4BACP,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,MAAM;oBACN,UAAU;wBACR;4BACE,IAAI;4BACJ,GAAG;4BACH,GAAG;4BACH,cAAc;4BACd,OAAO;4BACP,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,MAAM;oBACN,UAAU;wBACR;4BACE,IAAI;4BACJ,GAAG;4BACH,GAAG;4BACH,cAAc;4BACd,OAAO;4BACP,aAAa;wBACf;wBACA;4BACE,IAAI;4BACJ,GAAG;4BACH,GAAG;4BACH,cAAc;4BACd,OAAO;4BACP,aAAa;wBACf;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,aAAa;oBACb,MAAM;oBACN,UAAU;wBACR;4BACE,IAAI;4BACJ,GAAG;4BACH,GAAG;4BACH,cAAc;4BACd,OAAO;4BACP,aAAa;wBACf;qBACD;gBACH;aACD;QACH;KACD;IACD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;QAC5C;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAEtF,iBAAiB;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAEpE,uBAAuB;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,MAAM,YAAY,UAAU,SAAS;YACrC,MAAM,SAAS,2DAA2D,IAAI,CAAC;YAC/E,YAAY;QACd;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,mBAAmB,aAAa,SAAS,CAAC,GAAG,MAAM;QAC/D,cAAc;QAEd,oCAAoC;QACpC,MAAM,WAAkB;YACtB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QAEA,UAAU,CAAA,OAAQ;gBAAC;mBAAa;aAAK;IACvC;IAEA,sBAAsB;IACtB,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,oBAAoB,UAAU,MAAM;IAChD,uDAAuD;IACzD;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB,CAAC;QAC7B,QAAQ,GAAG,CAAC,qBAAqB,YAAY,SAAS,CAAC,GAAG,MAAM;QAChE,gBAAgB;QAEhB,6CAA6C;QAC7C,MAAM,WAAkB;YACtB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,OAAO,KAAK,IAAI,CAAC,KAAK,MAAM,KAAK,KAAK;YACtC,QAAQ;YACR,aAAa;YACb,WAAW;YACX,aAAa;YACb,WAAW;QACb;QAEA,UAAU,CAAA,OAAQ;gBAAC;mBAAa;aAAK;IACvC;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,OAAO,2DAA2D,IAAI,CAAC,UAAU,SAAS;IAC5F;IAEA,mCAAmC;IACnC,MAAM,cAAc,CAAC;QACnB,IAAI,WAAW,QAAQ,0BAA0B;QAEjD,aAAa;QACb,oBAAoB;QAEpB,sBAAsB;QACtB,IAAI,kBAAkB;YACpB,kBAAkB;gBAChB;gBACA,UAAU;gBACV,WAAW;gBACX,WAAW;YACb;YACA,WAAW;gBACT,yBAAyB;gBACzB,aAAa;YACf,GAAG;QACL,OAAO;YACL,YAAY;YACZ,WAAW;gBACT,cAAc;gBACd,aAAa;YACf,GAAG;QACL;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW;QAEf,aAAa;QACb,mBAAmB;QAEnB,wBAAwB;QACxB,IAAI,kBAAkB;YACpB,wBAAwB;YACxB,MAAM,YAAY,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,WAAW,KAAK,QAAQ,KAAK,CAAC,EAAE;YACjG,IAAI,WAAW;gBACb,kBAAkB;oBAChB,aAAa,UAAU,WAAW;oBAClC,UAAU,UAAU,IAAI;oBACxB,WAAW,QAAQ,IAAI;oBACvB,WAAW;gBACb;gBACA,WAAW;oBACT,yBAAyB;oBACzB,aAAa;gBACf,GAAG;YACL;QACF,OAAO;YACL,eAAe;YACf,WAAW;gBACT,mBAAmB;gBACnB,aAAa;YACf,GAAG;QACL;IACF;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,CAAC;QAC1B,YAAY,CAAA;YACV,MAAM,gBAAgB,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE;YAC7D,IAAI,iBAAiB,GAAG;gBACtB,wBAAwB;gBACxB,MAAM,UAAU;uBAAI;iBAAK;gBACzB,OAAO,CAAC,cAAc,GAAG;gBACzB,OAAO;YACT,OAAO;gBACL,gBAAgB;gBAChB,OAAO;oBAAC;uBAAY;iBAAK;YAC3B;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,kBACxB,8OAAC;YACC,SAAS,IAAM,cAAc;YAC7B,WAAU;sBAEV,cAAA,8OAAC,uMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;;;;;;IAIlB,uBAAuB;IACvB,MAAM,YAAY,CAAC,EAAE,KAAK,EAAoB,iBAC5C,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;wBACZ,MAAM,SAAS,iBACd,8OAAC;4BACC,KAAK,MAAM,SAAS;4BACpB,KAAK,MAAM,IAAI;4BACf,WAAW,CAAC,2DAA2D,EACrE,cAAc,GAAG,CAAC,MAAM,EAAE,IAAI,cAAc,eAC5C;4BACF,SAAQ;4BACR,UAAS;4BACT,QAAQ;gCACN,iBAAiB,CAAA;oCACf,MAAM,SAAS,IAAI,IAAI;oCACvB,OAAO,MAAM,CAAC,MAAM,EAAE;oCACtB,OAAO;gCACT;4BACF;4BACA,SAAS;gCACP,iBAAiB,CAAA;oCACf,MAAM,SAAS,IAAI,IAAI;oCACvB,OAAO,MAAM,CAAC,MAAM,EAAE;oCACtB,OAAO;gCACT;4BACF;;;;;iDAGF,8OAAC,mMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAI3B,cAAc,GAAG,CAAC,MAAM,EAAE,mBACzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;wBAKlB,MAAM,SAAS,kBACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iMAAA,CAAA,MAAG;oCAAC,MAAM;oCAAI,WAAU;;;;;;gCAAS;;;;;;;;;;;;;8BAOxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC,MAAM,IAAI;;;;;;8CAC7D,8OAAC,uNAAA,CAAA,eAAY;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;;sCAGpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2MAAA,CAAA,SAAM;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC5B,8OAAC;8CAAM,MAAM,OAAO;;;;;;;;;;;;sCAGtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,mMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDACzB,MAAM,KAAK;gDAAC;;;;;;;sDAEf,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,uMAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;gDAC3B,MAAM,MAAM;gDAAC;;;;;;;;;;;;;8CAGlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,qMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC3B,8OAAC;sDAAM,MAAM,WAAW;;;;;;;;;;;;;;;;;;sCAK5B,8OAAC;4BAAI,WAAU;;gCACZ,MAAM,SAAS,IAAI,MAAM,WAAW,kBACnC,8OAAC;oCACC,SAAS,IAAM,YAAY,MAAM,WAAW;oCAC5C,UAAU;oCACV,WAAW,CAAC,8FAA8F,EACxG,YACI,iDACA,4DACJ;8CAED,0BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAAuE;;qEAIxF;;0DACE,8OAAC,mMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;;8CAM3C,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,UAAU;oCACV,WAAW,CAAC,8FAA8F,EACxG,YACI,iDACA,+DACJ;;sDAEF,8OAAC,uMAAA,CAAA,SAAM;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;;IAQ/C,oBAAoB;IACpB,MAAM,mBAAmB,kBACvB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,WACV,6BACA,qCACJ;;0CAEF,8OAAC,mMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CACZ,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAGjC,8OAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,WACV,6BACA,qCACJ;;0CAEF,8OAAC,uMAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;0CACd,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAGjC,8OAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,SACV,6BACA,qCACJ;;0CAEF,8OAAC,iMAAA,CAAA,MAAG;gCAAC,MAAM;;;;;;0CACX,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAGjC,8OAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,aACV,6BACA,qCACJ;;0CAEF,8OAAC,2MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;IAMvC,qBACE,8OAAC;QAAI,WAAU;;YAEZ,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;sCACzC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;0BAMhD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,+MAAA,CAAA,aAAU;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAElC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;wBAIxC,0BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;gCAA+C;;;;;;;;;;;;;;;;;;0BAQtE,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,0BACb,8OAAC;;0CAEC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,cAAc;gDAC7B,WAAU;;kEAEV,8OAAC,uMAAA,CAAA,SAAM;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAAS;;;;;;;0DAGvC,8OAAC;gDACC,SAAS,IAAM,oBAAoB;gDACnC,WAAU;;kEAEV,8OAAC,mMAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;4BAOxC,SAAS,MAAM,GAAG,mBACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAK,WAAU;;oDAAyB,SAAS,MAAM;oDAAC;;;;;;;;;;;;;kDAG3D,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA6B,QAAQ,IAAI;;;;;;kFACvD,8OAAC;wEAAE,WAAU;kFAAyB,QAAQ,OAAO;;;;;;;;;;;;0EAEvD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;;wEAAyB,QAAQ,KAAK,CAAC,MAAM;wEAAC;;;;;;;;;;;;;;;;;;kEAIlE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,iBAAiB;gEAChC,UAAU;gEACV,WAAW,CAAC,8FAA8F,EACxG,YACI,iDACA,gEACJ;;kFAEF,8OAAC,iMAAA,CAAA,MAAG;wEAAC,MAAM;wEAAI,WAAU;;;;;;oEAAS;;;;;;;0EAGpC,8OAAC;gEACC,SAAS;oEACP,mBAAmB;oEACnB,oBAAoB;gEACtB;gEACA,UAAU;gEACV,WAAW,CAAC,8FAA8F,EACxG,YACI,iDACA,+DACJ;;kFAEF,8OAAC,2MAAA,CAAA,WAAQ;wEAAC,MAAM;wEAAI,WAAU;;;;;;oEAAS;;;;;;;;;;;;;;+CApCnC,QAAQ,EAAE;;;;;;;;;;;;;;;;0CA+C5B,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAK,WAAU;;oDAAyB,OAAO,MAAM;oDAAC;;;;;;;;;;;;;kDAGzD,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gDAAyB,OAAO;+CAAjB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAOjC,cAAc,0BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uMAAA,CAAA,SAAM;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC5B,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCACC,SAAS,IAAM,cAAc;gCAC7B,WAAU;0CACX;;;;;;;;;;;;oBAMJ,cAAc,wBACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;;0DAEV,8OAAC,mMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;kDAGrC,8OAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,8OAAC,mMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;kDAGrC,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;4BAMvD,SAAS,MAAM,GAAG,mBACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAAqB,WAAU;0DAC9B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA6B,QAAQ,IAAI;;;;;;8EACvD,8OAAC;oEAAE,WAAU;8EAAyB,QAAQ,OAAO;;;;;;8EACrD,8OAAC;oEAAE,WAAU;;wEAAyB,QAAQ,KAAK,CAAC,MAAM;wEAAC;;;;;;;;;;;;;sEAE7D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,SAAS,IAAM,iBAAiB;oEAChC,WAAU;;sFAEV,8OAAC,iMAAA,CAAA,MAAG;4EAAC,MAAM;4EAAI,WAAU;;;;;;wEAAS;;;;;;;8EAGpC,8OAAC;oEACC,SAAS;wEACP,mBAAmB;wEACnB,oBAAoB;oEACtB;oEACA,WAAU;;sFAEV,8OAAC,2MAAA,CAAA,WAAQ;4EAAC,MAAM;4EAAI,WAAU;;;;;;wEAAS;;;;;;;;;;;;;;;;;;;+CAtBrC,QAAQ,EAAE;;;;;;;;;;;;;;;;0CAkC5B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACZ,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,EAAE,GAAG,CAAC,CAAC,sBAC5C,8OAAC;gDAAmB,WAAU;0DAC5B,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA6B,MAAM,IAAI;;;;;;8EACrD,8OAAC;oEAAE,WAAU;8EAAyB,MAAM,OAAO;;;;;;;;;;;;sEAErD,8OAAC;4DACC,SAAS,IAAM,YAAY,MAAM,WAAW;4DAC5C,WAAU;;8EAEV,8OAAC,mMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;;gEAAS;;;;;;;;;;;;;+CAV/B,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAqB3B,cAAc,4BACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAiB;;;;;;;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS3C,cAAc,0BAAY,8OAAC;;;;;0BAG5B,8OAAC;;;;;YAGA,4BACC,8OAAC,mJAAA,CAAA,sBAAmB;gBAClB,gBAAgB;gBAChB,SAAS,IAAM,cAAc;;;;;;YAKhC,cAAc,kCACb,8OAAC,0IAAA,CAAA,iBAAc;gBACb,aAAa;gBACb,OAAM;gBACN,aAAY;gBACZ,SAAS,IAAM,cAAc;;;;;;YAKhC,8BACC,8OAAC,6IAAA,CAAA,gBAAa;gBACZ,kBAAkB;gBAClB,mBAAmB;gBACnB,SAAS,IAAM,gBAAgB;gBAC/B,WAAW;;;;;;YAKd,6BACC,8OAAC,iJAAA,CAAA,mBAAgB;gBACf,SAAS,IAAM,eAAe;gBAC9B,eAAe,CAAC,aAAa;oBAC3B,uDAAuD;oBACvD,MAAM,WAAkB;wBACtB,IAAI,KAAK,GAAG,GAAG,QAAQ;wBACvB,MAAM;wBACN,SAAS;wBACT,OAAO,KAAK,IAAI,CAAC,KAAK,MAAM,KAAK,KAAK;wBACtC,QAAQ;wBACR,aAAa;wBACb,WAAW;wBACX,aAAa;wBACb,WAAW;oBACb;oBACA,UAAU,CAAA,OAAQ;4BAAC;+BAAa;yBAAK;gBACvC;;;;;;YAKH,mBAAmB,iCAClB,8OAAC,uIAAA,CAAA,UAAW;gBACV,OAAO;gBACP,SAAS;oBACP,mBAAmB;oBACnB,mBAAmB;gBACrB;;;;;;YAKH,kCACC,8OAAC,wIAAA,CAAA,UAAY;gBACX,SAAS;oBACP,oBAAoB;oBACpB,mBAAmB;gBACrB;gBACA,gBAAgB;gBAChB,eAAe,mBAAmB;;;;;;YAKrC,yBAAyB,gCACxB,8OAAC,6IAAA,CAAA,UAAiB;gBAChB,aAAa,eAAe,WAAW;gBACvC,UAAU,eAAe,QAAQ;gBACjC,WAAW,eAAe,SAAS;gBACnC,WAAW,eAAe,SAAS;gBACnC,SAAS;oBACP,yBAAyB;oBACzB,kBAAkB;gBACpB;gBACA,cAAc;oBACZ,kBAAkB,CAAA,OAAQ,OAAO;4BAC/B,GAAG,IAAI;4BACP,WAAW,CAAC,KAAK,SAAS;wBAC5B,IAAI;gBACN;;;;;;;;;;;;AAKV", "debugId": null}}]}