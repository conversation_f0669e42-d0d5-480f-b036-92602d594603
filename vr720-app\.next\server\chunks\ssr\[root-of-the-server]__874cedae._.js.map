{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/components/VR/MobileOptimizedVR.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { X, Home, Info, Map, Play, Pause } from 'lucide-react';\n\ninterface MobileOptimizedVRProps {\n  panoramaUrl: string;\n  roomName: string;\n  houseName: string;\n  onClose: () => void;\n  is720Mode?: boolean;\n  onToggleMode?: () => void;\n}\n\nconst MobileOptimizedVR: React.FC<MobileOptimizedVRProps> = ({\n  panoramaUrl,\n  roomName,\n  houseName,\n  onClose,\n  is720Mode = true,\n  onToggleMode\n}) => {\n  const [isLoading, setIsLoading] = useState(true);\n  const [showInfo, setShowInfo] = useState(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (typeof window !== 'undefined' && containerRef.current) {\n      setIsLoading(true);\n\n      // 检测设备性能\n      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      const isLowEnd = /Android.*4\\.|iPhone.*OS [5-9]_|iPad.*OS [5-9]_/i.test(navigator.userAgent);\n\n      // 移动端优化的VR场景\n      const sceneHTML = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=no\">\n          <script src=\"https://aframe.io/releases/1.4.0/aframe.min.js\"></script>\n          <style>\n            body { \n              margin: 0; \n              overflow: hidden; \n              background: #000;\n              touch-action: none;\n              -webkit-user-select: none;\n              user-select: none;\n              -webkit-overflow-scrolling: touch;\n            }\n            #vr-scene { \n              width: 100vw; \n              height: 100vh; \n              position: fixed;\n              top: 0;\n              left: 0;\n            }\n            .loading {\n              position: fixed;\n              top: 50%;\n              left: 50%;\n              transform: translate(-50%, -50%);\n              color: white;\n              font-family: Arial, sans-serif;\n              text-align: center;\n              z-index: 1000;\n            }\n            .spinner {\n              width: 30px;\n              height: 30px;\n              border: 2px solid rgba(255,255,255,0.3);\n              border-top: 2px solid #4ecdc4;\n              border-radius: 50%;\n              animation: spin 1s linear infinite;\n              margin: 0 auto 10px;\n            }\n            @keyframes spin {\n              0% { transform: rotate(0deg); }\n              100% { transform: rotate(360deg); }\n            }\n          </style>\n        </head>\n        <body>\n          <div class=\"loading\" id=\"loading\">\n            <div class=\"spinner\"></div>\n            <div>Loading ${roomName}...</div>\n          </div>\n          \n          <a-scene \n            id=\"vr-scene\" \n            embedded \n            vr-mode-ui=\"enabled: false\"\n            background=\"color: #000\"\n            device-orientation-permission-ui=\"enabled: false\"\n            renderer=\"antialias: false; \n                     logarithmicDepthBuffer: false; \n                     precision: ${isLowEnd ? 'lowp' : 'mediump'}; \n                     maxCanvasWidth: ${isLowEnd ? 768 : 1024}; \n                     maxCanvasHeight: ${isLowEnd ? 768 : 1024};\n                     colorManagement: false;\n                     sortObjects: false;\n                     physicallyCorrectLights: false\"\n            stats=\"false\"\n          >\n            <a-assets>\n              <img id=\"panorama\" src=\"${panoramaUrl}\" crossorigin=\"anonymous\">\n            </a-assets>\n            \n            <!-- 简化的天空球 -->\n            <a-sky \n              id=\"room-sky\"\n              src=\"#panorama\" \n              rotation=\"0 -90 0\"\n              animation=\"property: rotation; to: 0 ${is720Mode ? '630' : '270'} 0; loop: true; dur: ${isLowEnd ? (is720Mode ? 240000 : 150000) : (is720Mode ? 180000 : 100000)}; easing: linear\"\n              material=\"side: back; npot: true\"\n            ></a-sky>\n            \n            <!-- 优化的相机 -->\n            <a-camera \n              id=\"main-camera\"\n              look-controls=\"enabled: true; \n                           reverseMouseDrag: false; \n                           touchEnabled: true; \n                           magicWindowTrackingEnabled: true;\n                           pointerLockEnabled: false;\n                           mouseSensitivity: 0.2;\n                           touchSensitivity: ${isLowEnd ? 4 : 8}\"\n              wasd-controls=\"enabled: false\"\n              position=\"0 1.6 0\"\n              fov=\"75\"\n            ></a-camera>\n            \n            <!-- 简化的环境光 -->\n            <a-light type=\"ambient\" color=\"#666666\"></a-light>\n            \n            <!-- 简化的房间标题 -->\n            <a-text\n              value=\"${roomName}\"\n              position=\"0 3 -4\"\n              align=\"center\"\n              color=\"#4ecdc4\"\n              opacity=\"0.8\"\n              font=\"size: 14; weight: bold\"\n            ></a-text>\n          </a-scene>\n\n          <script>\n            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n            \n            document.addEventListener('DOMContentLoaded', function() {\n              // 移动端优化\n              if (isMobile) {\n                // 禁用右键菜单和滚动\n                document.addEventListener('contextmenu', e => e.preventDefault());\n                document.addEventListener('touchmove', e => e.preventDefault(), { passive: false });\n                \n                // 优化触摸响应\n                let touchStartTime = 0;\n                document.addEventListener('touchstart', e => {\n                  touchStartTime = Date.now();\n                }, { passive: true });\n                \n                document.addEventListener('touchend', e => {\n                  const touchDuration = Date.now() - touchStartTime;\n                  if (touchDuration < 200) {\n                    // 短触摸，可以添加特定操作\n                  }\n                }, { passive: true });\n              }\n              \n              // 隐藏加载界面\n              const scene = document.querySelector('a-scene');\n              const loading = document.querySelector('#loading');\n              \n              const hideLoading = () => {\n                if (loading) {\n                  loading.style.display = 'none';\n                }\n                // 通知父窗口加载完成\n                window.parent.postMessage({ type: 'loaded' }, '*');\n              };\n              \n              if (scene) {\n                scene.addEventListener('loaded', () => {\n                  setTimeout(hideLoading, 800);\n                });\n              } else {\n                setTimeout(hideLoading, 2000);\n              }\n            });\n            \n            // 监听父窗口消息\n            window.addEventListener('message', function(event) {\n              if (event.data === 'toggle720') {\n                const sky = document.querySelector('#room-sky');\n                const currentAnimation = sky.getAttribute('animation');\n                const isCurrently720 = currentAnimation.to.includes('630');\n                \n                const isLowEnd = ${isLowEnd.toString()};\n                const duration720 = isLowEnd ? 240000 : 180000;\n                const duration360 = isLowEnd ? 150000 : 100000;\n                \n                if (isCurrently720) {\n                  sky.setAttribute('animation', \\`property: rotation; to: 0 270 0; loop: true; dur: \\${duration360}; easing: linear\\`);\n                } else {\n                  sky.setAttribute('animation', \\`property: rotation; to: 0 630 0; loop: true; dur: \\${duration720}; easing: linear\\`);\n                }\n                \n                // 通知父窗口模式已切换\n                window.parent.postMessage({ \n                  type: 'modeChanged', \n                  is720Mode: !isCurrently720 \n                }, '*');\n              }\n            });\n          </script>\n        </body>\n        </html>\n      `;\n\n      // 创建iframe\n      const iframe = document.createElement('iframe');\n      iframe.style.width = '100%';\n      iframe.style.height = '100%';\n      iframe.style.border = 'none';\n      iframe.style.position = 'absolute';\n      iframe.style.top = '0';\n      iframe.style.left = '0';\n      iframe.srcdoc = sceneHTML;\n\n      // 监听iframe消息\n      const handleMessage = (event: MessageEvent) => {\n        if (event.data?.type === 'loaded') {\n          setIsLoading(false);\n        } else if (event.data?.type === 'modeChanged') {\n          // 处理模式切换\n        }\n      };\n      \n      window.addEventListener('message', handleMessage);\n      \n      containerRef.current.appendChild(iframe);\n      \n      return () => {\n        window.removeEventListener('message', handleMessage);\n        if (containerRef.current && iframe) {\n          containerRef.current.removeChild(iframe);\n        }\n      };\n    }\n  }, [panoramaUrl, roomName, is720Mode]);\n\n  // 切换720/360模式\n  const handleToggleMode = () => {\n    const iframe = containerRef.current?.querySelector('iframe');\n    if (iframe?.contentWindow) {\n      iframe.contentWindow.postMessage('toggle720', '*');\n    }\n    onToggleMode?.();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50\">\n      {/* VR场景容器 */}\n      <div ref={containerRef} className=\"w-full h-full relative\" />\n\n      {/* 加载状态 */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/90 flex items-center justify-center z-30\">\n          <div className=\"text-white text-center\">\n            <div className=\"relative mb-4\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-3 border-blue-500 mx-auto\"></div>\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"text-lg\">🏠</div>\n              </div>\n            </div>\n            <p className=\"text-lg font-bold text-blue-400\">Loading {roomName}</p>\n            <p className=\"text-sm text-gray-400 mt-1\">{houseName} - Mobile VR</p>\n          </div>\n        </div>\n      )}\n\n      {/* 简化的控制栏 */}\n      <div className=\"absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-3 z-20 pointer-events-none\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n            >\n              <X size={18} />\n            </button>\n            \n            <div className=\"text-white\">\n              <h2 className=\"text-base font-medium\">{houseName}</h2>\n              <p className=\"text-sm text-gray-300\">🏠 {roomName}</p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => setShowInfo(!showInfo)}\n              className=\"pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n            >\n              <Info size={18} />\n            </button>\n            \n            <button\n              onClick={handleToggleMode}\n              className=\"pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95\"\n              title={is720Mode ? '切换到360°' : '切换到720°'}\n            >\n              {is720Mode ? <Pause size={18} /> : <Play size={18} />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 信息面板 */}\n      {showInfo && (\n        <div className=\"absolute top-16 left-3 bg-black/90 text-white p-3 rounded-lg max-w-xs z-20\">\n          <h3 className=\"text-base font-medium mb-2\">{roomName}</h3>\n          <div className=\"text-xs text-gray-400 space-y-1\">\n            <p>• 拖拽旋转视角</p>\n            <p>• 当前模式: {is720Mode ? 'VR720°' : 'VR360°'}</p>\n            <p>• 移动端优化版本</p>\n          </div>\n        </div>\n      )}\n\n      {/* 底部状态指示器 */}\n      <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3 z-20 pointer-events-none\">\n        <div className=\"text-center\">\n          <div className=\"inline-flex items-center bg-black/70 text-white px-3 py-1 rounded-full text-xs\">\n            <div className={`w-2 h-2 rounded-full mr-2 ${is720Mode ? 'bg-green-500' : 'bg-blue-500'}`}></div>\n            {is720Mode ? 'VR720°' : 'VR360°'} Mobile • 拖拽旋转视角\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MobileOptimizedVR;\n\nconst duration720 = isLowEnd ? 360000 : 240000;\nconst duration360 = isLowEnd ? 240000 : 120000;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAcA,MAAM,oBAAsD,CAAC,EAC3D,WAAW,EACX,QAAQ,EACR,SAAS,EACT,OAAO,EACP,YAAY,IAAI,EAChB,YAAY,EACb;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAA2D;;QAgO3D;IACF,GAAG;QAAC;QAAa;QAAU;KAAU;IAErC,cAAc;IACd,MAAM,mBAAmB;QACvB,MAAM,SAAS,aAAa,OAAO,EAAE,cAAc;QACnD,IAAI,QAAQ,eAAe;YACzB,OAAO,aAAa,CAAC,WAAW,CAAC,aAAa;QAChD;QACA;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;;;;;YAGjC,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;;gCAAkC;gCAAS;;;;;;;sCACxD,8OAAC;4BAAE,WAAU;;gCAA8B;gCAAU;;;;;;;;;;;;;;;;;;0BAM3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,6LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;8CAGX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI;;;;;;;;;;;;;;;;;;;sCAI7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,YAAY,CAAC;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,mMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAGd,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO,YAAY,YAAY;8CAE9B,0BAAY,8OAAC,qMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;6DAAS,8OAAC,mMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOtD,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAC5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;;oCAAE;oCAAS,YAAY,WAAW;;;;;;;0CACnC,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;0BAMT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,0BAA0B,EAAE,YAAY,iBAAiB,eAAe;;;;;;4BACxF,YAAY,WAAW;4BAAS;;;;;;;;;;;;;;;;;;;;;;;AAM7C;uCAEe;AAEf,MAAM,cAAc,WAAW,SAAS;AACxC,MAAM,cAAc,WAAW,SAAS", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/VR720/vr720-app/src/app/mobile-performance/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { \n  Smartphone, \n  Monitor, \n  Zap, \n  Settings, \n  CheckCircle,\n  AlertTriangle,\n  Info,\n  Play,\n  ArrowRight\n} from 'lucide-react';\nimport MobileOptimizedVR from '@/components/VR/MobileOptimizedVR';\n\nconst MobilePerformancePage: React.FC = () => {\n  const [showDemo, setShowDemo] = useState(false);\n  const [deviceInfo, setDeviceInfo] = useState<{\n    isMobile: boolean;\n    isLowEnd: boolean;\n    userAgent: string;\n    screenSize: string;\n    pixelRatio: number;\n  } | null>(null);\n\n  useEffect(() => {\n    // 检测设备信息\n    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n    const isLowEnd = /Android.*4\\.|iPhone.*OS [5-9]_|iPad.*OS [5-9]_/i.test(navigator.userAgent);\n    \n    setDeviceInfo({\n      isMobile,\n      isLowEnd,\n      userAgent: navigator.userAgent,\n      screenSize: `${window.screen.width}x${window.screen.height}`,\n      pixelRatio: window.devicePixelRatio || 1\n    });\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 头部 */}\n      <div className=\"bg-white shadow-sm border-b border-gray-200 px-4 py-4\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"flex items-center\">\n            <div className=\"w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center mr-3\">\n              <Zap size={24} className=\"text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900\">移动端性能优化</h1>\n              <p className=\"text-sm text-gray-600\">VR720移动端观看体验优化方案</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto px-4 py-8\">\n        {/* 设备检测结果 */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          <h2 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\n            <Smartphone className=\"text-blue-600 mr-2\" size={24} />\n            当前设备检测\n          </h2>\n          \n          {deviceInfo && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <span className=\"font-medium\">设备类型</span>\n                  <span className={`px-2 py-1 rounded text-sm ${\n                    deviceInfo.isMobile ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                  }`}>\n                    {deviceInfo.isMobile ? '📱 移动设备' : '💻 桌面设备'}\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <span className=\"font-medium\">性能等级</span>\n                  <span className={`px-2 py-1 rounded text-sm ${\n                    deviceInfo.isLowEnd ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'\n                  }`}>\n                    {deviceInfo.isLowEnd ? '⚡ 低端设备' : '🚀 高性能设备'}\n                  </span>\n                </div>\n              </div>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <span className=\"font-medium\">屏幕分辨率</span>\n                  <span className=\"text-gray-700\">{deviceInfo.screenSize}</span>\n                </div>\n                \n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                  <span className=\"font-medium\">像素密度</span>\n                  <span className=\"text-gray-700\">{deviceInfo.pixelRatio}x</span>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* 优化方案 */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          <h2 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\n            <Settings className=\"text-green-600 mr-2\" size={24} />\n            性能优化方案\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* 移动端优化 */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n                <Smartphone className=\"text-blue-600 mr-2\" size={20} />\n                移动端优化\n              </h3>\n              <ul className=\"space-y-3\">\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"text-green-600 mr-2 mt-0.5 flex-shrink-0\" size={16} />\n                  <div>\n                    <span className=\"font-medium\">渲染器优化</span>\n                    <p className=\"text-sm text-gray-600\">关闭抗锯齿、降低精度、限制画布尺寸</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"text-green-600 mr-2 mt-0.5 flex-shrink-0\" size={16} />\n                  <div>\n                    <span className=\"font-medium\">动画优化</span>\n                    <p className=\"text-sm text-gray-600\">减少复杂动画、调整帧率、简化效果</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"text-green-600 mr-2 mt-0.5 flex-shrink-0\" size={16} />\n                  <div>\n                    <span className=\"font-medium\">触控优化</span>\n                    <p className=\"text-sm text-gray-600\">优化触摸响应、防止页面滚动</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"text-green-600 mr-2 mt-0.5 flex-shrink-0\" size={16} />\n                  <div>\n                    <span className=\"font-medium\">内存管理</span>\n                    <p className=\"text-sm text-gray-600\">及时释放资源、减少内存占用</p>\n                  </div>\n                </li>\n              </ul>\n            </div>\n\n            {/* 桌面端功能 */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n                <Monitor className=\"text-purple-600 mr-2\" size={20} />\n                桌面端功能\n              </h3>\n              <ul className=\"space-y-3\">\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"text-green-600 mr-2 mt-0.5 flex-shrink-0\" size={16} />\n                  <div>\n                    <span className=\"font-medium\">高质量渲染</span>\n                    <p className=\"text-sm text-gray-600\">开启抗锯齿、高精度渲染</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"text-green-600 mr-2 mt-0.5 flex-shrink-0\" size={16} />\n                  <div>\n                    <span className=\"font-medium\">丰富动画</span>\n                    <p className=\"text-sm text-gray-600\">复杂动画效果、光环指示器</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"text-green-600 mr-2 mt-0.5 flex-shrink-0\" size={16} />\n                  <div>\n                    <span className=\"font-medium\">3D连接点</span>\n                    <p className=\"text-sm text-gray-600\">完整的3D连接点系统</p>\n                  </div>\n                </li>\n                <li className=\"flex items-start\">\n                  <CheckCircle className=\"text-green-600 mr-2 mt-0.5 flex-shrink-0\" size={16} />\n                  <div>\n                    <span className=\"font-medium\">全屋漫游</span>\n                    <p className=\"text-sm text-gray-600\">完整的多房间导航功能</p>\n                  </div>\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* 性能对比 */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          <h2 className=\"text-xl font-bold text-gray-900 mb-4\">性能对比</h2>\n          \n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full text-sm\">\n              <thead>\n                <tr className=\"border-b border-gray-200\">\n                  <th className=\"text-left py-3 px-4 font-medium text-gray-900\">优化项目</th>\n                  <th className=\"text-center py-3 px-4 font-medium text-gray-900\">优化前</th>\n                  <th className=\"text-center py-3 px-4 font-medium text-gray-900\">优化后</th>\n                  <th className=\"text-center py-3 px-4 font-medium text-gray-900\">提升</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"py-3 px-4\">加载时间</td>\n                  <td className=\"text-center py-3 px-4 text-red-600\">3-5秒</td>\n                  <td className=\"text-center py-3 px-4 text-green-600\">1-2秒</td>\n                  <td className=\"text-center py-3 px-4 text-blue-600\">60%+</td>\n                </tr>\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"py-3 px-4\">帧率稳定性</td>\n                  <td className=\"text-center py-3 px-4 text-red-600\">15-25 FPS</td>\n                  <td className=\"text-center py-3 px-4 text-green-600\">25-30 FPS</td>\n                  <td className=\"text-center py-3 px-4 text-blue-600\">40%+</td>\n                </tr>\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"py-3 px-4\">内存占用</td>\n                  <td className=\"text-center py-3 px-4 text-red-600\">80-120MB</td>\n                  <td className=\"text-center py-3 px-4 text-green-600\">40-60MB</td>\n                  <td className=\"text-center py-3 px-4 text-blue-600\">50%+</td>\n                </tr>\n                <tr className=\"border-b border-gray-100\">\n                  <td className=\"py-3 px-4\">触控响应</td>\n                  <td className=\"text-center py-3 px-4 text-red-600\">100-200ms</td>\n                  <td className=\"text-center py-3 px-4 text-green-600\">50-80ms</td>\n                  <td className=\"text-center py-3 px-4 text-blue-600\">60%+</td>\n                </tr>\n                <tr>\n                  <td className=\"py-3 px-4\">电池消耗</td>\n                  <td className=\"text-center py-3 px-4 text-red-600\">高</td>\n                  <td className=\"text-center py-3 px-4 text-green-600\">中等</td>\n                  <td className=\"text-center py-3 px-4 text-blue-600\">30%+</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* 体验演示 */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          <h2 className=\"text-xl font-bold text-gray-900 mb-4\">体验演示</h2>\n          <p className=\"text-gray-600 mb-6\">\n            点击下方按钮体验优化后的移动端VR720°观看效果，感受流畅的旋转动画和快速的响应速度。\n          </p>\n          \n          <div className=\"text-center\">\n            <button\n              onClick={() => setShowDemo(true)}\n              className=\"bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg text-lg font-medium flex items-center mx-auto transition-colors\"\n            >\n              <Play size={20} className=\"mr-2\" />\n              体验优化版VR720°\n            </button>\n          </div>\n        </div>\n\n        {/* 技术说明 */}\n        <div className=\"bg-blue-50 rounded-lg p-6\">\n          <h2 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\n            <Info className=\"text-blue-600 mr-2\" size={24} />\n            技术说明\n          </h2>\n          \n          <div className=\"space-y-4 text-gray-700\">\n            <div>\n              <h3 className=\"font-semibold mb-2\">自适应性能调整</h3>\n              <p className=\"text-sm\">\n                系统会自动检测设备性能，为低端设备提供优化的渲染设置，为高端设备提供完整的视觉效果。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"font-semibold mb-2\">实时性能监控</h3>\n              <p className=\"text-sm\">\n                内置FPS监控系统，当检测到帧率过低时会自动降低渲染质量以保证流畅度。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"font-semibold mb-2\">移动端专用组件</h3>\n              <p className=\"text-sm\">\n                为移动设备专门开发的轻量级VR组件，去除了不必要的功能，专注于核心的VR720°体验。\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* 返回按钮 */}\n        <div className=\"text-center mt-8\">\n          <a\n            href=\"/mobile\"\n            className=\"inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <ArrowRight className=\"mr-2 rotate-180\" size={20} />\n            返回移动端页面\n          </a>\n        </div>\n      </div>\n\n      {/* VR演示组件 */}\n      {showDemo && (\n        <MobileOptimizedVR\n          panoramaUrl=\"/panoramas/living-room-modern-1.jpg\"\n          roomName=\"性能演示\"\n          houseName=\"移动端优化测试\"\n          is720Mode={true}\n          onClose={() => setShowDemo(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default MobilePerformancePage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAdA;;;;;AAgBA,MAAM,wBAAkC;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAMjC;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;QACT,MAAM,WAAW,2DAA2D,IAAI,CAAC,UAAU,SAAS;QACpG,MAAM,WAAW,kDAAkD,IAAI,CAAC,UAAU,SAAS;QAE3F,cAAc;YACZ;YACA;YACA,WAAW,UAAU,SAAS;YAC9B,YAAY,GAAG,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE;YAC5D,YAAY,OAAO,gBAAgB,IAAI;QACzC;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iMAAA,CAAA,MAAG;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAE3B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,+MAAA,CAAA,aAAU;wCAAC,WAAU;wCAAqB,MAAM;;;;;;oCAAM;;;;;;;4BAIxD,4BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,8OAAC;wDAAK,WAAW,CAAC,0BAA0B,EAC1C,WAAW,QAAQ,GAAG,8BAA8B,6BACpD;kEACC,WAAW,QAAQ,GAAG,YAAY;;;;;;;;;;;;0DAIvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,8OAAC;wDAAK,WAAW,CAAC,0BAA0B,EAC1C,WAAW,QAAQ,GAAG,kCAAkC,+BACxD;kEACC,WAAW,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;kDAKxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,8OAAC;wDAAK,WAAU;kEAAiB,WAAW,UAAU;;;;;;;;;;;;0DAGxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAc;;;;;;kEAC9B,8OAAC;wDAAK,WAAU;;4DAAiB,WAAW,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,2MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAsB,MAAM;;;;;;oCAAM;;;;;;;0CAIxD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,+MAAA,CAAA,aAAU;wDAAC,WAAU;wDAAqB,MAAM;;;;;;oDAAM;;;;;;;0DAGzD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;gEAA2C,MAAM;;;;;;0EACxE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;gEAA2C,MAAM;;;;;;0EACxE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;gEAA2C,MAAM;;;;;;0EACxE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;gEAA2C,MAAM;;;;;;0EACxE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,yMAAA,CAAA,UAAO;wDAAC,WAAU;wDAAuB,MAAM;;;;;;oDAAM;;;;;;;0DAGxD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;gEAA2C,MAAM;;;;;;0EACxE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;gEAA2C,MAAM;;;;;;0EACxE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;gEAA2C,MAAM;;;;;;0EACxE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,qNAAA,CAAA,cAAW;gEAAC,WAAU;gEAA2C,MAAM;;;;;;0EACxE,8OAAC;;kFACC,8OAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAErD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAAgD;;;;;;kEAC9D,8OAAC;wDAAG,WAAU;kEAAkD;;;;;;kEAChE,8OAAC;wDAAG,WAAU;kEAAkD;;;;;;kEAChE,8OAAC;wDAAG,WAAU;kEAAkD;;;;;;;;;;;;;;;;;sDAGpE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAY;;;;;;sEAC1B,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEACrD,8OAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAY;;;;;;sEAC1B,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEACrD,8OAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAY;;;;;;sEAC1B,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEACrD,8OAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAEtD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAY;;;;;;sEAC1B,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEACrD,8OAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAEtD,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAY;;;;;;sEAC1B,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEACrD,8OAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAIlC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAU;;sDAEV,8OAAC,mMAAA,CAAA,OAAI;4CAAC,MAAM;4CAAI,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;kCAOzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,mMAAA,CAAA,OAAI;wCAAC,WAAU;wCAAqB,MAAM;;;;;;oCAAM;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAKzB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAKzB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAQ7B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;;8CAEV,8OAAC,mNAAA,CAAA,aAAU;oCAAC,WAAU;oCAAkB,MAAM;;;;;;gCAAM;;;;;;;;;;;;;;;;;;YAOzD,0BACC,8OAAC,6IAAA,CAAA,UAAiB;gBAChB,aAAY;gBACZ,UAAS;gBACT,WAAU;gBACV,WAAW;gBACX,SAAS,IAAM,YAAY;;;;;;;;;;;;AAKrC;uCAEe", "debugId": null}}]}