# VR720 数据库系统

VR720应用使用了一个灵活的数据库系统，支持SQLite作为默认数据库，并设计了可扩展的接口以便后续迁移到其他数据库系统。

## 🏗️ 架构设计

### 数据库抽象层
- **接口定义**: `src/lib/database/types.ts` - 定义了所有数据库操作的接口
- **工厂模式**: `src/lib/database/factory.ts` - 支持多种数据库类型的创建和管理
- **仓库模式**: 每个实体都有对应的仓库类，提供CRUD操作

### 当前实现
- **SQLite**: 使用 `better-sqlite3` 作为默认数据库
- **迁移系统**: 自动管理数据库结构变更
- **连接管理**: 自动连接池和事务支持

## 📊 数据模型

### 核心实体

#### 用户 (Users)
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: 'admin' | 'agent' | 'client';
  preferences: UserPreferences;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 图片 (Images)
```typescript
interface Image {
  id: string;
  name: string;
  originalName: string;
  size: number;
  mimeType: string;
  width?: number;
  height?: number;
  aspectRatio?: number;
  filePath: string;
  thumbnailPath?: string;
  metadata: ImageMetadata;
  userId?: string;
  tags: string[];
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 房屋 (Houses)
```typescript
interface House {
  id: string;
  name: string;
  address: string;
  description?: string;
  price?: number;
  currency: string;
  totalArea?: number;
  bedrooms: number;
  bathrooms: number;
  propertyType: 'single-family' | 'condo' | 'townhouse' | 'mansion' | 'studio';
  status: 'draft' | 'published' | 'sold' | 'archived';
  features: string[];
  userId?: string;
  agentId?: string;
  isPublic: boolean;
  viewCount: number;
  metadata: HouseMetadata;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 房间 (Rooms)
```typescript
interface Room {
  id: string;
  houseId: string;
  name: string;
  type: string;
  description?: string;
  area?: number;
  floor?: number;
  position: RoomPosition;
  panoramaImageId?: string;
  thumbnailImageId?: string;
  features: string[];
  metadata: RoomMetadata;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 热点 (Hotspots)
```typescript
interface Hotspot {
  id: string;
  roomId: string;
  targetRoomId: string;
  label: string;
  description?: string;
  position: HotspotPosition;
  type: 'navigation' | 'info' | 'media' | 'external';
  isActive: boolean;
  metadata: HotspotMetadata;
  createdAt: Date;
  updatedAt: Date;
}
```

## 🚀 快速开始

### 1. 初始化数据库
```bash
# 初始化空数据库
npm run db:init

# 初始化数据库并创建示例数据
npm run db:init:sample
```

### 2. 在代码中使用数据库
```typescript
import { getDatabase, getDefaultConfig } from '@/lib/database/factory';

// 获取数据库实例
const db = await getDatabase(getDefaultConfig());

// 创建房屋
const house = await db.houses.create({
  name: '我的房屋',
  address: '123 Main St',
  bedrooms: 3,
  bathrooms: 2,
  propertyType: 'single-family',
  status: 'draft',
  features: [],
  isPublic: false,
  viewCount: 0,
  currency: 'USD',
  metadata: {}
});

// 查询房屋
const houses = await db.houses.findMany({ isPublic: true });

// 更新房屋
const updated = await db.houses.update(house.id, { 
  status: 'published',
  isPublic: true 
});
```

### 3. 使用React Hook
```typescript
import { useHouses, useImages } from '@/hooks/useDatabase';

function MyComponent() {
  const { houses, loading, error, fetchHouses, createHouse } = useHouses();
  const { images, uploadImage } = useImages();

  useEffect(() => {
    fetchHouses({ isPublic: true });
  }, [fetchHouses]);

  // 组件逻辑...
}
```

## 🔧 配置

### 环境变量
```bash
# 数据库类型 (sqlite, mysql, postgresql, mongodb)
DB_TYPE=sqlite

# SQLite 配置
DATABASE_URL=./data/vr720.db

# MySQL 配置 (当 DB_TYPE=mysql 时)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=vr720
DB_USER=root
DB_PASSWORD=password

# PostgreSQL 配置 (当 DB_TYPE=postgresql 时)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vr720
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false

# MongoDB 配置 (当 DB_TYPE=mongodb 时)
DATABASE_URL=mongodb://localhost:27017/vr720
```

### 自定义配置
```typescript
import { DatabaseFactory } from '@/lib/database/factory';

const customConfig = {
  type: 'sqlite',
  connectionString: './custom/path/database.db',
  options: {
    verbose: console.log // 启用SQL日志
  }
};

const db = await DatabaseFactory.create(customConfig);
```

## 📈 API 端点

### 房屋 API
- `GET /api/houses` - 获取房屋列表
- `POST /api/houses` - 创建新房屋
- `GET /api/houses/[id]` - 获取单个房屋
- `PUT /api/houses/[id]` - 更新房屋
- `DELETE /api/houses/[id]` - 删除房屋

### 图片 API
- `GET /api/images` - 获取图片列表
- `POST /api/images` - 上传图片
- `PUT /api/images` - 批量更新图片
- `DELETE /api/images` - 批量删除图片

## 🔄 数据库迁移

### 创建新迁移
```typescript
// src/lib/database/sqlite/migrations.ts
export const newMigration: Migration = {
  version: '002',
  name: 'add_new_feature',
  
  async up() {
    // 升级逻辑
    await connection.executeQuery(`
      ALTER TABLE houses ADD COLUMN new_field TEXT
    `);
  },

  async down() {
    // 回滚逻辑
    await connection.executeQuery(`
      ALTER TABLE houses DROP COLUMN new_field
    `);
  }
};

// 添加到迁移列表
export const migrations: Migration[] = [
  initialMigration,
  newMigration
];
```

## 🔄 数据库迁移到其他系统

### 迁移到 MySQL
1. 安装 MySQL 驱动: `npm install mysql2`
2. 实现 MySQL 连接类
3. 实现 MySQL 仓库类
4. 更新工厂配置

### 迁移到 PostgreSQL
1. 安装 PostgreSQL 驱动: `npm install pg`
2. 实现 PostgreSQL 连接类
3. 实现 PostgreSQL 仓库类
4. 更新工厂配置

### 迁移到 MongoDB
1. 安装 MongoDB 驱动: `npm install mongodb`
2. 实现 MongoDB 连接类
3. 实现 MongoDB 仓库类（适配NoSQL）
4. 更新工厂配置

## 🛠️ 维护工具

### 备份数据库
```bash
npm run db:backup
```

### 优化数据库
```bash
npm run db:optimize
```

### 健康检查
```typescript
import { healthCheck } from '@/lib/database/factory';

const health = await healthCheck(db);
console.log('Database status:', health.status);
```

## 📝 最佳实践

1. **使用事务**: 对于复杂操作，使用事务确保数据一致性
2. **索引优化**: 为常用查询字段添加索引
3. **数据验证**: 在应用层进行数据验证
4. **错误处理**: 妥善处理数据库错误
5. **连接管理**: 及时关闭数据库连接
6. **备份策略**: 定期备份重要数据
7. **监控性能**: 监控查询性能和数据库使用情况

## 🔍 故障排除

### 常见问题

1. **数据库文件权限错误**
   - 确保应用有读写数据库文件的权限
   - 检查数据库目录是否存在

2. **迁移失败**
   - 检查迁移脚本语法
   - 查看错误日志
   - 手动回滚到上一个版本

3. **连接超时**
   - 检查数据库服务是否运行
   - 验证连接配置
   - 检查网络连接

4. **性能问题**
   - 添加适当的索引
   - 优化查询语句
   - 考虑数据分页

### 调试技巧

1. 启用SQL日志查看执行的查询
2. 使用数据库工具检查数据结构
3. 监控数据库文件大小和性能指标
4. 定期运行VACUUM和ANALYZE优化数据库
