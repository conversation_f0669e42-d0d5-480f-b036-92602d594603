# VR720 - 专业360度全景拍摄与VR体验应用

VR720是一个基于Next.js开发的现代化全景拍摄和VR查看应用，支持360度全景图片的拍摄、拼接和沉浸式VR体验。

## ✨ 主要功能

### 📱 智能拍摄
- **360度全景拍摄**：引导用户按45度间隔拍摄8张照片
- **实时方向指示**：显示设备方向和拍摄进度
- **智能拍摄提示**：提供拍摄指导和质量建议
- **单张拍摄模式**：支持单张照片的VR查看

### 🔧 自动拼接
- **智能图像拼接**：自动将多张照片拼接成360度全景图
- **边缘融合算法**：平滑处理图片接缝
- **实时进度显示**：显示拼接处理进度
- **高质量输出**：生成4K分辨率全景图

### 🥽 VR体验
- **沉浸式查看**：基于A-Frame的VR全景查看器
- **多种交互方式**：支持鼠标、触摸、键盘控制
- **VR设备支持**：兼容各种VR头显设备
- **全屏模式**：提供最佳的观看体验

## 🚀 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器
- 现代浏览器（支持WebRTC和WebGL）

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

打开 [http://localhost:3001](http://localhost:3001) 查看应用。

### 构建生产版本
```bash
npm run build
npm start
```

## 🛠️ 技术栈

### 前端框架
- **Next.js 15.3.5** - React全栈框架
- **React 18.3.1** - 用户界面库
- **TypeScript** - 类型安全的JavaScript

### UI组件
- **Tailwind CSS 4** - 现代化CSS框架
- **Lucide React** - 精美的图标库
- **Framer Motion** - 流畅的动画效果

### VR/3D技术
- **A-Frame 1.4.0** - WebVR框架
- **Three.js** - 3D图形库
- **@react-three/fiber** - React Three.js集成
- **@react-three/drei** - Three.js实用工具

### 图像处理
- **Canvas API** - 图像处理和拼接
- **WebRTC** - 相机访问
- **Photo Sphere Viewer** - 全景图查看器

## 📱 使用指南

### 1. 360度全景拍摄
1. 点击"360度全景拍摄"按钮
2. 允许相机权限访问
3. 按照屏幕提示，每45度拍摄一张照片
4. 保持手机水平，确保照片有重叠区域
5. 完成8张照片拍摄后自动进入拼接模式

### 2. 图像拼接处理
1. 查看已拍摄的照片预览
2. 点击"开始生成全景图"按钮
3. 等待自动拼接处理完成
4. 预览生成的全景图效果

### 3. VR全景体验
1. 点击"查看VR效果"进入VR模式
2. 使用鼠标拖拽查看360度全景
3. 滚轮缩放调整视野范围
4. 点击VR图标进入沉浸式VR模式
5. 支持VR头显设备的完整体验

### 4. 体验演示
- 点击"体验演示"按钮可立即体验VR720的完整功能
- 无需拍摄即可查看演示全景图的VR效果

## 🎯 核心特性

### 响应式设计
- 完美适配桌面端和移动端
- 触摸友好的交互设计
- 自适应布局和组件

### 性能优化
- 图像压缩和优化
- 懒加载和代码分割
- 高效的内存管理

### 用户体验
- 直观的操作界面
- 实时反馈和进度显示
- 详细的使用指导

## 🔧 开发说明

### 项目结构
```
vr720-app/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── page.tsx        # 主页面
│   │   ├── layout.tsx      # 布局组件
│   │   └── globals.css     # 全局样式
│   └── components/         # React组件
│       ├── Camera/         # 相机拍摄组件
│       ├── Panorama/       # 全景拼接组件
│       ├── VR/            # VR查看器组件
│       └── Demo/          # 演示数据
├── public/                # 静态资源
└── package.json          # 项目配置
```

### 主要组件
- **CameraCapture**: 相机拍摄和图像捕获
- **PanoramaStitcher**: 全景图拼接处理
- **VRViewer**: VR全景查看器
- **AFrameViewer**: A-Frame VR实现

## 🌟 未来计划

- [ ] 支持更多图像格式
- [ ] 添加滤镜和后处理效果
- [ ] 云端存储和分享功能
- [ ] AI辅助的拍摄优化
- [ ] 社交功能和作品展示
- [ ] 更多VR交互功能

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**VR720** - 让每个人都能轻松创建和体验专业级的360度全景VR内容。
