import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 图片优化配置
  images: {
    domains: [
      'localhost',
      '127.0.0.1',
      'upset-crabs-hang.loca.lt'
    ],
    unoptimized: true
  },

  // 开发服务器配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*'
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS'
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization'
          }
        ]
      }
    ];
  },

  // 压缩配置
  compress: true,

  // 开发模式配置
  devIndicators: {
    position: 'bottom-right'
  }
};

export default nextConfig;
