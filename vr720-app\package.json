{"name": "vr720-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:https": "node server.js", "build": "next build", "start": "next start", "lint": "next lint", "db:init": "node scripts/init-db.js", "db:init:sample": "node scripts/init-db.js --with-sample-data", "db:backup": "node scripts/backup-db.js", "db:optimize": "node scripts/optimize-db.js"}, "dependencies": {"@photo-sphere-viewer/core": "^5.13.3", "@react-three/drei": "^9.96.1", "@react-three/fiber": "^8.15.19", "@types/better-sqlite3": "^7.6.13", "@types/uuid": "^10.0.0", "aframe": "^1.4.0", "better-sqlite3": "^12.2.0", "framer-motion": "^10.16.16", "lucide-react": "^0.263.1", "next": "15.3.5", "pannellum": "^2.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "three": "^0.170.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/three": "^0.170.0", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}