#!/usr/bin/env node

// 数据库初始化脚本

const path = require('path');
const fs = require('fs');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

async function initializeDatabase() {
  try {
    console.log('🚀 Initializing VR720 Database...');

    // 确保数据目录存在
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log(`📂 Created database directory: ${dataDir}`);
    }

    const dbPath = path.join(dataDir, 'vr720.db');
    console.log(`📍 Database path: ${dbPath}`);

    console.log('✅ Database directory setup completed!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start your application: npm run dev');
    console.log('2. Visit http://localhost:3001');
    console.log('3. The database will be automatically created when you first use the app');
    console.log('4. Start uploading images and creating house configurations!');

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
}

// 运行初始化
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
