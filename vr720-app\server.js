const { createServer } = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = 3001;

// Create Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

// Self-signed certificate for development
const httpsOptions = {
  key: `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wEiOfH3nzor9cwHXLbkiG+cy6vJ3oVFNjKQhk/iaLp3oLVwC5d2QGpuLOjXXhqC
clCGAjbhIL+XbNwPBxBTeSBF/+/JmRBSA7cIekH4KykM2O+fy2VxFBdXfpHAQy
xVHqbVw9cpTRiGdWN4jqZbckmDtmHRTWiQlPXPp5xJbYpfUE1nf6foHpaEcDNw
9JWvVelxzlrtxzpiAMRgHOg4EiuY/MHGWJYuM3OwQRYf3VGWOzjTTU5FGg6RgR
xggdMmUUQ5lPGoI20WBuNiNBOycsjfzpe/v/2l/qfPfrd8AxVD6OM+FJrri2k8
sYgwHpFGpzh7AgMBAAECggEBALNjnQeXw2oOjmeOiURaD4QJdp1o6T4Ae1/auV
wc+K4+bC2la2+n4+epKAU5/TxmqeQGGouuSqbrdxYEyLOizRdXiuIvOQpdmzIs
Pv02uJPK+5f+r9/Oa9jLmYD8RMXVs2U5LnT5naFaafcPuLhfAE7VNffKvNtd
BO9aHH0/lFrij2ExjIgQ+DT14+NUMpOGA/DA5FPcB4Q6ADHD8sl+mizOeKIV
+cMVoFVeEhkubhbeUebBRWpK2dK8kHuJRjOjT6cqHVqAQYnJZgfCeX+w2kzV
VlFZOJ+MqBgIDaqVAiJTlTybjN/RwKBK/C/QYmyZMpnGrS1+VoHCRdwlIUE
CgYEA4Pd7shNkDeBHlGSHT4Z4UJdmyDRQwpJtAXdSH+ntM8oppSLBaMRYjGe
IwjMurkHtC0mlfhLHM4b/HonQ3nzDxgVlsCHMpTMpTGjjHyuIDn+EkJVdwz
kTVmJstMKVy/JXjwlVRWC+XPSRgn+G/Qk7dSHdVGLWQskU+Ddd+Vs0CgYEA
1jKpAoGBAOjIwIiXS4xyIjZAPlAq6x7StXn5VhYGnMNMPvMBa4EdFNkqHwH
6erHwMiC/aLlJ+AoYdUx5rBugtE+cqz3VfqhKpQHI5sHM1uat2Hk+Gg6Fq
YOE+SL8dlHGfwjn7bgHdBOBFNV+bgAAoGAVqP02uJPK+5f+r9/Oa9jLmYD8
RMXVs2U5LnT5naFaafcPuLhfAE7VNffKvNtdBO9aHH0/lFrij2ExjIgQ+DT
14+NUMpOGA/DA5FPcB4Q6ADHD8sl+mizOeKIV+cMVoFVeEhkubhbeUebBRW
pK2dK8kHuJRjOjT6cqHVqAQYnJZgfCeX+w2kzVVlFZOJ+MqBgIDaqVAiJT
lTybjN/RwKBK/C/QYmyZMpnGrS1+VoHCRdwlIUECgYEA4Pd7shNkDeBHlGS
HT4Z4UJdmyDRQwpJtAXdSH+ntM8oppSLBaMRYjGIwjMurkHtC0mlfhLHM4b
/HonQ3nzDxgVlsCHMpTMpTGjjHyuIDn+EkJVdwzkTVmJstMKVy/JXjwlVRW
C+XPSRgn+G/Qk7dSHdVGLWQskU+Ddd+Vs0CgYEA1jKpAoGBAOjIwIiXS4xy
IjZAPlAq6x7StXn5VhYGnMNMPvMBa4EdFNkqHwH6erHwMiC/aLlJ+AoYdUx
5rBugtE+cqz3VfqhKpQHI5sHM1uat2Hk+Gg6FqYOE+SL8dlHGfwjn7bgHdB
OBFNVbgAAoGAVqP02uJPK+5f+r9/Oa9jLmYD8RMXVs2U5LnT5naFaafcPuL
hfAE7VNffKvNtdBO9aHH0/lFrij2ExjIgQ+DT14+NUMpOGA/DA5FPcB4Q6A
DHD8sl+mizOeKIV+cMVoFVeEhkubhbeUebBRWpK2dK8kHuJRjOjT6cqHVqA
QYnJZgfCeX+w2kzVVlFZOJ+MqBgIDaqVAiJTlTybjN/RwKBK/C/QYmyZMpn
GrS1+VoHCRdwlIUE=
-----END PRIVATE KEY-----`,
  cert: `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/OvD/XuFMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMTYxMjI4MjE0MjM1WhcNMjYxMjI2MjE0MjM1WjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAu1SU1L7VLPHCgcBIjnx9586K/XMB1y25IhvnMuryc6FRTYykIZP4mi6d
6C1cAuXdkBqbizo114agnJQhgI24SC/l2zcDwcQU3kgRf/vyZkQUgO3CHpB+CspD
Njvn8tlcRQXV36RwEMsVR6m1cPXKU0YhnVjeI6mW3JJg7Zh0U1okJT1z6ecSW2KX
1BNZ3+n6B6WhHAzcPSVr1Xpcc5a7cc6YgDEYBzoOBIrmPzBxliWLjNzsEEWH91Rl
js4001ORRoOkYEcYIHTJlFEOZTxqCNtFgbjYjQTsnLI386Xv7/9pf6nz363fAMVQ
+jjPhSa64tpPLGIMB6RRqc4ewIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3IWyKy
WNUX2eVzVhG2lOgwHwYDVR0jBBgwFoAUhKs/VJ3IWyKyWNUX2eVzVhG2lOgwDAYD
VR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAEhSQTK9+R4yjBiwJlk8tL7ss
JqOpfoLqIQHhPkmhDlwlxy8EJ4s6qXizIjQdEZKMxgMgFGunIdKiPQD9f6YBh8R
L9edzFrGSaHwvmeMB/v1sOb1kk+1ZdZLw+vXDfrjKjy6M24xzI7pVlNtKxsF5v7
BHFxXihyNjuCZ2KKtgMzLxR4C2HBhXL/rAjmw6uP4ykOeE5ziEjKKHcx9/0g6m
YzA/74Z1NPkS/cqrda+PK/+7hxvJJZ70jOoYQNdRqn1l/Q2u9QPHgWdXBHwVfP
kFJuJFrCdMuQdNoBSX+Qf8GQ1LWOb9+/UO4VrAi4Rs+LZKQkeD5zbdxqL8VQ==
-----END CERTIFICATE-----`
};

app.prepare().then(() => {
  createServer(httpsOptions, async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  })
  .once('error', (err) => {
    console.error(err);
    process.exit(1);
  })
  .listen(port, () => {
    console.log(`> Ready on https://${hostname}:${port}`);
  });
});
