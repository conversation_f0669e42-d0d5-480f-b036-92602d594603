@echo off
chcp 65001 >nul
echo ========================================
echo VR720 全景图片自动设置脚本
echo ========================================
echo.

REM 创建目标目录
echo 正在创建目标目录...
if not exist "public\panoramas" mkdir "public\panoramas"
if not exist "public\thumbnails" mkdir "public\thumbnails"

echo.
echo 正在复制和重命名全景图片...
echo.

REM 复制客厅图片 (选择前10张)
echo [1/4] 处理客厅图片...
copy "fanli\客厅\淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg" "public\panoramas\living-room-modern-1.jpg" >nul 2>&1
copy "fanli\客厅\淘宝店铺：三老爹设计素材基地-淘宝网 (5).jpg" "public\panoramas\living-room-luxury-2.jpg" >nul 2>&1
copy "fanli\客厅\淘宝店铺：三老爹设计素材基地-淘宝网 (10).jpg" "public\panoramas\living-room-cozy-3.jpg" >nul 2>&1
copy "fanli\客厅\淘宝店铺：三老爹设计素材基地-淘宝网 (15).jpg" "public\panoramas\living-room-elegant-4.jpg" >nul 2>&1
copy "fanli\客厅\淘宝店铺：三老爹设计素材基地-淘宝网 (20).jpg" "public\panoramas\living-room-spacious-5.jpg" >nul 2>&1

REM 复制卧室图片 (选择前5张)
echo [2/4] 处理卧室图片...
copy "fanli\卧室\淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg" "public\panoramas\bedroom-master-1.jpg" >nul 2>&1
copy "fanli\卧室\淘宝店铺：三老爹设计素材基地-淘宝网 (5).jpg" "public\panoramas\bedroom-guest-2.jpg" >nul 2>&1
copy "fanli\卧室\淘宝店铺：三老爹设计素材基地-淘宝网 (10).jpg" "public\panoramas\bedroom-kids-3.jpg" >nul 2>&1
copy "fanli\卧室\淘宝店铺：三老爹设计素材基地-淘宝网 (15).jpg" "public\panoramas\bedroom-cozy-4.jpg" >nul 2>&1

REM 复制厨房图片 (选择前3张)
echo [3/4] 处理厨房图片...
copy "fanli\厨房\淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg" "public\panoramas\kitchen-modern-1.jpg" >nul 2>&1
copy "fanli\厨房\淘宝店铺：三老爹设计素材基地-淘宝网 (3).jpg" "public\panoramas\kitchen-open-2.jpg" >nul 2>&1
copy "fanli\厨房\淘宝店铺：三老爹设计素材基地-淘宝网 (5).jpg" "public\panoramas\kitchen-luxury-3.jpg" >nul 2>&1

REM 复制卫浴图片 (选择前3张)
echo [4/4] 处理卫浴图片...
copy "fanli\卫浴\淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg" "public\panoramas\bathroom-master-1.jpg" >nul 2>&1
copy "fanli\卫浴\淘宝店铺：三老爹设计素材基地-淘宝网 (3).jpg" "public\panoramas\bathroom-guest-2.jpg" >nul 2>&1
copy "fanli\卫浴\淘宝店铺：三老爹设计素材基地-淘宝网 (5).jpg" "public\panoramas\bathroom-luxury-3.jpg" >nul 2>&1

echo.
echo ========================================
echo 图片复制完成！
echo ========================================
echo.
echo 已复制的全景图片：
echo.
echo 客厅 (Living Room):
echo   - living-room-modern-1.jpg
echo   - living-room-luxury-2.jpg  
echo   - living-room-cozy-3.jpg
echo   - living-room-elegant-4.jpg
echo   - living-room-spacious-5.jpg
echo.
echo 卧室 (Bedroom):
echo   - bedroom-master-1.jpg
echo   - bedroom-guest-2.jpg
echo   - bedroom-kids-3.jpg
echo   - bedroom-cozy-4.jpg
echo.
echo 厨房 (Kitchen):
echo   - kitchen-modern-1.jpg
echo   - kitchen-open-2.jpg
echo   - kitchen-luxury-3.jpg
echo.
echo 卫浴 (Bathroom):
echo   - bathroom-master-1.jpg
echo   - bathroom-guest-2.jpg
echo   - bathroom-luxury-3.jpg
echo.
echo ========================================
echo 下一步：创建缩略图
echo ========================================
echo.
echo 请使用图片编辑软件为每张图片创建 300x150 像素的缩略图
echo 保存到 public\thumbnails\ 目录，文件名加 -thumb 后缀
echo.
echo 例如：
echo   living-room-modern-1.jpg → living-room-modern-1-thumb.jpg
echo.
echo 完成后重启开发服务器：npm run dev
echo.
pause
