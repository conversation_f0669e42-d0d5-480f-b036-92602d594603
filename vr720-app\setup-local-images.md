# 设置本地全景图片指南

## 📁 文件夹结构设置

请按照以下步骤将您的全景图片集成到VR720应用中：

### 1. 创建公共目录
```bash
# 在项目根目录下执行
mkdir -p public/panoramas
mkdir -p public/thumbnails
```

### 2. 从您的本地文件夹复制图片

从 `F:\BaiduNetdiskDownload\s1234全景效果图` 文件夹中选择以下类型的图片：

#### 🏠 客厅 (Living Room)
- 复制 2-3 张最好的客厅全景图到 `public/panoramas/`
- 重命名为：
  - `living-room-modern-1.jpg`
  - `living-room-luxury-2.jpg`
  - `living-room-cozy-3.jpg`

#### 🛏️ 卧室 (Bedroom)
- 复制 2-3 张卧室全景图到 `public/panoramas/`
- 重命名为：
  - `bedroom-master-1.jpg`
  - `bedroom-guest-2.jpg`
  - `bedroom-kids-3.jpg`

#### 🍳 厨房 (Kitchen)
- 复制 2 张厨房全景图到 `public/panoramas/`
- 重命名为：
  - `kitchen-modern-1.jpg`
  - `kitchen-open-2.jpg`

#### 🛁 浴室 (Bathroom)
- 复制 1-2 张浴室全景图到 `public/panoramas/`
- 重命名为：
  - `bathroom-master-1.jpg`
  - `bathroom-guest-2.jpg`

#### 🍽️ 餐厅 (Dining Room)
- 复制 1 张餐厅全景图到 `public/panoramas/`
- 重命名为：
  - `dining-formal-1.jpg`

#### 📚 书房/办公室 (Office)
- 复制 1 张书房全景图到 `public/panoramas/`
- 重命名为：
  - `office-home-1.jpg`

#### 🌳 外景 (Exterior)
- 复制 2 张外景全景图到 `public/panoramas/`
- 重命名为：
  - `exterior-entrance-1.jpg`
  - `exterior-garden-2.jpg`

### 3. 创建缩略图

为每张全景图创建缩略图版本：
- 尺寸：300x150 像素
- 保存到 `public/thumbnails/`
- 文件名：在原文件名后加 `-thumb`
- 例如：`living-room-modern-1-thumb.jpg`

### 4. 图片要求

#### ✅ 推荐规格：
- **分辨率**：4096x2048 或更高
- **宽高比**：2:1 (等距柱状投影)
- **格式**：JPEG, PNG, WebP
- **文件大小**：每张图片小于 5MB
- **质量**：高质量，清晰度好

#### ⚠️ 注意事项：
- 确保图片是真正的360°全景图片
- 避免使用普通照片或非全景图片
- 检查图片没有明显的拼接痕迹
- 确保图片亮度和对比度适中

## 🔧 快速设置命令

如果您使用Windows，可以创建一个批处理文件来快速复制：

```batch
@echo off
echo 正在设置VR720全景图片...

REM 创建目录
mkdir "public\panoramas" 2>nul
mkdir "public\thumbnails" 2>nul

REM 复制图片 (请根据您的实际文件名调整)
copy "F:\BaiduNetdiskDownload\s1234全景效果图\客厅\*.jpg" "public\panoramas\"
copy "F:\BaiduNetdiskDownload\s1234全景效果图\卧室\*.jpg" "public\panoramas\"
copy "F:\BaiduNetdiskDownload\s1234全景效果图\厨房\*.jpg" "public\panoramas\"
copy "F:\BaiduNetdiskDownload\s1234全景效果图\浴室\*.jpg" "public\panoramas\"
copy "F:\BaiduNetdiskDownload\s1234全景效果图\餐厅\*.jpg" "public\panoramas\"
copy "F:\BaiduNetdiskDownload\s1234全景效果图\书房\*.jpg" "public\panoramas\"
copy "F:\BaiduNetdiskDownload\s1234全景效果图\外景\*.jpg" "public\panoramas\"

echo 图片复制完成！
echo 请手动重命名图片文件，并创建缩略图。
pause
```

## 📝 重命名建议

### 客厅图片重命名：
```
原文件名 → 新文件名
现代客厅1.jpg → living-room-modern-1.jpg
豪华客厅2.jpg → living-room-luxury-2.jpg
简约客厅3.jpg → living-room-minimal-3.jpg
```

### 卧室图片重命名：
```
主卧室1.jpg → bedroom-master-1.jpg
客房2.jpg → bedroom-guest-2.jpg
儿童房3.jpg → bedroom-kids-3.jpg
```

### 厨房图片重命名：
```
现代厨房1.jpg → kitchen-modern-1.jpg
开放式厨房2.jpg → kitchen-open-2.jpg
```

## 🎯 完成后的文件结构

```
vr720-app/
├── public/
│   ├── panoramas/
│   │   ├── living-room-modern-1.jpg
│   │   ├── living-room-luxury-2.jpg
│   │   ├── bedroom-master-1.jpg
│   │   ├── bedroom-guest-2.jpg
│   │   ├── kitchen-modern-1.jpg
│   │   ├── kitchen-open-2.jpg
│   │   ├── bathroom-master-1.jpg
│   │   ├── dining-formal-1.jpg
│   │   ├── office-home-1.jpg
│   │   ├── exterior-entrance-1.jpg
│   │   └── exterior-garden-2.jpg
│   └── thumbnails/
│       ├── living-room-modern-1-thumb.jpg
│       ├── living-room-luxury-2-thumb.jpg
│       ├── bedroom-master-1-thumb.jpg
│       └── ... (对应的缩略图)
```

## 🚀 测试设置

设置完成后，重启开发服务器：
```bash
npm run dev
```

然后访问 `http://localhost:3000/mobile-en` 查看效果。

## 💡 提示

1. **选择最佳图片**：从每个房间类型中选择质量最好、最有代表性的全景图
2. **检查比例**：确保选择的是2:1比例的等距柱状投影图片
3. **测试效果**：设置后在VR浏览器中测试，确保360°旋转效果良好
4. **优化大小**：如果图片太大，可以适当压缩以提高加载速度

完成这些步骤后，您的VR720应用就会使用真实的高质量全景图片了！
