// 单个房屋API路由

import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, getDefaultConfig } from '@/lib/database/factory';

// GET /api/houses/[id] - 获取单个房屋详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const includeRooms = searchParams.get('includeRooms') === 'true';
    const incrementView = searchParams.get('incrementView') === 'true';

    const db = await getDatabase(getDefaultConfig());

    // 增加浏览次数
    if (incrementView) {
      await (db.houses as any).incrementViewCount(id);
    }

    let house;
    if (includeRooms) {
      house = await db.findHouseWithRooms(id);
    } else {
      house = await db.houses.findById(id);
    }

    if (!house) {
      return NextResponse.json(
        { success: false, error: 'House not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: house
    });
  } catch (error) {
    console.error('Error fetching house:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch house' },
      { status: 500 }
    );
  }
}

// PUT /api/houses/[id] - 更新房屋
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const db = await getDatabase(getDefaultConfig());

    // 检查房屋是否存在
    const existingHouse = await db.houses.findById(id);
    if (!existingHouse) {
      return NextResponse.json(
        { success: false, error: 'House not found' },
        { status: 404 }
      );
    }

    // 更新房屋
    const updatedHouse = await db.houses.update(id, body);

    // 如果有房间更新
    if (body.rooms && Array.isArray(body.rooms)) {
      // 获取现有房间
      const existingRooms = await db.rooms.findMany({ houseId: id } as any);
      const existingRoomIds = existingRooms.map(room => room.id);

      for (const roomData of body.rooms) {
        if (roomData.id && existingRoomIds.includes(roomData.id)) {
          // 更新现有房间
          await db.rooms.update(roomData.id, {
            name: roomData.name,
            type: roomData.type,
            description: roomData.description,
            area: roomData.area,
            floor: roomData.floor,
            position: roomData.position,
            panoramaImageId: roomData.panoramaImageId,
            thumbnailImageId: roomData.thumbnailImageId,
            features: roomData.features || [],
            metadata: roomData.metadata || {}
          });
        } else {
          // 创建新房间
          await db.rooms.create({
            houseId: id,
            name: roomData.name,
            type: roomData.type,
            description: roomData.description,
            area: roomData.area,
            floor: roomData.floor,
            position: roomData.position,
            panoramaImageId: roomData.panoramaImageId,
            thumbnailImageId: roomData.thumbnailImageId,
            features: roomData.features || [],
            metadata: roomData.metadata || {}
          });
        }
      }

      // 删除不再存在的房间
      const newRoomIds = body.rooms.filter((r: any) => r.id).map((r: any) => r.id);
      const roomsToDelete = existingRoomIds.filter(id => !newRoomIds.includes(id));
      
      for (const roomId of roomsToDelete) {
        await db.rooms.delete(roomId);
      }
    }

    // 返回更新后的完整房屋数据
    const completeHouse = await db.findHouseWithRooms(id);

    return NextResponse.json({
      success: true,
      data: completeHouse
    });
  } catch (error) {
    console.error('Error updating house:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update house' },
      { status: 500 }
    );
  }
}

// DELETE /api/houses/[id] - 删除房屋
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const db = await getDatabase(getDefaultConfig());

    // 检查房屋是否存在
    const existingHouse = await db.houses.findById(id);
    if (!existingHouse) {
      return NextResponse.json(
        { success: false, error: 'House not found' },
        { status: 404 }
      );
    }

    // 删除房屋（级联删除房间和热点）
    const deleted = await db.houses.delete(id);

    if (!deleted) {
      return NextResponse.json(
        { success: false, error: 'Failed to delete house' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'House deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting house:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete house' },
      { status: 500 }
    );
  }
}
