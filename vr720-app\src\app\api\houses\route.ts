// 房屋API路由

import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, getDefaultConfig } from '@/lib/database/factory';
import { House } from '@/lib/database/types';

// GET /api/houses - 获取房屋列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const propertyType = searchParams.get('propertyType');
    const isPublic = searchParams.get('isPublic');
    const userId = searchParams.get('userId');

    const offset = (page - 1) * limit;
    const db = await getDatabase(getDefaultConfig());

    let houses: House[];
    let total: number;

    if (search) {
      // 搜索房屋
      houses = await db.searchHouses(search, { limit, offset });
      total = houses.length; // 简化实现，实际应该有单独的计数查询
    } else {
      // 构建过滤条件
      const filter: Partial<House> = {};
      if (status) filter.status = status as any;
      if (propertyType) filter.propertyType = propertyType as any;
      if (isPublic !== null) filter.isPublic = isPublic === 'true';
      if (userId) filter.userId = userId;

      houses = await db.houses.findMany(filter, { 
        limit, 
        offset, 
        orderBy: 'updatedAt', 
        orderDirection: 'DESC' 
      });
      total = await db.houses.count(filter);
    }

    return NextResponse.json({
      success: true,
      data: houses,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching houses:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch houses' },
      { status: 500 }
    );
  }
}

// POST /api/houses - 创建新房屋
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const db = await getDatabase(getDefaultConfig());

    // 验证必需字段
    if (!body.name || !body.address) {
      return NextResponse.json(
        { success: false, error: 'Name and address are required' },
        { status: 400 }
      );
    }

    // 创建房屋数据
    const houseData: Omit<House, 'id' | 'createdAt' | 'updatedAt'> = {
      name: body.name,
      address: body.address,
      description: body.description || '',
      price: body.price || 0,
      currency: body.currency || 'USD',
      totalArea: body.totalArea || 0,
      bedrooms: body.bedrooms || 0,
      bathrooms: body.bathrooms || 0,
      propertyType: body.propertyType || 'single-family',
      status: body.status || 'draft',
      features: body.features || [],
      userId: body.userId,
      agentId: body.agentId,
      isPublic: body.isPublic || false,
      viewCount: 0,
      metadata: body.metadata || {}
    };

    const house = await db.houses.create(houseData);

    // 如果有房间数据，一并创建
    if (body.rooms && Array.isArray(body.rooms)) {
      for (const roomData of body.rooms) {
        await db.rooms.create({
          houseId: house.id,
          name: roomData.name,
          type: roomData.type,
          description: roomData.description,
          area: roomData.area,
          floor: roomData.floor,
          position: roomData.position,
          panoramaImageId: roomData.panoramaImageId,
          thumbnailImageId: roomData.thumbnailImageId,
          features: roomData.features || [],
          metadata: roomData.metadata || {}
        });
      }
    }

    // 返回完整的房屋数据（包含房间）
    const completeHouse = await db.findHouseWithRooms(house.id);

    return NextResponse.json({
      success: true,
      data: completeHouse
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating house:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create house' },
      { status: 500 }
    );
  }
}

// PUT /api/houses - 批量更新房屋
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { ids, updates } = body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, error: 'House IDs are required' },
        { status: 400 }
      );
    }

    const db = await getDatabase(getDefaultConfig());
    const updatedHouses: House[] = [];

    // 批量更新
    for (const id of ids) {
      try {
        const updated = await db.houses.update(id, updates);
        updatedHouses.push(updated);
      } catch (error) {
        console.error(`Failed to update house ${id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedHouses,
      updated: updatedHouses.length,
      total: ids.length
    });
  } catch (error) {
    console.error('Error updating houses:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update houses' },
      { status: 500 }
    );
  }
}

// DELETE /api/houses - 批量删除房屋
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get('ids');
    
    if (!idsParam) {
      return NextResponse.json(
        { success: false, error: 'House IDs are required' },
        { status: 400 }
      );
    }

    const ids = idsParam.split(',');
    const db = await getDatabase(getDefaultConfig());
    let deletedCount = 0;

    // 批量删除
    for (const id of ids) {
      try {
        const deleted = await db.houses.delete(id);
        if (deleted) deletedCount++;
      } catch (error) {
        console.error(`Failed to delete house ${id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      deleted: deletedCount,
      total: ids.length
    });
  } catch (error) {
    console.error('Error deleting houses:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete houses' },
      { status: 500 }
    );
  }
}
