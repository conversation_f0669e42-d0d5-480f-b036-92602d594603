// 图片API路由

import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { randomUUID } from 'crypto';
import { getDatabase, getDefaultConfig } from '@/lib/database/factory';
import { Image } from '@/lib/database/types';

// 确保上传目录存在
const ensureUploadDir = async (dir: string) => {
  if (!existsSync(dir)) {
    await mkdir(dir, { recursive: true });
  }
};

// GET /api/images - 获取图片列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const tags = searchParams.get('tags');
    const mimeType = searchParams.get('mimeType');
    const isPublic = searchParams.get('isPublic');
    const userId = searchParams.get('userId');
    const panoramicOnly = searchParams.get('panoramicOnly') === 'true';

    const offset = (page - 1) * limit;
    const db = await getDatabase(getDefaultConfig());

    let images: Image[];
    let total: number;

    if (search) {
      // 搜索图片
      images = await (db.images as any).searchImages(search, { limit, offset });
      total = images.length;
    } else if (tags) {
      // 按标签查找
      const tagArray = tags.split(',');
      images = await (db.images as any).findImagesByTags(tagArray, { limit, offset });
      total = images.length;
    } else if (panoramicOnly) {
      // 只查找全景图片
      images = await (db.images as any).findPanoramicImages({ limit, offset });
      total = images.length;
    } else {
      // 构建过滤条件
      const filter: Partial<Image> = {};
      if (mimeType) filter.mimeType = mimeType;
      if (isPublic !== null) filter.isPublic = isPublic === 'true';
      if (userId) filter.userId = userId;

      images = await db.images.findMany(filter, { 
        limit, 
        offset, 
        orderBy: 'createdAt', 
        orderDirection: 'DESC' 
      });
      total = await db.images.count(filter);
    }

    return NextResponse.json({
      success: true,
      data: images,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching images:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch images' },
      { status: 500 }
    );
  }
}

// POST /api/images - 上传图片
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const userId = formData.get('userId') as string;
    const tags = formData.get('tags') as string;
    const isPublic = formData.get('isPublic') === 'true';

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      );
    }

    // 验证文件大小（100MB限制）
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, error: 'File too large. Maximum size is 100MB.' },
        { status: 400 }
      );
    }

    // 生成文件名和路径
    const fileId = randomUUID();
    const fileExtension = path.extname(file.name);
    const fileName = `${fileId}${fileExtension}`;
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'images');
    const filePath = path.join(uploadDir, fileName);
    const relativePath = `/uploads/images/${fileName}`;

    // 确保上传目录存在
    await ensureUploadDir(uploadDir);

    // 保存文件
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // 获取图片尺寸（简化实现，实际应该使用图片处理库）
    let width: number | undefined;
    let height: number | undefined;
    let aspectRatio: number | undefined;

    try {
      // 这里应该使用sharp或其他图片处理库来获取真实尺寸
      // 暂时使用文件大小作为估算
      if (file.size > 1024 * 1024) { // 大于1MB，可能是高分辨率图片
        width = 4096;
        height = 2048;
        aspectRatio = 2.0;
      }
    } catch (error) {
      console.warn('Failed to get image dimensions:', error);
    }

    // 保存到数据库
    const db = await getDatabase(getDefaultConfig());
    
    const imageData: Omit<Image, 'id' | 'createdAt' | 'updatedAt'> = {
      name: file.name,
      originalName: file.name,
      size: file.size,
      mimeType: file.type,
      width,
      height,
      aspectRatio,
      filePath: relativePath,
      metadata: {
        captureDate: new Date(),
        processingInfo: {
          stitched: false,
          enhanced: false,
          compressed: false
        }
      },
      userId: userId || undefined,
      tags: tags ? tags.split(',').map(tag => tag.trim()) : [],
      isPublic
    };

    const image = await db.images.create(imageData);

    return NextResponse.json({
      success: true,
      data: image
    }, { status: 201 });
  } catch (error) {
    console.error('Error uploading image:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload image' },
      { status: 500 }
    );
  }
}

// PUT /api/images - 批量更新图片
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { ids, updates } = body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Image IDs are required' },
        { status: 400 }
      );
    }

    const db = await getDatabase(getDefaultConfig());
    const updatedImages: Image[] = [];

    // 批量更新
    for (const id of ids) {
      try {
        const updated = await db.images.update(id, updates);
        updatedImages.push(updated);
      } catch (error) {
        console.error(`Failed to update image ${id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedImages,
      updated: updatedImages.length,
      total: ids.length
    });
  } catch (error) {
    console.error('Error updating images:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update images' },
      { status: 500 }
    );
  }
}

// DELETE /api/images - 批量删除图片
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get('ids');
    
    if (!idsParam) {
      return NextResponse.json(
        { success: false, error: 'Image IDs are required' },
        { status: 400 }
      );
    }

    const ids = idsParam.split(',');
    const db = await getDatabase(getDefaultConfig());
    let deletedCount = 0;

    // 批量删除
    for (const id of ids) {
      try {
        // 获取图片信息以删除文件
        const image = await db.images.findById(id);
        if (image) {
          // 删除数据库记录
          const deleted = await db.images.delete(id);
          if (deleted) {
            deletedCount++;
            
            // 删除文件（可选，可能需要保留文件用于恢复）
            try {
              const fs = require('fs').promises;
              const fullPath = path.join(process.cwd(), 'public', image.filePath);
              await fs.unlink(fullPath);
            } catch (fileError) {
              console.warn(`Failed to delete file for image ${id}:`, fileError);
            }
          }
        }
      } catch (error) {
        console.error(`Failed to delete image ${id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      deleted: deletedCount,
      total: ids.length
    });
  } catch (error) {
    console.error('Error deleting images:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete images' },
      { status: 500 }
    );
  }
}
