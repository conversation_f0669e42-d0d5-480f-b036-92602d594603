// 数据库测试API

import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, getDefaultConfig } from '@/lib/database/factory';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing database connection...');
    
    // 获取数据库实例
    const db = await getDatabase(getDefaultConfig());
    
    // 测试基本操作
    const userCount = await db.users.count();
    const houseCount = await db.houses.count();
    const imageCount = await db.images.count();
    
    console.log('Database test successful');
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      stats: {
        users: userCount,
        houses: houseCount,
        images: imageCount
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Database test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
