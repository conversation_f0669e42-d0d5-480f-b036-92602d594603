'use client';

import React, { useState } from 'react';
import { Navigation, Home, ArrowLeft, Play, MapPin, Info } from 'lucide-react';
import { GuidedVRViewer, createSampleVRTour } from '@/components/VR/GuidedVRViewer';

export default function GuidedTourPage() {
  const [showGuidedTour, setShowGuidedTour] = useState(false);

  const tourScenes = createSampleVRTour();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="flex items-center">
          <button
            onClick={() => window.history.back()}
            className="mr-3 p-2 hover:bg-gray-100 rounded-full"
          >
            <ArrowLeft size={20} />
          </button>
          <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
            <Navigation size={20} className="text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">Guided VR Tour</h1>
            <p className="text-sm text-gray-600">Interactive property navigation</p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl shadow-lg p-8 text-white mb-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Home size={32} className="text-white" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Interactive Property Tour</h2>
            <p className="text-blue-100 mb-6">
              Experience a guided walkthrough with navigation hotspots, room information, and interactive features
            </p>
            <button
              onClick={() => setShowGuidedTour(true)}
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-all duration-200 active:scale-95 flex items-center mx-auto"
            >
              <Play size={20} className="mr-2" />
              Start Guided Tour
            </button>
          </div>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <Navigation size={24} className="text-blue-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Smart Navigation</h3>
            <p className="text-sm text-gray-600">
              Interactive hotspots guide you through each room with seamless transitions
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <Info size={24} className="text-green-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Rich Information</h3>
            <p className="text-sm text-gray-600">
              Detailed descriptions and features for each room and area
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <MapPin size={24} className="text-purple-600" />
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Property Map</h3>
            <p className="text-sm text-gray-600">
              Mini-map showing your current location and available destinations
            </p>
          </div>
        </div>

        {/* Tour Preview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="font-semibold text-gray-900 mb-4">Tour Preview</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {tourScenes.map((scene, index) => (
              <div key={scene.id} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-lg flex items-center justify-center mb-2 mx-auto">
                  <span className="text-2xl">
                    {index === 0 ? '🚪' : 
                     index === 1 ? '🛋️' : 
                     index === 2 ? '🍳' : 
                     index === 3 ? '🛏️' : '🛁'}
                  </span>
                </div>
                <h4 className="text-xs font-medium text-gray-900">{scene.name}</h4>
                <p className="text-xs text-gray-500">{scene.hotspots.length} hotspots</p>
              </div>
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">🎮 How to Navigate</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• <strong>Look around:</strong> Drag to explore the 360° view</li>
            <li>• <strong>Navigate:</strong> Click on blue hotspots to move between rooms</li>
            <li>• <strong>Get info:</strong> Click on info hotspots for room details</li>
            <li>• <strong>Auto tour:</strong> Use the play button for automatic navigation</li>
            <li>• <strong>Mini map:</strong> Toggle the map to see your location</li>
            <li>• <strong>VR mode:</strong> Click the VR icon for immersive experience</li>
          </ul>
        </div>

        {/* Technical Features */}
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-800 mb-2">🔧 Technical Features</h4>
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <div className="font-medium text-gray-800">Navigation System</div>
              <ul className="space-y-1">
                <li>• Interactive hotspots</li>
                <li>• Scene transitions</li>
                <li>• Progress tracking</li>
              </ul>
            </div>
            <div>
              <div className="font-medium text-gray-800">VR Technology</div>
              <ul className="space-y-1">
                <li>• WebXR support</li>
                <li>• A-Frame rendering</li>
                <li>• Mobile optimization</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Guided VR Tour */}
      {showGuidedTour && (
        <GuidedVRViewer
          scenes={tourScenes}
          title="Interactive Property Tour"
          onClose={() => setShowGuidedTour(false)}
        />
      )}
    </div>
  );
}
