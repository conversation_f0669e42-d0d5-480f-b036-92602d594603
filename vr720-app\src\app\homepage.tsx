'use client';

import React, { useState, useEffect } from 'react';
import { Camera, Eye, Settings, ArrowLeft, Play, Building, Home, Upload, Search, MapPin, TrendingUp, Users, Award, Star, ChevronRight, Menu, Bell, User, Smartphone } from 'lucide-react';
import { useRouter } from 'next/navigation';
import CameraCapture from '@/components/Camera/CameraCapture';
import { MobileCameraAccess } from '@/components/Camera/MobileCameraAccess';
import PanoramaStitcher from '@/components/Panorama/PanoramaStitcher';
import VRViewer from '@/components/VR/VRViewer';
import { demoPhotos, generateDemoPanorama } from '@/components/Demo/DemoData';
import RoomTypeSelector, { RoomType } from '@/components/RealEstate/RoomTypeSelector';
import RealEstateCameraCapture from '@/components/RealEstate/RealEstateCameraCapture';
import AdvancedPanoramaStitcher from '@/components/RealEstate/AdvancedPanoramaStitcher';
import HouseTourViewer from '@/components/RealEstate/HouseTourViewer';
import EnhancedHouseTourViewer from '@/components/RealEstate/EnhancedHouseTourViewer';
import HouseConfigurationManager from '@/components/RealEstate/HouseConfigurationManager';
import { demoHouses } from '@/components/Demo/DemoHouseData';
import { enhancedDemoHouses, defaultEnhancedHouse } from '@/components/Demo/EnhancedDemoHouseData';
import { House } from '@/components/RealEstate/HouseStructureManager';
import ImageGalleryViewer from '@/components/Upload/ImageGalleryViewer';

type AppMode = 'home' | 'room-select' | 'capture' | 'stitch' | 'view' | 'house-tour' | 'upload' | 'house-config';

export default function VR720RealtyApp() {
  const router = useRouter();
  const [currentMode, setCurrentMode] = useState<AppMode>('home');

  // 检测移动设备并重定向
  useEffect(() => {
    const checkMobileAndRedirect = () => {
      const userAgent = navigator.userAgent;
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
      const isSmallScreen = window.innerWidth <= 768;

      if (isMobile || isSmallScreen) {
        // 移动设备重定向到移动端页面
        router.push('/mobile');
      }
    };

    checkMobileAndRedirect();
    window.addEventListener('resize', checkMobileAndRedirect);

    return () => window.removeEventListener('resize', checkMobileAndRedirect);
  }, [router]);
  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const [panoramaUrl, setPanoramaUrl] = useState<string>('');
  const [selectedRoomType, setSelectedRoomType] = useState<RoomType>('living-room');
  const [useRealEstateMode, setUseRealEstateMode] = useState(true);
  const [captureMode, setCaptureMode] = useState<'single' | 'panoramic'>('panoramic');
  const [currentHouse, setCurrentHouse] = useState(demoHouses[0]);
  const [currentEnhancedHouse, setCurrentEnhancedHouse] = useState(enhancedDemoHouses[0]);
  const [tourStartingRoom, setTourStartingRoom] = useState<string>('');
  const [tourStartingViewPoint, setTourStartingViewPoint] = useState<string>('');
  const [configuredHouse, setConfiguredHouse] = useState<any>(null);

  // Start real estate capture workflow
  const startRealEstateCapture = () => {
    setUseRealEstateMode(true);
    setCurrentMode('room-select');
  };

  // Start house tour
  const startHouseTour = (roomId?: string, viewPointId?: string) => {
    setTourStartingRoom(roomId ?? currentEnhancedHouse.rooms[0]?.id ?? '');
    setTourStartingViewPoint(viewPointId ?? currentEnhancedHouse.rooms[0]?.viewPoints[0]?.id ?? '');
    setCurrentMode('house-tour');
  };

  // Start tour from house configuration manager
  const startConfiguredHouseTour = (house: any, startingRoomId?: string) => {
    setConfiguredHouse(house);
    setTourStartingRoom(startingRoomId || house.rooms[0]?.id || '');
    setCurrentMode('house-tour');
  };

  // Start demo
  const startDemo = () => {
    const demoPanorama = generateDemoPanorama();
    setPanoramaUrl(demoPanorama);
    setCurrentMode('view');
  };

  // Reset application
  const resetApp = () => {
    setCapturedPhotos([]);
    setPanoramaUrl('');
    setCurrentMode('home');
  };

  // Render professional real estate homepage
  const renderHomePage = () => {
    return (
    <div className="min-h-screen bg-white">
      {/* Top navigation bar */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Building size={24} className="text-white" />
                </div>
                <div className="ml-3">
                  <h1 className="text-xl font-bold text-gray-900">VR720 Realty</h1>
                  <p className="text-xs text-gray-500">Professional VR Solutions</p>
                </div>
              </div>
            </div>

            {/* Right menu */}
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Search size={20} />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Bell size={20} />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <User size={20} />
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main content area */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome banner */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold mb-2">Welcome to VR720 Realty</h2>
              <p className="text-blue-100 text-lg mb-4">
                Transform your real estate business with immersive virtual reality experiences
              </p>
              <div className="flex items-center space-x-6 text-sm">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  <span>Professional Grade</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                  <span>AI-Powered</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                  <span>Cloud Sync</span>
                </div>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="w-32 h-32 bg-white/10 rounded-full flex items-center justify-center">
                <Building size={64} className="text-white/80" />
              </div>
            </div>
          </div>
        </div>

        {/* Statistics cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Home size={24} className="text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Listings</p>
                <p className="text-2xl font-bold text-gray-900">1,247</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <Eye size={24} className="text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">VR Tours</p>
                <p className="text-2xl font-bold text-gray-900">3,892</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Camera size={24} className="text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Photos Captured</p>
                <p className="text-2xl font-bold text-gray-900">15,634</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-3 bg-orange-100 rounded-lg">
                <TrendingUp size={24} className="text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Engagement</p>
                <p className="text-2xl font-bold text-gray-900">+24%</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main feature area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left side - Main operations */}
          <div className="lg:col-span-2">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Professional Tools</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Professional photography */}
              <button
                onClick={startRealEstateCapture}
                className="group bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md hover:border-blue-300 transition-all duration-200 text-left"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                    <Camera size={24} className="text-blue-600" />
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                    Professional
                  </span>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Professional Capture</h4>
                <p className="text-gray-600 text-sm">
                  AI-guided photography with real-time quality assessment for perfect shots every time.
                </p>
              </button>

              {/* Panorama stitching */}
              <button
                onClick={() => setCurrentMode('stitch')}
                className="group bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md hover:border-purple-300 transition-all duration-200 text-left"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                    <Settings size={24} className="text-purple-600" />
                  </div>
                  <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full font-medium">
                    AI-Powered
                  </span>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Panorama Stitching</h4>
                <p className="text-gray-600 text-sm">
                  Advanced AI algorithms create seamless 4K/8K panoramic images from multiple photos.
                </p>
              </button>

              {/* VR tour */}
              <button
                onClick={() => startHouseTour()}
                className="group bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md hover:border-green-300 transition-all duration-200 text-left"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors">
                    <Eye size={24} className="text-green-600" />
                  </div>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                    Immersive
                  </span>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Virtual Tours</h4>
                <p className="text-gray-600 text-sm">
                  Create immersive 360° virtual reality experiences that engage potential buyers.
                </p>
              </button>

              {/* Property configuration */}
              <button
                onClick={() => setCurrentMode('house-config')}
                className="group bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md hover:border-indigo-300 transition-all duration-200 text-left"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-indigo-100 rounded-lg group-hover:bg-indigo-200 transition-colors">
                    <Building size={24} className="text-indigo-600" />
                  </div>
                  <span className="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full font-medium">
                    Smart
                  </span>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Property Manager</h4>
                <p className="text-gray-600 text-sm">
                  Configure room layouts, manage floor plans, and organize virtual property tours.
                </p>
              </button>
            </div>
          </div>

          {/* Right side - Quick actions and information */}
          <div className="space-y-6">
            {/* Quick upload */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Quick Upload</h4>
              <button
                onClick={() => setCurrentMode('upload')}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <Upload size={20} className="mr-2" />
                Upload Panoramic Images
              </button>
              <p className="text-xs text-gray-500 mt-2 text-center">
                Drag & drop or browse to upload your 360° photos
              </p>
            </div>

            {/* Recent activity */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h4>
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                  <span className="text-gray-600">New VR tour created</span>
                </div>
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                  <span className="text-gray-600">5 photos processed</span>
                </div>
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                  <span className="text-gray-600">Property layout updated</span>
                </div>
              </div>
            </div>

            {/* Demo functionality */}
            <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Try Demo</h4>
              <p className="text-gray-600 text-sm mb-4">
                Experience our VR technology with sample properties.
              </p>
              <button
                onClick={startDemo}
                className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                <Play size={16} className="inline mr-2" />
                Start Demo Tour
              </button>
            </div>

            {/* Help and support */}
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Need Help?</h4>
              <p className="text-gray-600 text-sm mb-4">
                Get started with our comprehensive guides and tutorials.
              </p>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                View Documentation →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    );
  };

  // Main rendering logic
  switch (currentMode) {
    case 'room-select':
      return (
        <RoomTypeSelector
          onRoomSelect={(roomType) => {
            setSelectedRoomType(roomType);
            setCurrentMode('capture');
          }}
          onBack={() => setCurrentMode('home')}
        />
      );

    case 'capture':
      if (useRealEstateMode) {
        return (
          <RealEstateCameraCapture
            roomType={selectedRoomType}
            onPhotoCapture={(photoData, metadata) => {
              setCapturedPhotos(prev => [...prev, { data: photoData, metadata }]);
              // You can add logic here to handle when enough photos are captured
            }}
            onBack={() => setCurrentMode('room-select')}
          />
        );
      } else {
        return (
          <CameraCapture
            mode={captureMode}
            onPhotoCaptured={(photos) => {
              setCapturedPhotos(photos);
              setCurrentMode('stitch');
            }}
            onBack={() => setCurrentMode('home')}
          />
        );
      }

    case 'stitch':
      if (useRealEstateMode) {
        return (
          <AdvancedPanoramaStitcher
            photos={capturedPhotos}
            roomType={selectedRoomType}
            onPanoramaGenerated={(url) => {
              setPanoramaUrl(url);
              setCurrentMode('view');
            }}
            onBack={() => setCurrentMode('capture')}
          />
        );
      } else {
        return (
          <PanoramaStitcher
            photos={capturedPhotos}
            onPanoramaGenerated={(url) => {
              setPanoramaUrl(url);
              setCurrentMode('view');
            }}
            onBack={() => setCurrentMode('capture')}
          />
        );
      }

    case 'view':
      return (
        <VRViewer
          panoramaUrl={panoramaUrl}
          onBack={() => setCurrentMode('home')}
          onReset={resetApp}
        />
      );

    case 'upload':
      return (
        <ImageGalleryViewer
          onBack={() => setCurrentMode('home')}
        />
      );

    case 'house-config':
      return (
        <HouseConfigurationManager
          onBack={() => setCurrentMode('home')}
          onStartTour={startConfiguredHouseTour}
        />
      );

    case 'house-tour': {
      const enhancedHouseToShow = configuredHouse ?? currentEnhancedHouse;
      return (
        <EnhancedHouseTourViewer
          house={enhancedHouseToShow}
          startingRoomId={tourStartingRoom}
          startingViewPointId={tourStartingViewPoint}
          onBack={() => setCurrentMode(configuredHouse ? 'house-config' : 'home')}
          onRoomChange={(roomId, viewPointId) => {
            setTourStartingRoom(roomId);
            setTourStartingViewPoint(viewPointId ?? '');
          }}
        />
      );
    }

    default:
      return renderHomePage();
  }
}
