'use client';

import React, { useState } from 'react';
import { 
  Home, 
  Eye, 
  Navigation, 
  Settings, 
  Play,
  ArrowRight,
  CheckCircle,
  Info
} from 'lucide-react';
import HouseVRTour, { House as VRHouse } from '@/components/VR/HouseVRTour';

const HouseTourDemo: React.FC = () => {
  const [showDemo, setShowDemo] = useState(false);

  // 演示房屋数据
  const demoHouse: VRHouse = {
    id: 'demo-house',
    name: '全屋漫游演示',
    address: '演示地址',
    description: '这是一个全屋漫游功能的演示项目',
    startRoomId: 'living-room',
    rooms: [
      {
        id: 'living-room',
        name: '客厅',
        type: 'living_room',
        panoramaUrl: '/panoramas/living-room-modern-1.jpg',
        description: '现代化客厅，宽敞明亮',
        area: 45,
        hotSpots: [
          {
            id: 'to-kitchen',
            x: 0.3,
            y: 0.6,
            targetRoomId: 'kitchen',
            title: '前往厨房',
            description: '通往开放式厨房'
          },
          {
            id: 'to-bedroom',
            x: 0.7,
            y: 0.5,
            targetRoomId: 'bedroom',
            title: '前往卧室',
            description: '通往主卧室'
          }
        ]
      },
      {
        id: 'kitchen',
        name: '厨房',
        type: 'kitchen',
        panoramaUrl: '/panoramas/kitchen-modern-1.jpg',
        description: '现代化开放式厨房',
        area: 25,
        hotSpots: [
          {
            id: 'back-to-living',
            x: 0.5,
            y: 0.7,
            targetRoomId: 'living-room',
            title: '返回客厅',
            description: '返回客厅区域'
          }
        ]
      },
      {
        id: 'bedroom',
        name: '卧室',
        type: 'bedroom',
        panoramaUrl: '/panoramas/bedroom-master-1.jpg',
        description: '舒适的主卧室',
        area: 35,
        hotSpots: [
          {
            id: 'back-to-living-2',
            x: 0.4,
            y: 0.8,
            targetRoomId: 'living-room',
            title: '返回客厅',
            description: '返回客厅区域'
          }
        ]
      }
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200 px-4 py-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
              <Home size={24} className="text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">全屋漫游功能演示</h1>
              <p className="text-sm text-gray-600">体验沉浸式VR房屋漫游</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 功能介绍 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">🏠 全屋漫游功能</h2>
          <p className="text-gray-600 mb-6">
            全屋漫游功能采用VR720技术，创建包含多个房间的720度VR体验。用户可以通过3D连接点在不同房间之间自由导航，
            支持720°/360°模式切换，并提供平面图快速导航，为房地产展示提供专业级的沉浸式体验。
          </p>

          {/* 功能特点 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <CheckCircle className="text-green-600 mr-2" size={20} />
                核心功能
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  VR720°/360°双模式展示
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  3D连接点导航系统
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  平面图快速房间切换
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  A-Frame VR引擎驱动
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Settings className="text-blue-600 mr-2" size={20} />
                管理功能
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  可视化3D连接点编辑器
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  房间类型和信息管理
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  全景图上传和管理
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  VR720°/360°模式切换
                </li>
              </ul>
            </div>
          </div>

          {/* 演示按钮 */}
          <div className="text-center">
            <button
              onClick={() => setShowDemo(true)}
              className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg text-lg font-medium flex items-center mx-auto transition-colors"
            >
              <Play size={20} className="mr-2" />
              开始演示
            </button>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
            <Info className="text-blue-600 mr-2" size={24} />
            使用说明
          </h2>

          <div className="space-y-6">
            {/* 步骤1 */}
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold flex-shrink-0">
                1
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">创建房屋项目</h3>
                <p className="text-gray-600">
                  在移动端页面点击"New House Tour"按钮，填写房屋基本信息，包括名称、地址和描述。
                </p>
              </div>
            </div>

            {/* 步骤2 */}
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold flex-shrink-0">
                2
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">添加房间</h3>
                <p className="text-gray-600">
                  在房间设置步骤中，添加各个房间并上传对应的360度全景图。设置房间类型、名称和描述信息。
                </p>
              </div>
            </div>

            {/* 步骤3 */}
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold flex-shrink-0">
                3
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">设置3D连接点</h3>
                <p className="text-gray-600">
                  在连接点设置步骤中，点击全景图选择连接点位置，系统会自动转换为3D球面坐标，创建带有动画效果的3D连接点。
                </p>
              </div>
            </div>

            {/* 步骤4 */}
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center font-bold flex-shrink-0">
                4
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">开始VR720°漫游</h3>
                <p className="text-gray-600">
                  保存房屋项目后，点击"全屋漫游"按钮即可开始VR720°体验。支持拖拽旋转视角，点击3D连接球切换房间，使用平面图快速导航。
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 操作指南 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
            <Navigation className="text-purple-600 mr-2" size={24} />
            VR漫游操作指南
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">桌面端操作</h3>
              <ul className="space-y-2 text-gray-600">
                <li>• 鼠标拖拽：旋转VR720°视角</li>
                <li>• 点击3D连接球：切换房间</li>
                <li>• 顶部按钮：720°/360°模式切换</li>
                <li>• 平面图：快速房间导航</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-3">移动端操作</h3>
              <ul className="space-y-2 text-gray-600">
                <li>• 手指拖拽：旋转VR720°视角</li>
                <li>• 点击3D连接球：切换房间</li>
                <li>• 设备陀螺仪：自动视角跟随</li>
                <li>• 底部导航：快速切换房间</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-blue-800 text-sm">
              <strong>VR720°技术提示：</strong> 3D连接球带有动画效果和光环，点击即可前往对应房间。
              支持720°连续旋转模式和360°标准模式切换，平面图提供快速房间导航。
            </p>
          </div>
        </div>

        {/* 返回按钮 */}
        <div className="text-center mt-8">
          <a
            href="/mobile"
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ArrowRight className="mr-2 rotate-180" size={20} />
            返回移动端页面
          </a>
        </div>
      </div>

      {/* VR演示组件 */}
      {showDemo && (
        <HouseVRTour
          house={demoHouse}
          onClose={() => setShowDemo(false)}
        />
      )}
    </div>
  );
};

export default HouseTourDemo;
