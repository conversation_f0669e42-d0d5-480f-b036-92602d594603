'use client';

import React, { useState } from 'react';
import { Camera, Home, Eye, Settings, Smartphone, CheckCircle, AlertCircle, Upload, ArrowLeft } from 'lucide-react';
import { MobileCameraCapture } from '@/components/Camera/MobileCameraCapture';
import { MobileVRViewer } from '@/components/VR/MobileVRViewer';
import { GuidedVRViewer, createSampleVRTour } from '@/components/VR/GuidedVRViewer';

// Helper functions
const getBrowserName = () => {
  const userAgent = navigator.userAgent;
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Edge')) return 'Edge';
  return 'Other';
};

const getPlatformName = () => {
  const userAgent = navigator.userAgent;
  if (/iPad|iPhone|iPod/.test(userAgent)) return 'iOS';
  if (/Android/.test(userAgent)) return 'Android';
  if (/Windows/.test(userAgent)) return 'Windows';
  if (/Mac/.test(userAgent)) return 'macOS';
  return 'Unknown';
};

export default function MobileSimplePage() {
  const [showCamera, setShowCamera] = useState(false);
  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const [message, setMessage] = useState<string>('');
  const [showVRTour, setShowVRTour] = useState(false);
  const [showGuidedTour, setShowGuidedTour] = useState(false);
  const [showUploader, setShowUploader] = useState(false);
  const [selectedPanorama, setSelectedPanorama] = useState('');

  // 全景照片列表
  const panoramaImages = [
    '/panoramas/living-room-modern-1.jpg',
    '/panoramas/living-room-luxury-2.jpg',
    '/panoramas/bedroom-master-1.jpg',
    '/panoramas/kitchen-modern-1.jpg',
    '/panoramas/bathroom-master-1.jpg',
    '/panoramas/bedroom-guest-1.jpg',
    '/panoramas/kitchen-luxury-2.jpg',
    '/panoramas/bathroom-guest-2.jpg'
  ];

  // 随机选择全景照片并启动VR漫游
  const startRandomVRTour = () => {
    const randomIndex = Math.floor(Math.random() * panoramaImages.length);
    const randomPanorama = panoramaImages[randomIndex];
    setSelectedPanorama(randomPanorama);
    setShowVRTour(true);
    setMessage('Starting random VR tour...');
    setTimeout(() => setMessage(''), 2000);
  };

  // Handle photo capture completion
  const handlePhotoCapture = (photoDataUrl: string) => {
    setCapturedPhotos(prev => [...prev, photoDataUrl]);
    setMessage('Photo captured successfully!');
    setShowCamera(false);

    // Clear message after 3 seconds
    setTimeout(() => setMessage(''), 3000);
  };

  // Download photo
  const downloadPhoto = (photoUrl: string, index: number) => {
    const link = document.createElement('a');
    link.download = `vr720-photo-${index + 1}-${Date.now()}.jpg`;
    link.href = photoUrl;
    link.click();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top title bar */}
      <div className="bg-blue-600 text-white p-4 shadow-lg">
        <div className="flex items-center justify-center">
          <Smartphone size={24} className="mr-3" />
          <h1 className="text-xl font-bold">VR720 Mobile</h1>
        </div>
        <p className="text-center text-blue-100 text-sm mt-1">Real Estate VR Capture App</p>
      </div>

      {/* 消息提示 */}
      {message && (
        <div className="mx-4 mt-4 p-3 bg-green-100 border border-green-300 rounded-lg flex items-center">
          <CheckCircle size={20} className="text-green-600 mr-2" />
          <span className="text-green-800">{message}</span>
        </div>
      )}

      {/* Main function area */}
      <div className="p-4 space-y-6">
        {/* Quick capture card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Camera size={32} className="text-blue-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Start Capture</h2>
            <p className="text-gray-600 mb-6">Capture high-quality real estate photos</p>
            <button
              onClick={() => setShowCamera(true)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-4 px-6 rounded-lg text-lg font-medium transition-colors"
            >
              Start Camera
            </button>
          </div>
        </div>

        {/* Function menu */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          {/* VR Tours Button */}
          <button
            onClick={startRandomVRTour}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 active:scale-95"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Eye size={24} className="text-white" />
              </div>
              <h3 className="font-medium text-gray-900">VR Tours</h3>
              <p className="text-sm text-gray-600 mt-1">Random panoramic viewing</p>
            </div>
          </button>

          {/* Property Manage Button */}
          <button
            onClick={() => setShowUploader(true)}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:bg-green-50 hover:border-green-300 transition-all duration-200 active:scale-95"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Upload size={24} className="text-white" />
              </div>
              <h3 className="font-medium text-gray-900">Property Manage</h3>
              <p className="text-sm text-gray-600 mt-1">Upload & stitch photos</p>
            </div>
          </button>
        </div>

        {/* Guided Tour Section */}
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl shadow-lg p-6 text-white mb-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mr-4">
              <Home size={24} className="text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Guided Property Tour</h3>
              <p className="text-blue-100 text-sm">Interactive navigation with hotspots</p>
            </div>
          </div>
          <button
            onClick={() => setShowGuidedTour(true)}
            className="w-full bg-white text-blue-600 py-3 px-4 rounded-lg font-medium hover:bg-blue-50 transition-all duration-200 active:scale-95"
          >
            Start Guided Tour Experience
          </button>
        </div>

        {/* Captured photos */}
        {capturedPhotos.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <h3 className="font-medium text-gray-900 mb-4">
              Captured Photos ({capturedPhotos.length})
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {capturedPhotos.map((photo, index) => (
                <div key={`photo-${index}-${Date.now()}`} className="relative">
                  <img
                    src={photo}
                    alt={`Photo ${index + 1}`}
                    className="w-full h-24 object-cover rounded-lg border border-gray-200"
                  />
                  <button
                    onClick={() => downloadPhoto(photo, index)}
                    className="absolute top-1 right-1 bg-black/50 text-white p-1 rounded text-xs hover:bg-black/70"
                  >
                    Download
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Usage instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-3 flex items-center">
            <AlertCircle size={20} className="mr-2" />
            Usage Instructions
          </h3>
          <ul className="text-sm text-blue-800 space-y-2">
            <li>• Ensure using camera function in HTTPS environment</li>
            <li>• Allow browser to access camera permissions</li>
            <li>• Recommend using rear camera for shooting</li>
            <li>• Keep device stable to ensure clear photos</li>
            <li>• Download and save photos after capture</li>
          </ul>
        </div>

        {/* Device information */}
        <div className="bg-gray-100 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">Device Information</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <div>Browser: {getBrowserName()}</div>
            <div>Platform: {getPlatformName()}</div>
            <div>Connection: {typeof window !== 'undefined' && window.location.protocol === 'https:' ? 'HTTPS ✅' : 'HTTP ⚠️'}</div>
            <div>Camera API: {navigator.mediaDevices?.getUserMedia ? 'Supported ✅' : 'Not Supported ❌'}</div>
          </div>
        </div>
      </div>

      {/* Bottom navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div className="flex justify-around">
          <button className="flex flex-col items-center text-blue-600">
            <Camera size={24} />
            <span className="text-xs mt-1">Capture</span>
          </button>
          <button className="flex flex-col items-center text-gray-600">
            <Home size={24} />
            <span className="text-xs mt-1">Properties</span>
          </button>
          <button className="flex flex-col items-center text-gray-600">
            <Eye size={24} />
            <span className="text-xs mt-1">VR Tours</span>
          </button>
          <button className="flex flex-col items-center text-gray-600">
            <Settings size={24} />
            <span className="text-xs mt-1">Settings</span>
          </button>
        </div>
      </div>

      {/* 相机组件 */}
      {showCamera && (
        <MobileCameraCapture
          onPhotoCapture={handlePhotoCapture}
          onClose={() => setShowCamera(false)}
        />
      )}

      {/* VR Tour Modal */}
      {showVRTour && selectedPanorama && (
        <MobileVRViewer
          panoramaUrl={selectedPanorama}
          title="Random VR Tour"
          description="Exploring a random panoramic property"
          onClose={() => setShowVRTour(false)}
        />
      )}

      {/* Guided VR Tour */}
      {showGuidedTour && (
        <GuidedVRViewer
          scenes={createSampleVRTour()}
          title="Interactive Property Tour"
          onClose={() => setShowGuidedTour(false)}
        />
      )}

      {/* Photo Stitching Uploader */}
      {showUploader && <PhotoStitchingUploader />}

      {/* Bottom spacing */}
      <div className="h-20"></div>
    </div>
  );

  // 照片拼接上传器组件
  function PhotoStitchingUploader() {
    const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
    const [isStitching, setIsStitching] = useState(false);
    const [stitchingProgress, setStitchingProgress] = useState(0);

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      setUploadedFiles(prev => [...prev, ...files]);
    };

    const removeFile = (index: number) => {
      setUploadedFiles(prev => prev.filter((_, i) => i !== index));
    };

    const startStitching = async () => {
      if (uploadedFiles.length < 4) {
        alert('请至少上传4张照片进行全景拼接');
        return;
      }

      setIsStitching(true);
      setStitchingProgress(0);

      // 模拟拼接过程
      const interval = setInterval(() => {
        setStitchingProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            setIsStitching(false);
            alert('全景照片拼接完成！已保存到全景库中。');
            setUploadedFiles([]);
            setShowUploader(false);
            return 100;
          }
          return prev + 10;
        });
      }, 500);
    };

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg w-full max-w-md max-h-[80vh] overflow-y-auto">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Photo Stitching</h2>
              <button
                onClick={() => setShowUploader(false)}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <ArrowLeft size={20} />
              </button>
            </div>
            <p className="text-sm text-gray-600 mt-1">Upload photos to create panoramic VR</p>
          </div>

          {/* Content */}
          <div className="p-4">
            {/* Upload Area */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4">
              <Upload size={32} className="mx-auto text-gray-400 mb-2" />
              <p className="text-sm text-gray-600 mb-2">Upload multiple photos for stitching</p>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                id="photo-upload"
              />
              <label
                htmlFor="photo-upload"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm cursor-pointer hover:bg-blue-700"
              >
                Choose Photos
              </label>
              <p className="text-xs text-gray-500 mt-2">Minimum 4 photos required</p>
            </div>

            {/* Uploaded Files */}
            {uploadedFiles.length > 0 && (
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  Uploaded Photos ({uploadedFiles.length})
                </h3>
                <div className="grid grid-cols-2 gap-2">
                  {uploadedFiles.map((file, index) => (
                    <div key={`${file.name}-${index}`} className="relative">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`Upload ${index + 1}`}
                        className="w-full h-20 object-cover rounded border"
                      />
                      <button
                        onClick={() => removeFile(index)}
                        className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Stitching Progress */}
            {isStitching && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-900">Stitching Progress</span>
                  <span className="text-sm text-gray-600">{stitchingProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${stitchingProgress}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">Processing panoramic image...</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={startStitching}
                disabled={uploadedFiles.length < 4 || isStitching}
                className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium ${
                  uploadedFiles.length >= 4 && !isStitching
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {isStitching ? 'Stitching...' : 'Start Stitching'}
              </button>
              <button
                onClick={() => setUploadedFiles([])}
                disabled={isStitching}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-50"
              >
                Clear All
              </button>
            </div>

            {/* Instructions */}
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-1">Stitching Tips:</h4>
              <ul className="text-xs text-blue-800 space-y-1">
                <li>• Upload 4-8 overlapping photos</li>
                <li>• Take photos in a circular pattern</li>
                <li>• Ensure 30-50% overlap between photos</li>
                <li>• Use consistent lighting and exposure</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
