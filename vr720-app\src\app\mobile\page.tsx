'use client';

import React, { useState, useEffect } from 'react';
import { 
  Camera, 
  Home, 
  Eye, 
  Settings, 
  Plus, 
  Grid,
  MapPin,
  Clock,
  Star,
  ChevronRight,
  Smartphone,
  Play,
  Upload,
  Download,
  Share2
} from 'lucide-react';
import { MobileCameraCapture } from '@/components/Camera/MobileCameraCapture';
import { MobileVRViewer } from '@/components/VR/MobileVRViewer';
import { PhotoUploader } from '@/components/Upload/PhotoUploader';
import { PanoramicGallery } from '@/components/Gallery/PanoramicGallery';
import HouseVRTour, { House as VRHouse, Room } from '@/components/VR/HouseVRTour';
import HouseManager from '@/components/VR/HouseManager';
import MobileOptimizedVR from '@/components/VR/MobileOptimizedVR';


interface House {
  id: string;
  name: string;
  address: string;
  rooms: number;
  photos: number;
  lastUpdated: string;
  thumbnail?: string;
  panoramaUrl?: string;
  hasVRTour?: boolean;
}



export default function MobileEnglishPage() {
  const [showCamera, setShowCamera] = useState(false);
  const [showVRTour, setShowVRTour] = useState(false);
  const [showUploader, setShowUploader] = useState(false);
  const [showGallery, setShowGallery] = useState(false);
  const [selectedPanorama, setSelectedPanorama] = useState<string>('');
  const [showHouseVRTour, setShowHouseVRTour] = useState(false);
  const [showHouseManager, setShowHouseManager] = useState(false);
  const [selectedVRHouse, setSelectedVRHouse] = useState<VRHouse | null>(null);
  const [showMobileOptimizedVR, setShowMobileOptimizedVR] = useState(false);
  const [mobileVRConfig, setMobileVRConfig] = useState<{
    panoramaUrl: string;
    roomName: string;
    houseName: string;
    is720Mode: boolean;
  } | null>(null);
  const [vrHouses, setVRHouses] = useState<VRHouse[]>([
    {
      id: 'demo-house-1',
      name: '现代豪华别墅',
      address: '北京市朝阳区CBD核心区',
      description: '精装修现代别墅，配备智能家居系统',
      startRoomId: 'living-room-1',
      rooms: [
        {
          id: 'living-room-1',
          name: '客厅',
          type: 'living_room',
          panoramaUrl: '/panoramas/living-room-modern-1.jpg',
          description: '宽敞明亮的现代客厅，配备大型落地窗',
          area: 45,
          hotSpots: [
            {
              id: 'hotspot-1',
              x: 0.3,
              y: 0.6,
              targetRoomId: 'kitchen-1',
              title: '前往厨房',
              description: '通往开放式厨房'
            },
            {
              id: 'hotspot-2',
              x: 0.7,
              y: 0.5,
              targetRoomId: 'bedroom-1',
              title: '前往主卧',
              description: '通往主卧室'
            }
          ]
        },
        {
          id: 'kitchen-1',
          name: '厨房',
          type: 'kitchen',
          panoramaUrl: '/panoramas/kitchen-modern-1.jpg',
          description: '现代化开放式厨房，配备高端厨具',
          area: 25,
          hotSpots: [
            {
              id: 'hotspot-3',
              x: 0.5,
              y: 0.7,
              targetRoomId: 'living-room-1',
              title: '返回客厅',
              description: '返回客厅区域'
            },
            {
              id: 'hotspot-4',
              x: 0.8,
              y: 0.4,
              targetRoomId: 'dining-room-1',
              title: '前往餐厅',
              description: '通往餐厅区域'
            }
          ]
        },
        {
          id: 'dining-room-1',
          name: '餐厅',
          type: 'dining_room',
          panoramaUrl: '/panoramas/living-room-luxury-2.jpg',
          description: '优雅的餐厅空间，适合家庭聚餐',
          area: 20,
          hotSpots: [
            {
              id: 'hotspot-5',
              x: 0.2,
              y: 0.6,
              targetRoomId: 'kitchen-1',
              title: '返回厨房',
              description: '返回厨房区域'
            }
          ]
        },
        {
          id: 'bedroom-1',
          name: '主卧室',
          type: 'bedroom',
          panoramaUrl: '/panoramas/bedroom-master-1.jpg',
          description: '宽敞的主卧室，配备独立卫浴',
          area: 35,
          hotSpots: [
            {
              id: 'hotspot-6',
              x: 0.4,
              y: 0.8,
              targetRoomId: 'living-room-1',
              title: '返回客厅',
              description: '返回客厅区域'
            },
            {
              id: 'hotspot-7',
              x: 0.9,
              y: 0.3,
              targetRoomId: 'bathroom-1',
              title: '前往主卫',
              description: '通往主卧卫生间'
            }
          ]
        },
        {
          id: 'bathroom-1',
          name: '主卫生间',
          type: 'bathroom',
          panoramaUrl: '/panoramas/bathroom-master-1.jpg',
          description: '豪华主卫，配备浴缸和独立淋浴',
          area: 15,
          hotSpots: [
            {
              id: 'hotspot-8',
              x: 0.1,
              y: 0.7,
              targetRoomId: 'bedroom-1',
              title: '返回主卧',
              description: '返回主卧室'
            }
          ]
        }
      ]
    }
  ]);
  const [houses, setHouses] = useState<House[]>([
    {
      id: '1',
      name: 'Modern Living Room',
      address: 'Beverly Hills, CA',
      rooms: 5,
      photos: 12,
      lastUpdated: '2 hours ago',
      thumbnail: '/thumbnails/living-room-modern-1-thumb.jpg',
      panoramaUrl: '/panoramas/living-room-modern-1.jpg',
      hasVRTour: true
    },
    {
      id: '2',
      name: 'Luxury Living Room',
      address: 'Manhattan, NY',
      rooms: 4,
      photos: 10,
      lastUpdated: '4 hours ago',
      thumbnail: '/thumbnails/living-room-luxury-2-thumb.jpg',
      panoramaUrl: '/panoramas/living-room-luxury-2.jpg',
      hasVRTour: true
    },
    {
      id: '3',
      name: 'Master Bedroom Suite',
      address: 'San Francisco, CA',
      rooms: 3,
      photos: 8,
      lastUpdated: '1 day ago',
      thumbnail: '/thumbnails/bedroom-master-1-thumb.jpg',
      panoramaUrl: '/panoramas/bedroom-master-1.jpg',
      hasVRTour: true
    },
    {
      id: '4',
      name: 'Guest Bedroom',
      address: 'Los Angeles, CA',
      rooms: 2,
      photos: 6,
      lastUpdated: '1 day ago',
      thumbnail: '/thumbnails/bedroom-guest-2-thumb.jpg',
      panoramaUrl: '/panoramas/bedroom-guest-2.jpg',
      hasVRTour: true
    },
    {
      id: '5',
      name: 'Contemporary Kitchen',
      address: 'Seattle, WA',
      rooms: 4,
      photos: 15,
      lastUpdated: '2 days ago',
      thumbnail: '/thumbnails/kitchen-modern-1-thumb.jpg',
      panoramaUrl: '/panoramas/kitchen-modern-1.jpg',
      hasVRTour: true
    },
    {
      id: '6',
      name: 'Open Concept Kitchen',
      address: 'Portland, OR',
      rooms: 3,
      photos: 12,
      lastUpdated: '2 days ago',
      thumbnail: '/thumbnails/kitchen-open-2-thumb.jpg',
      panoramaUrl: '/panoramas/kitchen-open-2.jpg',
      hasVRTour: true
    },
    {
      id: '7',
      name: 'Master Bathroom',
      address: 'Miami Beach, FL',
      rooms: 6,
      photos: 20,
      lastUpdated: '3 days ago',
      thumbnail: '/thumbnails/bathroom-master-1-thumb.jpg',
      panoramaUrl: '/panoramas/bathroom-master-1.jpg',
      hasVRTour: true
    },
    {
      id: '8',
      name: 'Guest Bathroom',
      address: 'Austin, TX',
      rooms: 4,
      photos: 8,
      lastUpdated: '3 days ago',
      thumbnail: '/thumbnails/bathroom-guest-2-thumb.jpg',
      panoramaUrl: '/panoramas/bathroom-guest-2.jpg',
      hasVRTour: true
    }
  ]);

  const [activeTab, setActiveTab] = useState<'houses' | 'camera' | 'view' | 'settings'>('houses');

  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());

  // Detect mobile device
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent;
      const mobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
      setIsMobile(mobile);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle photo capture
  const handlePhotoCapture = (photoDataUrl: string) => {
    console.log('Photo captured:', photoDataUrl.substring(0, 50) + '...');
    setShowCamera(false);

    // Add new house with captured photo
    const newHouse: House = {
      id: Date.now().toString(),
      name: 'New Property',
      address: 'Location TBD',
      rooms: 1,
      photos: 1,
      lastUpdated: 'Just now',
      thumbnail: photoDataUrl,
      panoramaUrl: photoDataUrl, // Use captured photo as panorama
      hasVRTour: true
    };

    setHouses(prev => [newHouse, ...prev]);
  };

  // Handle photo upload
  const handlePhotosUploaded = (photoUrls: string[]) => {
    console.log('Photos uploaded:', photoUrls.length);
    // Don't close uploader yet, wait for panorama creation
  };

  // Handle panorama creation
  const handlePanoramaCreated = (panoramaUrl: string) => {
    console.log('Panorama created:', panoramaUrl.substring(0, 50) + '...');
    setShowUploader(false);

    // Create new house with the created panorama
    const newHouse: House = {
      id: Date.now().toString(),
      name: 'New 360° Property',
      address: 'Location TBD',
      rooms: Math.ceil(Math.random() * 5) + 1, // Random room count
      photos: 1, // The panorama counts as 1 photo
      lastUpdated: 'Just now',
      thumbnail: panoramaUrl, // Use panorama as thumbnail
      panoramaUrl: panoramaUrl, // The stitched panorama
      hasVRTour: true
    };

    setHouses(prev => [newHouse, ...prev]);
  };

  // 检测设备类型
  const isMobileDevice = () => {
    return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  };

  // Start VR Tour with loading state
  const startVRTour = (panoramaUrl: string) => {
    if (isLoading) return; // Prevent multiple clicks

    setIsLoading(true);
    setSelectedPanorama(panoramaUrl);

    // 检测是否为移动设备，使用优化的VR组件
    if (isMobileDevice()) {
      setMobileVRConfig({
        panoramaUrl,
        roomName: 'VR Tour',
        houseName: 'Property View',
        is720Mode: true
      });
      setTimeout(() => {
        setShowMobileOptimizedVR(true);
        setIsLoading(false);
      }, 300);
    } else {
      // 桌面端使用原有组件
      setTimeout(() => {
        setShowVRTour(true);
        setIsLoading(false);
      }, 300);
    }
  };

  // Start House VR Tour
  const startHouseVRTour = (vrHouse: VRHouse) => {
    if (isLoading) return;

    setIsLoading(true);
    setSelectedVRHouse(vrHouse);

    // 移动设备使用优化版本，桌面设备使用完整版本
    if (isMobileDevice()) {
      // 移动端使用优化的单房间VR，从起始房间开始
      const startRoom = vrHouse.rooms.find(room => room.id === vrHouse.startRoomId) || vrHouse.rooms[0];
      if (startRoom) {
        setMobileVRConfig({
          panoramaUrl: startRoom.panoramaUrl,
          roomName: startRoom.name,
          houseName: vrHouse.name,
          is720Mode: true
        });
        setTimeout(() => {
          setShowMobileOptimizedVR(true);
          setIsLoading(false);
        }, 300);
      }
    } else {
      // 桌面端使用完整的全屋漫游
      setTimeout(() => {
        setShowHouseVRTour(true);
        setIsLoading(false);
      }, 300);
    }
  };

  // Handle house creation/update
  const handleHouseCreated = (vrHouse: VRHouse) => {
    setVRHouses(prev => {
      const existingIndex = prev.findIndex(h => h.id === vrHouse.id);
      if (existingIndex >= 0) {
        // Update existing house
        const updated = [...prev];
        updated[existingIndex] = vrHouse;
        return updated;
      } else {
        // Add new house
        return [vrHouse, ...prev];
      }
    });
  };

  // Quick camera button
  const QuickCameraButton = () => (
    <button
      onClick={() => setShowCamera(true)}
      className="fixed bottom-20 right-4 w-16 h-16 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center z-40 transition-all duration-200 active:scale-95"
    >
      <Camera size={24} />
    </button>
  );

  // House card component
  const HouseCard = ({ house }: { house: House }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Optimized Thumbnail with lazy loading */}
      <div className="h-32 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center relative overflow-hidden">
        {house.thumbnail ? (
          <img
            src={house.thumbnail}
            alt={house.name}
            className={`w-full h-full object-cover transition-opacity duration-300 ${
              loadingImages.has(house.id) ? 'opacity-0' : 'opacity-100'
            }`}
            loading="lazy"
            decoding="async"
            onLoad={() => {
              setLoadingImages(prev => {
                const newSet = new Set(prev);
                newSet.delete(house.id);
                return newSet;
              });
            }}
            onError={() => {
              setLoadingImages(prev => {
                const newSet = new Set(prev);
                newSet.delete(house.id);
                return newSet;
              });
            }}
          />
        ) : (
          <Home size={32} className="text-blue-500" />
        )}

        {/* Loading indicator */}
        {loadingImages.has(house.id) && (
          <div className="absolute inset-0 bg-blue-100/50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        )}

        {/* VR Tour Badge */}
        {house.hasVRTour && (
          <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center z-10">
            <Eye size={12} className="mr-1" />
            VR Tour
          </div>
        )}
      </div>
      
      {/* House info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-medium text-gray-900 text-lg">{house.name}</h3>
          <ChevronRight size={20} className="text-gray-400 mt-1" />
        </div>
        
        <div className="flex items-center text-gray-600 text-sm mb-2">
          <MapPin size={14} className="mr-1" />
          <span>{house.address}</span>
        </div>
        
        <div className="flex items-center justify-between text-sm mb-3">
          <div className="flex items-center space-x-4">
            <span className="flex items-center text-gray-600">
              <Grid size={14} className="mr-1" />
              {house.rooms} rooms
            </span>
            <span className="flex items-center text-gray-600">
              <Camera size={14} className="mr-1" />
              {house.photos} photos
            </span>
          </div>
          <div className="flex items-center text-gray-500">
            <Clock size={14} className="mr-1" />
            <span>{house.lastUpdated}</span>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex space-x-2">
          {house.hasVRTour && house.panoramaUrl && (
            <button
              onClick={() => startVRTour(house.panoramaUrl!)}
              disabled={isLoading}
              className={`flex-1 py-2 px-3 rounded text-sm flex items-center justify-center transition-all duration-200 ${
                isLoading
                  ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white active:scale-95'
              }`}
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Loading...
                </>
              ) : (
                <>
                  <Play size={14} className="mr-1" />
                  VR Tour
                </>
              )}
            </button>
          )}
          <button
            onClick={() => setShowUploader(true)}
            disabled={isLoading}
            className={`flex-1 py-2 px-3 rounded text-sm flex items-center justify-center transition-all duration-200 ${
              isLoading
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 active:scale-95'
            }`}
          >
            <Upload size={14} className="mr-1" />
            Upload
          </button>
        </div>
      </div>
    </div>
  );

  // Bottom navigation
  const BottomNavigation = () => (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-30">
      <div className="flex items-center justify-around">
        <button
          onClick={() => setActiveTab('houses')}
          className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
            activeTab === 'houses' 
              ? 'text-blue-600 bg-blue-50' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Home size={20} />
          <span className="text-xs mt-1">Properties</span>
        </button>
        
        <button
          onClick={() => setActiveTab('camera')}
          className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
            activeTab === 'camera' 
              ? 'text-blue-600 bg-blue-50' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Camera size={20} />
          <span className="text-xs mt-1">Capture</span>
        </button>
        
        <button
          onClick={() => setActiveTab('view')}
          className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
            activeTab === 'view' 
              ? 'text-blue-600 bg-blue-50' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Eye size={20} />
          <span className="text-xs mt-1">VR Tours</span>
        </button>
        
        <button
          onClick={() => setActiveTab('settings')}
          className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
            activeTab === 'settings' 
              ? 'text-blue-600 bg-blue-50' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Settings size={20} />
          <span className="text-xs mt-1">Settings</span>
        </button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Global Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-700 font-medium">Loading VR Tour...</p>
            <p className="text-gray-500 text-sm mt-1">Please wait</p>
          </div>
        </div>
      )}

      {/* Top status bar */}
      <div className="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
              <Smartphone size={20} className="text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">VR720</h1>
              <p className="text-xs text-gray-500">Real Estate VR Capture</p>
            </div>
          </div>
          
          {isMobile && (
            <div className="flex items-center text-green-600 text-sm">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              Mobile Device
            </div>
          )}
        </div>
      </div>

      {/* Main content area */}
      <div className="pb-20 px-4 py-6">
        {activeTab === 'houses' && (
          <div>
            {/* Quick actions */}
            <div className="mb-6">
              <h2 className="text-lg font-medium text-gray-900 mb-3">Quick Actions</h2>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setShowCamera(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg flex items-center justify-center transition-colors"
                >
                  <Camera size={20} className="mr-2" />
                  Capture Now
                </button>
                <button
                  onClick={() => setShowHouseManager(true)}
                  className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg flex items-center justify-center transition-colors"
                >
                  <Plus size={20} className="mr-2" />
                  New House Tour
                </button>
              </div>
            </div>

            {/* VR Houses Section */}
            {vrHouses.length > 0 && (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-lg font-medium text-gray-900">全屋漫游</h2>
                  <span className="text-sm text-gray-500">{vrHouses.length} 个项目</span>
                </div>

                <div className="space-y-3">
                  {vrHouses.map((vrHouse) => (
                    <div key={vrHouse.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h3 className="font-medium text-gray-900">{vrHouse.name}</h3>
                          <p className="text-sm text-gray-600">{vrHouse.address}</p>
                        </div>
                        <div className="text-right">
                          <span className="text-sm text-gray-500">{vrHouse.rooms.length} 个房间</span>
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <button
                          onClick={() => startHouseVRTour(vrHouse)}
                          disabled={isLoading}
                          className={`flex-1 py-2 px-3 rounded text-sm flex items-center justify-center transition-all duration-200 ${
                            isLoading
                              ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                              : 'bg-purple-600 hover:bg-purple-700 text-white active:scale-95'
                          }`}
                        >
                          <Eye size={14} className="mr-1" />
                          全屋漫游
                        </button>
                        <button
                          onClick={() => {
                            setSelectedVRHouse(vrHouse);
                            setShowHouseManager(true);
                          }}
                          disabled={isLoading}
                          className={`flex-1 py-2 px-3 rounded text-sm flex items-center justify-center transition-all duration-200 ${
                            isLoading
                              ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                              : 'bg-gray-100 hover:bg-gray-200 text-gray-700 active:scale-95'
                          }`}
                        >
                          <Settings size={14} className="mr-1" />
                          编辑
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Properties list */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-medium text-gray-900">My Properties</h2>
                <span className="text-sm text-gray-500">{houses.length} projects</span>
              </div>
              
              <div className="space-y-4">
                {houses.map((house) => (
                  <HouseCard key={house.id} house={house} />
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'camera' && (
          <div className="text-center py-12">
            <Camera size={64} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">Camera Function</h3>
            <p className="text-gray-600 mb-6">Capture high-quality real estate photos</p>
            <button
              onClick={() => setShowCamera(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg"
            >
              Start Capture
            </button>
          </div>
        )}

        {activeTab === 'view' && (
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-4">VR Tours Available</h2>

            {/* Create House Tour Button */}
            <div className="mb-6">
              <button
                onClick={() => setShowHouseManager(true)}
                className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-3 px-4 rounded-lg flex items-center justify-center mb-3"
              >
                <Plus size={20} className="mr-2" />
                Create House Tour
              </button>
              <button
                onClick={() => setShowGallery(true)}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-3 px-4 rounded-lg flex items-center justify-center"
              >
                <Grid size={20} className="mr-2" />
                Browse Panoramic Gallery
              </button>
              <p className="text-xs text-gray-500 text-center mt-2">
                Create multi-room VR tours or explore 360° panoramic images
              </p>
            </div>

            {/* House VR Tours */}
            {vrHouses.length > 0 && (
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-900 mb-3">全屋漫游项目</h3>
                <div className="space-y-3">
                  {vrHouses.map((vrHouse) => (
                    <div key={vrHouse.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="font-medium text-gray-900">{vrHouse.name}</h4>
                          <p className="text-sm text-gray-600">{vrHouse.address}</p>
                          <p className="text-xs text-gray-500">{vrHouse.rooms.length} 个房间</p>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => startHouseVRTour(vrHouse)}
                            className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-lg flex items-center text-sm"
                          >
                            <Eye size={14} className="mr-1" />
                            漫游
                          </button>
                          <button
                            onClick={() => {
                              setSelectedVRHouse(vrHouse);
                              setShowHouseManager(true);
                            }}
                            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg flex items-center text-sm"
                          >
                            <Settings size={14} className="mr-1" />
                            编辑
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Single Room Tours */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-3">单房间VR</h3>
              <div className="space-y-4">
                {houses.filter(house => house.hasVRTour).map((house) => (
                  <div key={house.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900">{house.name}</h4>
                        <p className="text-sm text-gray-600">{house.address}</p>
                      </div>
                      <button
                        onClick={() => startVRTour(house.panoramaUrl!)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
                      >
                        <Play size={16} className="mr-1" />
                        View Tour
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-4">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="font-medium text-gray-900 mb-3">App Settings</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Camera Quality</span>
                  <span className="text-blue-600">High Quality</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Auto Save</span>
                  <span className="text-green-600">Enabled</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Cloud Sync</span>
                  <span className="text-gray-500">Not Configured</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quick capture button */}
      {activeTab !== 'camera' && <QuickCameraButton />}

      {/* Bottom navigation */}
      <BottomNavigation />

      {/* Camera component */}
      {showCamera && (
        <MobileCameraCapture
          onPhotoCapture={handlePhotoCapture}
          onClose={() => setShowCamera(false)}
        />
      )}

      {/* VR Tour component */}
      {showVRTour && selectedPanorama && (
        <MobileVRViewer
          panoramaUrl={selectedPanorama}
          title="Property VR Tour"
          description="Mobile-optimized 720° VR experience"
          onClose={() => setShowVRTour(false)}
        />
      )}

      {/* Photo Uploader component */}
      {showUploader && (
        <PhotoUploader
          onPhotosUploaded={handlePhotosUploaded}
          onPanoramaCreated={handlePanoramaCreated}
          onClose={() => setShowUploader(false)}
          maxPhotos={10}
        />
      )}

      {/* Panoramic Gallery component */}
      {showGallery && (
        <PanoramicGallery
          onClose={() => setShowGallery(false)}
          onImageSelect={(panoramaUrl, title) => {
            // Create a new house with the selected panoramic image
            const newHouse: House = {
              id: Date.now().toString(),
              name: title,
              address: 'Gallery Selection',
              rooms: Math.ceil(Math.random() * 5) + 1,
              photos: 1,
              lastUpdated: 'Just now',
              thumbnail: panoramaUrl,
              panoramaUrl: panoramaUrl,
              hasVRTour: true
            };
            setHouses(prev => [newHouse, ...prev]);
          }}
        />
      )}

      {/* House VR Tour component */}
      {showHouseVRTour && selectedVRHouse && (
        <HouseVRTour
          house={selectedVRHouse}
          onClose={() => {
            setShowHouseVRTour(false);
            setSelectedVRHouse(null);
          }}
        />
      )}

      {/* House Manager component */}
      {showHouseManager && (
        <HouseManager
          onClose={() => {
            setShowHouseManager(false);
            setSelectedVRHouse(null);
          }}
          onHouseCreated={handleHouseCreated}
          existingHouse={selectedVRHouse || undefined}
        />
      )}

      {/* Mobile Optimized VR component */}
      {showMobileOptimizedVR && mobileVRConfig && (
        <MobileOptimizedVR
          panoramaUrl={mobileVRConfig.panoramaUrl}
          roomName={mobileVRConfig.roomName}
          houseName={mobileVRConfig.houseName}
          is720Mode={mobileVRConfig.is720Mode}
          onClose={() => {
            setShowMobileOptimizedVR(false);
            setMobileVRConfig(null);
          }}
          onToggleMode={() => {
            setMobileVRConfig(prev => prev ? {
              ...prev,
              is720Mode: !prev.is720Mode
            } : null);
          }}
        />
      )}
    </div>
  );
}
