'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Camera, Download, AlertCircle, CheckCircle, Info } from 'lucide-react';

export default function SimpleCameraPage() {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isRequesting, setIsRequesting] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Add log function
  const addLog = useCallback((message: string) => {
    console.log(message);
    setLogs(prev => [...prev.slice(-10), `${new Date().toLocaleTimeString()}: ${message}`]);
  }, []);

  // Simple camera access
  const requestCamera = useCallback(async () => {
    setIsRequesting(true);
    setError('');
    setLogs([]);

    try {
      addLog('Starting camera access...');
      
      // Check if getUserMedia exists
      if (!navigator.mediaDevices?.getUserMedia) {
        // Try legacy APIs
        const legacyGetUserMedia = 
          (navigator as any).getUserMedia ||
          (navigator as any).webkitGetUserMedia ||
          (navigator as any).mozGetUserMedia ||
          (navigator as any).msGetUserMedia;
          
        if (!legacyGetUserMedia) {
          throw new Error('Camera API not supported. Please use Chrome, Firefox, Safari, or Edge.');
        }
        
        addLog('Using legacy getUserMedia API');
        
        // Wrap legacy API in Promise
        const constraints = { video: true, audio: false };
        const stream = await new Promise<MediaStream>((resolve, reject) => {
          legacyGetUserMedia.call(navigator, constraints, resolve, reject);
        });
        
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          await videoRef.current.play();
          setStream(stream);
          setIsStreaming(true);
          addLog('Camera access successful with legacy API');
        }
        return;
      }

      addLog('Modern getUserMedia API available');

      // Check protocol
      const isSecure = location.protocol === 'https:' || 
                      location.hostname === 'localhost' || 
                      location.hostname === '127.0.0.1';
      
      if (!isSecure) {
        addLog(`Warning: Insecure connection (${location.protocol}). Camera may not work.`);
      } else {
        addLog(`Secure connection: ${location.protocol}//${location.hostname}`);
      }

      // Try to enumerate devices
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(d => d.kind === 'videoinput');
        addLog(`Found ${videoDevices.length} video devices`);
        
        if (videoDevices.length === 0) {
          addLog('No video devices found, but will try anyway...');
        }
      } catch (enumError) {
        addLog('Could not enumerate devices (permission needed)');
      }

      // Simple constraints to try
      const constraints = [
        { video: true },
        { video: { width: 640, height: 480 } },
        { video: { facingMode: 'environment' } },
        { video: { facingMode: 'user' } }
      ];

      let cameraStream = null;
      let lastError = null;

      for (let i = 0; i < constraints.length; i++) {
        try {
          addLog(`Trying constraint ${i + 1}: ${JSON.stringify(constraints[i])}`);
          cameraStream = await navigator.mediaDevices.getUserMedia(constraints[i]);
          
          if (cameraStream?.active) {
            addLog(`Success with constraint ${i + 1}`);
            break;
          }
        } catch (err: any) {
          addLog(`Constraint ${i + 1} failed: ${err.message}`);
          lastError = err;
          if (cameraStream) {
            cameraStream.getTracks().forEach(track => track.stop());
          }
        }
      }

      if (!cameraStream?.active) {
        throw lastError || new Error('All camera access attempts failed');
      }

      // Set up video
      if (videoRef.current) {
        videoRef.current.srcObject = cameraStream;
        
        videoRef.current.onloadedmetadata = () => {
          addLog('Video metadata loaded');
          setIsStreaming(true);
        };

        try {
          await videoRef.current.play();
          addLog('Video playback started');
        } catch (playError) {
          addLog('Auto-play failed, user interaction may be required');
        }
      }

      setStream(cameraStream);
      addLog('Camera setup complete');

    } catch (err: any) {
      const errorMessage = err.message || 'Unknown error';
      addLog(`Error: ${errorMessage}`);
      
      let userMessage = 'Camera access failed: ';
      
      if (err.name === 'NotAllowedError') {
        userMessage += 'Permission denied. Please allow camera access and try again.';
      } else if (err.name === 'NotFoundError') {
        userMessage += 'No camera found. Please check if a camera is connected.';
      } else if (err.name === 'NotSupportedError') {
        userMessage += 'Camera not supported on this device/browser.';
      } else if (err.name === 'NotReadableError') {
        userMessage += 'Camera is being used by another application.';
      } else {
        userMessage += errorMessage;
      }
      
      setError(userMessage);
    } finally {
      setIsRequesting(false);
    }
  }, [addLog]);

  // Stop camera
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
      setIsStreaming(false);
      addLog('Camera stopped');
    }
  }, [stream, addLog]);

  // Take photo
  const takePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || !isStreaming) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0);

    const photoDataUrl = canvas.toDataURL('image/jpeg', 0.9);
    setCapturedPhoto(photoDataUrl);
    addLog('Photo captured');
  }, [isStreaming, addLog]);

  // Download photo
  const downloadPhoto = useCallback(() => {
    if (!capturedPhoto) return;

    const link = document.createElement('a');
    link.download = `camera-test-${Date.now()}.jpg`;
    link.href = capturedPhoto;
    link.click();
    addLog('Photo downloaded');
  }, [capturedPhoto, addLog]);

  // Get device info
  const getDeviceInfo = () => {
    const userAgent = navigator.userAgent;
    return {
      platform: navigator.platform,
      userAgent: userAgent,
      isIOS: /iPad|iPhone|iPod/.test(userAgent),
      isAndroid: /Android/.test(userAgent),
      isChrome: /Chrome/.test(userAgent),
      isSafari: /Safari/.test(userAgent) && !/Chrome/.test(userAgent),
      isFirefox: /Firefox/.test(userAgent),
      isHTTPS: typeof window !== 'undefined' ? window.location.protocol === 'https:' : false,
      isLocalhost: typeof window !== 'undefined' ? window.location.hostname === 'localhost' : false,
      hasMediaDevices: !!navigator.mediaDevices,
      hasGetUserMedia: !!navigator.mediaDevices?.getUserMedia,
      hasLegacyGetUserMedia: !!(navigator as any).getUserMedia ||
                            !!(navigator as any).webkitGetUserMedia ||
                            !!(navigator as any).mozGetUserMedia
    };
  };

  const deviceInfo = getDeviceInfo();

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Simple Camera Test</h1>
          <p className="text-gray-600">Simplified camera access testing for debugging</p>
        </div>

        {/* Device Info */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-lg font-semibold mb-4">Device Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>Platform: {deviceInfo.platform}</div>
            <div>HTTPS: {deviceInfo.isHTTPS ? '✅' : '❌'}</div>
            <div>Localhost: {deviceInfo.isLocalhost ? '✅' : '❌'}</div>
            <div>MediaDevices API: {deviceInfo.hasMediaDevices ? '✅' : '❌'}</div>
            <div>getUserMedia: {deviceInfo.hasGetUserMedia ? '✅' : '❌'}</div>
            <div>Legacy getUserMedia: {deviceInfo.hasLegacyGetUserMedia ? '✅' : '❌'}</div>
            <div>iOS: {deviceInfo.isIOS ? '✅' : '❌'}</div>
            <div>Android: {deviceInfo.isAndroid ? '✅' : '❌'}</div>
            <div>Chrome: {deviceInfo.isChrome ? '✅' : '❌'}</div>
            <div>Safari: {deviceInfo.isSafari ? '✅' : '❌'}</div>
            <div>Firefox: {deviceInfo.isFirefox ? '✅' : '❌'}</div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertCircle className="text-red-500 mr-2 mt-0.5 flex-shrink-0" size={16} />
              <div className="text-red-800 text-sm">{error}</div>
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex flex-wrap gap-3">
            {!isStreaming ? (
              <button
                onClick={requestCamera}
                disabled={isRequesting}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg font-medium"
              >
                <Camera size={20} />
                <span>{isRequesting ? 'Requesting Camera...' : 'Start Camera'}</span>
              </button>
            ) : (
              <>
                <button
                  onClick={takePhoto}
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium"
                >
                  <Camera size={20} />
                  <span>Take Photo</span>
                </button>
                <button
                  onClick={stopCamera}
                  className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium"
                >
                  <span>Stop Camera</span>
                </button>
              </>
            )}
          </div>
        </div>

        {/* Camera Preview */}
        {isStreaming && (
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold mb-4">Camera Preview</h3>
            <div className="relative bg-black rounded-lg overflow-hidden">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-auto max-h-96"
              />
            </div>
          </div>
        )}

        {/* Captured Photo */}
        {capturedPhoto && (
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Captured Photo</h3>
              <button
                onClick={downloadPhoto}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              >
                <Download size={16} />
                <span>Download</span>
              </button>
            </div>
            <img
              src={capturedPhoto}
              alt="Captured"
              className="w-full h-auto max-h-96 object-contain bg-gray-100 rounded"
            />
          </div>
        )}

        {/* Debug Logs */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Debug Logs</h3>
          <div className="bg-gray-50 rounded p-4 max-h-64 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500 text-sm">No logs yet. Click "Start Camera" to begin.</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-xs font-mono text-gray-700">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Hidden canvas */}
        <canvas ref={canvasRef} className="hidden" />
      </div>
    </div>
  );
}
