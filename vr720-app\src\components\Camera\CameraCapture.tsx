'use client';

import React, { useRef, useState, useCallback } from 'react';
import { Camera, Download, RotateCcw, CheckCircle } from 'lucide-react';

interface CameraCaptureProps {
  onPhotoCapture: (photoData: string, metadata: any) => void;
  captureMode: 'single' | 'panoramic';
  currentStep?: number;
  totalSteps?: number;
}

export default function CameraCapture({ 
  onPhotoCapture, 
  captureMode, 
  currentStep = 1, 
  totalSteps = 8 
}: CameraCaptureProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const [deviceOrientation, setDeviceOrientation] = useState({ alpha: 0, beta: 0, gamma: 0 });

  // Enhanced camera support check
  const checkCameraSupport = useCallback(() => {
    // Check for HTTPS requirement
    const isSecure = typeof window !== 'undefined' && (
      window.location.protocol === 'https:' ||
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1'
    );

    if (!isSecure) {
      alert('Camera access requires HTTPS. Please use HTTPS or localhost for testing.');
      return false;
    }

    // Check for getUserMedia support
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert('Your browser does not support camera access. Please use a modern browser like Chrome, Firefox, or Safari.');
      return false;
    }

    return true;
  }, []);

  // Check camera permissions
  const checkCameraPermissions = useCallback(async () => {
    try {
      const result = await navigator.permissions.query({ name: 'camera' as PermissionName });
      console.log('Camera permission status:', result.state);
      return result.state;
    } catch (error) {
      console.log('Permission API not supported, will try direct access');
      return 'unknown';
    }
  }, []);

  // Start camera with enhanced error handling
  const startCamera = useCallback(async () => {
    if (!checkCameraSupport()) return;

    try {
      // Check permissions first
      const permissionStatus = await checkCameraPermissions();

      if (permissionStatus === 'denied') {
        alert('Camera permission denied. Please enable camera access in your browser settings and refresh the page.');
        return;
      }

      console.log('Requesting camera access...');

      // Try multiple constraint configurations for better compatibility
      const constraintOptions = [
        // Option 1: Rear camera with high quality
        {
          video: {
            facingMode: { exact: 'environment' },
            width: { ideal: 1920, min: 640 },
            height: { ideal: 1080, min: 480 }
          },
          audio: false
        },
        // Option 2: Any rear camera
        {
          video: {
            facingMode: 'environment',
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 }
          },
          audio: false
        },
        // Option 3: Any camera
        {
          video: {
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 }
          },
          audio: false
        },
        // Option 4: Basic video only
        {
          video: true,
          audio: false
        }
      ];

      let stream = null;
      let lastError = null;

      // Try each constraint option until one works
      for (const constraints of constraintOptions) {
        try {
          console.log('Trying constraints:', constraints);
          stream = await navigator.mediaDevices.getUserMedia(constraints);
          console.log('Camera access granted with constraints:', constraints);
          break;
        } catch (error) {
          console.log('Failed with constraints:', constraints, 'Error:', error);
          lastError = error;
          continue;
        }
      }

      if (!stream) {
        throw lastError || new Error('Failed to access camera with any configuration');
      }

      if (videoRef.current) {
        videoRef.current.srcObject = stream;

        // Wait for video to load
        videoRef.current.onloadedmetadata = () => {
          console.log('Camera stream loaded');
          setIsStreaming(true);
        };

        // Handle playback errors
        videoRef.current.onerror = (error) => {
          console.error('Video error:', error);
          alert('Video playback failed, please try again');
        };

        // Auto play
        try {
          await videoRef.current.play();
        } catch (playError) {
          console.error('Play error:', playError);
          // Some mobile devices require user interaction to play
        }
      }
    } catch (error) {
      console.error('Camera access error:', error);

      let errorMessage = 'Unable to access camera';
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = 'Camera permission denied. Please allow camera access in browser settings';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'Camera device not found';
        } else if (error.name === 'NotSupportedError') {
          errorMessage = 'Your device does not support camera functionality';
        } else if (error.name === 'NotReadableError') {
          errorMessage = 'Camera is being used by another application. Please close other camera apps and try again';
        }
      }

      alert(errorMessage);
    }
  }, [checkCameraSupport]);

  // Stop camera
  const stopCamera = useCallback(() => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      setIsStreaming(false);
    }
  }, []);

  // Take photo
  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    const photoData = canvas.toDataURL('image/jpeg', 0.9);
    const metadata = {
      timestamp: Date.now(),
      orientation: deviceOrientation,
      step: currentStep,
      mode: captureMode,
      resolution: {
        width: canvas.width,
        height: canvas.height
      }
    };

    setCapturedPhotos(prev => [...prev, photoData]);
    onPhotoCapture(photoData, metadata);
  }, [onPhotoCapture, captureMode, currentStep, deviceOrientation]);

  // Listen to device orientation
  React.useEffect(() => {
    const handleOrientation = (event: DeviceOrientationEvent) => {
      setDeviceOrientation({
        alpha: event.alpha || 0,
        beta: event.beta || 0,
        gamma: event.gamma || 0
      });
    };

    if (window.DeviceOrientationEvent) {
      window.addEventListener('deviceorientation', handleOrientation);
      return () => window.removeEventListener('deviceorientation', handleOrientation);
    }
  }, []);

  return (
    <div className="relative w-full h-screen bg-black">
      {/* Video preview */}
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted
        className="w-full h-full object-cover"
      />

      {/* Hidden canvas for photo capture */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Shooting guidance interface */}
      {captureMode === 'panoramic' && (
        <div className="absolute top-4 left-4 right-4 bg-black/70 text-white p-4 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm">Panoramic Shooting Progress</span>
            <span className="text-sm">{currentStep}/{totalSteps}</span>
          </div>
          <div className="w-full bg-gray-600 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
          <p className="text-xs mt-2 text-gray-300">
            Please rotate your phone slowly, taking a photo every 45 degrees
          </p>
        </div>
      )}

      {/* Direction indicator */}
      {captureMode === 'panoramic' && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="w-32 h-32 border-2 border-white/50 rounded-full flex items-center justify-center">
            <div 
              className="w-4 h-4 bg-blue-500 rounded-full transition-transform duration-100"
              style={{ 
                transform: `rotate(${deviceOrientation.alpha}deg) translateY(-40px)` 
              }}
            />
          </div>
        </div>
      )}

      {/* Control buttons */}
      <div className="absolute bottom-8 left-0 right-0 flex justify-center items-center space-x-8">
        {!isStreaming ? (
          <button
            onClick={startCamera}
            className="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-full transition-colors"
          >
            <Camera size={32} />
          </button>
        ) : (
          <>
            <button
              onClick={stopCamera}
              className="bg-red-500 hover:bg-red-600 text-white p-3 rounded-full transition-colors"
            >
              <RotateCcw size={24} />
            </button>
            
            <button
              onClick={capturePhoto}
              className="bg-white hover:bg-gray-100 text-black p-6 rounded-full transition-colors shadow-lg"
            >
              <Camera size={40} />
            </button>
            
            {capturedPhotos.length > 0 && (
              <div className="bg-green-500 text-white p-3 rounded-full">
                <CheckCircle size={24} />
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
                  {capturedPhotos.length}
                </span>
              </div>
            )}
          </>
        )}
      </div>

      {/* Captured photos preview */}
      {capturedPhotos.length > 0 && (
        <div className="absolute bottom-24 left-4 right-4">
          <div className="flex space-x-2 overflow-x-auto">
            {capturedPhotos.map((photo, index) => (
              <img
                key={index}
                src={photo}
                alt={`Photo ${index + 1}`}
                className="w-16 h-16 object-cover rounded border-2 border-white/50"
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
