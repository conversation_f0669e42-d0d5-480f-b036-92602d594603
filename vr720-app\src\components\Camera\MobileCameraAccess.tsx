'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Camera, AlertCircle, Smartphone, Settings, RefreshCw } from 'lucide-react';

interface MobileCameraAccessProps {
  onCameraReady: (stream: MediaStream) => void;
  onError: (error: string) => void;
}

export const MobileCameraAccess: React.FC<MobileCameraAccessProps> = ({
  onCameraReady,
  onError
}) => {
  const [permissionStatus, setPermissionStatus] = useState<'unknown' | 'granted' | 'denied' | 'prompt'>('unknown');
  const [isRequesting, setIsRequesting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [deviceInfo, setDeviceInfo] = useState<any>({});

  // Detect device and browser info
  useEffect(() => {
    const userAgent = navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    const isAndroid = /Android/.test(userAgent);
    const isMobile = isIOS || isAndroid;
    const isChrome = /Chrome/.test(userAgent);
    const isSafari = /Safari/.test(userAgent) && !isChrome;
    const isFirefox = /Firefox/.test(userAgent);
    
    setDeviceInfo({
      isIOS,
      isAndroid,
      isMobile,
      isChrome,
      isSafari,
      isFirefox,
      isHTTPS: location.protocol === 'https:',
      isLocalhost: location.hostname === 'localhost'
    });
  }, []);

  // Check camera permission status
  const checkPermissions = useCallback(async () => {
    try {
      if ('permissions' in navigator) {
        const result = await navigator.permissions.query({ name: 'camera' as PermissionName });
        setPermissionStatus(result.state);
        
        // Listen for permission changes
        result.onchange = () => {
          setPermissionStatus(result.state);
        };
      }
    } catch (error) {
      console.log('Permission API not supported');
      setPermissionStatus('unknown');
    }
  }, []);

  useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  // Request camera access with mobile-optimized constraints
  const requestCameraAccess = useCallback(async () => {
    setIsRequesting(true);
    setErrorMessage('');

    try {
      // Check basic support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera API not supported in this browser');
      }

      // Check HTTPS requirement (except localhost)
      if (!deviceInfo.isHTTPS && !deviceInfo.isLocalhost) {
        throw new Error('Camera access requires HTTPS connection');
      }

      // Mobile-optimized constraints
      const mobileConstraints = [
        // High quality rear camera (preferred)
        {
          video: {
            facingMode: { exact: 'environment' },
            width: { ideal: 1920, max: 1920, min: 1280 },
            height: { ideal: 1080, max: 1080, min: 720 },
            frameRate: { ideal: 30, max: 30 }
          },
          audio: false
        },
        // Medium quality rear camera
        {
          video: {
            facingMode: 'environment',
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 },
            frameRate: { ideal: 30 }
          },
          audio: false
        },
        // Any rear camera
        {
          video: {
            facingMode: 'environment'
          },
          audio: false
        },
        // Front camera fallback
        {
          video: {
            facingMode: 'user',
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 }
          },
          audio: false
        },
        // Basic video
        {
          video: true,
          audio: false
        }
      ];

      let stream = null;
      let lastError = null;

      // Try each constraint configuration
      for (let i = 0; i < mobileConstraints.length; i++) {
        const constraints = mobileConstraints[i];
        
        try {
          console.log(`Attempting camera access with config ${i + 1}:`, constraints);
          stream = await navigator.mediaDevices.getUserMedia(constraints);
          
          // Verify stream is active
          if (stream && stream.active && stream.getVideoTracks().length > 0) {
            console.log('Camera access successful!');
            onCameraReady(stream);
            setPermissionStatus('granted');
            return;
          } else {
            throw new Error('Stream is not active');
          }
        } catch (error) {
          console.log(`Config ${i + 1} failed:`, error);
          lastError = error;
          
          // Stop any partial stream
          if (stream) {
            stream.getTracks().forEach(track => track.stop());
          }
          
          continue;
        }
      }

      // If we get here, all attempts failed
      throw lastError || new Error('Failed to access camera');

    } catch (error: any) {
      console.error('Camera access failed:', error);
      
      let userFriendlyMessage = 'Unable to access camera. ';
      
      if (error.name === 'NotAllowedError') {
        userFriendlyMessage += 'Please allow camera access and try again.';
      } else if (error.name === 'NotFoundError') {
        userFriendlyMessage += 'No camera found on this device.';
      } else if (error.name === 'NotSupportedError') {
        userFriendlyMessage += 'Camera not supported on this device.';
      } else if (error.name === 'NotReadableError') {
        userFriendlyMessage += 'Camera is being used by another app.';
      } else if (error.message.includes('HTTPS')) {
        userFriendlyMessage += 'HTTPS connection required for camera access.';
      } else {
        userFriendlyMessage += error.message || 'Unknown error occurred.';
      }
      
      setErrorMessage(userFriendlyMessage);
      onError(userFriendlyMessage);
      setPermissionStatus('denied');
    } finally {
      setIsRequesting(false);
    }
  }, [deviceInfo, onCameraReady, onError]);

  // Get device-specific instructions
  const getInstructions = () => {
    if (deviceInfo.isIOS) {
      return [
        'Make sure you\'re using Safari browser',
        'Allow camera access when prompted',
        'If blocked, go to Settings > Safari > Camera and enable access'
      ];
    } else if (deviceInfo.isAndroid) {
      return [
        'Chrome browser is recommended for best compatibility',
        'Allow camera access when prompted',
        'If blocked, go to browser settings and enable camera permission'
      ];
    } else {
      return [
        'Use a modern browser (Chrome, Firefox, Safari)',
        'Allow camera access when prompted',
        'Make sure no other apps are using the camera'
      ];
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Camera size={32} className="text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Camera Access Required
          </h2>
          <p className="text-gray-600 text-sm">
            We need access to your camera to take photos for the VR tour
          </p>
        </div>

        {/* Device Info */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center mb-2">
            <Smartphone size={16} className="text-gray-500 mr-2" />
            <span className="text-sm font-medium text-gray-700">Device Info</span>
          </div>
          <div className="text-xs text-gray-600 space-y-1">
            <div>Platform: {deviceInfo.isIOS ? 'iOS' : deviceInfo.isAndroid ? 'Android' : 'Desktop'}</div>
            <div>Browser: {deviceInfo.isChrome ? 'Chrome' : deviceInfo.isSafari ? 'Safari' : deviceInfo.isFirefox ? 'Firefox' : 'Other'}</div>
            <div>Connection: {deviceInfo.isHTTPS ? 'HTTPS ✓' : deviceInfo.isLocalhost ? 'Localhost ✓' : 'HTTP ⚠️'}</div>
          </div>
        </div>

        {/* Error Message */}
        {errorMessage && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle size={16} className="text-red-500 mr-2 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-700">{errorMessage}</div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Instructions:</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            {getInstructions().map((instruction, index) => (
              <li key={index} className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                {instruction}
              </li>
            ))}
          </ul>
        </div>

        {/* Action Button */}
        <button
          onClick={requestCameraAccess}
          disabled={isRequesting}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center"
        >
          {isRequesting ? (
            <>
              <RefreshCw size={16} className="animate-spin mr-2" />
              Requesting Access...
            </>
          ) : (
            <>
              <Camera size={16} className="mr-2" />
              Enable Camera
            </>
          )}
        </button>

        {/* Permission Status */}
        {permissionStatus !== 'unknown' && (
          <div className="mt-4 text-center">
            <span className="text-xs text-gray-500">
              Permission Status: 
              <span className={`ml-1 font-medium ${
                permissionStatus === 'granted' ? 'text-green-600' : 
                permissionStatus === 'denied' ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {permissionStatus.charAt(0).toUpperCase() + permissionStatus.slice(1)}
              </span>
            </span>
          </div>
        )}

        {/* Troubleshooting */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <details className="text-sm">
            <summary className="text-gray-700 cursor-pointer hover:text-gray-900">
              Troubleshooting
            </summary>
            <div className="mt-2 text-xs text-gray-600 space-y-1">
              <p>• Refresh the page and try again</p>
              <p>• Check if another app is using the camera</p>
              <p>• Try using a different browser</p>
              <p>• Clear browser cache and cookies</p>
              <p>• Restart your browser</p>
            </div>
          </details>
        </div>
      </div>
    </div>
  );
};
