'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Camera, RotateCcw, Download, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';

interface MobileCameraCaptureProps {
  onPhotoCapture: (photoDataUrl: string) => void;
  onClose: () => void;
}

export const MobileCameraCapture: React.FC<MobileCameraCaptureProps> = ({
  onPhotoCapture,
  onClose
}) => {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [connectionStep, setConnectionStep] = useState<string>('');
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 启动相机
  const startCamera = useCallback(async () => {
    setIsConnecting(true);
    setError('');
    setConnectionStep('Checking device support...');

    try {
      // Check basic support
      if (!navigator.mediaDevices?.getUserMedia) {
        throw new Error('Your browser does not support camera functionality. Please use Chrome, Safari, or Firefox');
      }

      setConnectionStep('Checking permissions...');
      
      // 简化的约束配置，优先兼容性
      const constraints = [
        // 最基本的配置
        { video: true },
        // 指定后置摄像头
        { video: { facingMode: 'environment' } },
        // 指定前置摄像头作为备选
        { video: { facingMode: 'user' } }
      ];

      let cameraStream = null;
      let lastError = null;

      for (let i = 0; i < constraints.length; i++) {
        try {
          setConnectionStep(`Connecting to camera (${i + 1}/${constraints.length})...`);

          cameraStream = await navigator.mediaDevices.getUserMedia(constraints[i]);

          if (cameraStream && cameraStream.active) {
            console.log(`Camera connected successfully with config ${i + 1}`);
            break;
          }
        } catch (err: any) {
          console.log(`Config ${i + 1} failed:`, err);
          lastError = err;
          if (cameraStream) {
            cameraStream.getTracks().forEach(track => track.stop());
          }
        }
      }

      if (!cameraStream || !cameraStream.active) {
        throw lastError || new Error('Unable to connect to camera');
      }

      setConnectionStep('Setting up video stream...');

      // 设置视频元素
      if (videoRef.current) {
        videoRef.current.srcObject = cameraStream;
        
        // Wait for metadata to load
        const metadataPromise = new Promise<void>((resolve, reject) => {
          if (!videoRef.current) {
            reject(new Error('Video element not available'));
            return;
          }

          videoRef.current.onloadedmetadata = () => {
            console.log('Video metadata loaded successfully');
            resolve();
          };

          videoRef.current.onerror = (error) => {
            console.error('Video loading error:', error);
            reject(new Error('Video loading failed'));
          };

          // Timeout handling
          setTimeout(() => {
            reject(new Error('Video loading timeout'));
          }, 10000);
        });

        await metadataPromise;

        setConnectionStep('Starting video playback...');

        // Try to play video
        try {
          await videoRef.current.play();
          console.log('Video playback successful');
        } catch (playError) {
          console.log('Auto-play failed, user interaction required:', playError);
          // Mobile devices may require user interaction to play
        }

        setStream(cameraStream);
        setIsStreaming(true);
        setConnectionStep('');
      }

    } catch (err: any) {
      console.error('Camera startup failed:', err);

      let errorMessage = 'Camera connection failed: ';

      if (err.name === 'NotAllowedError') {
        errorMessage += 'Permission denied. Please allow camera access';
      } else if (err.name === 'NotFoundError') {
        errorMessage += 'No camera device found';
      } else if (err.name === 'NotSupportedError') {
        errorMessage += 'Camera not supported on this device';
      } else if (err.name === 'NotReadableError') {
        errorMessage += 'Camera is being used by another application';
      } else {
        errorMessage += err.message || 'Unknown error';
      }

      setError(errorMessage);
    } finally {
      setIsConnecting(false);
    }
  }, []);

  // 停止相机
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
      setIsStreaming(false);
    }
  }, [stream]);

  // 拍照
  const takePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || !isStreaming) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    // 设置画布尺寸
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // 绘制视频帧到画布
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // 获取图片数据
    const photoDataUrl = canvas.toDataURL('image/jpeg', 0.9);
    setCapturedPhoto(photoDataUrl);
  }, [isStreaming]);

  // 确认照片
  const confirmPhoto = useCallback(() => {
    if (capturedPhoto) {
      onPhotoCapture(capturedPhoto);
      stopCamera();
      onClose();
    }
  }, [capturedPhoto, onPhotoCapture, stopCamera, onClose]);

  // 重新拍照
  const retakePhoto = useCallback(() => {
    setCapturedPhoto('');
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [stream]);

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col">
      {/* Header control bar */}
      <div className="flex items-center justify-between p-4 bg-black/80 text-white">
        <button
          onClick={onClose}
          className="text-white hover:text-gray-300"
        >
          <RotateCcw size={24} />
        </button>
        <h2 className="text-lg font-medium">Take Photo</h2>
        <div className="w-6" /> {/* Spacer for centering */}
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 relative">
        {/* 相机预览 */}
        {isStreaming && !capturedPhoto && (
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className="w-full h-full object-cover"
          />
        )}

        {/* Captured photo preview */}
        {capturedPhoto && (
          <img
            src={capturedPhoto}
            alt="Captured photo"
            className="w-full h-full object-cover"
          />
        )}

        {/* Connection status */}
        {isConnecting && (
          <div className="absolute inset-0 bg-black flex flex-col items-center justify-center text-white">
            <Loader2 size={48} className="animate-spin mb-4" />
            <p className="text-lg mb-2">Connecting to camera...</p>
            {connectionStep && (
              <p className="text-sm text-gray-300">{connectionStep}</p>
            )}
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="absolute inset-0 bg-black flex flex-col items-center justify-center text-white p-6">
            <AlertCircle size={48} className="text-red-500 mb-4" />
            <p className="text-lg mb-4 text-center">{error}</p>
            <button
              onClick={startCamera}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg"
            >
              重试
            </button>
          </div>
        )}

        {/* Initial state */}
        {!isConnecting && !isStreaming && !error && (
          <div className="absolute inset-0 bg-black flex flex-col items-center justify-center text-white">
            <Camera size={64} className="mb-6 text-gray-400" />
            <h3 className="text-xl mb-2">Ready to Capture</h3>
            <p className="text-gray-300 mb-6 text-center px-6">
              Tap the button below to start using the camera
            </p>
            <button
              onClick={startCamera}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg"
            >
              Start Camera
            </button>
          </div>
        )}
      </div>

      {/* 底部控制栏 */}
      <div className="p-6 bg-black/80">
        {isStreaming && !capturedPhoto && (
          <div className="flex justify-center">
            <button
              onClick={takePhoto}
              className="w-16 h-16 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors"
            >
              <div className="w-12 h-12 bg-gray-800 rounded-full" />
            </button>
          </div>
        )}

        {capturedPhoto && (
          <div className="flex justify-center space-x-4">
            <button
              onClick={retakePhoto}
              className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg"
            >
              Retake
            </button>
            <button
              onClick={confirmPhoto}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center"
            >
              <CheckCircle size={20} className="mr-2" />
              Use Photo
            </button>
          </div>
        )}
      </div>

      {/* 隐藏的画布用于拍照 */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
};
