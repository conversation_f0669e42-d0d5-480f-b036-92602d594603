'use client';

// 演示用的全景图片数据
export const demoPhotos = [
  {
    data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNDI5OWZmO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3R5bGU9InN0b3AtY29sb3I6IzAwYmNkNDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNDI5OWZmO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNncmFkaWVudCkiLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWFqOaZr+WbvueJhyAxPC90ZXh0Pgo8L3N2Zz4=',
    metadata: {
      timestamp: Date.now() - 7000,
      orientation: { alpha: 0, beta: 0, gamma: 0 },
      step: 1,
      mode: 'panoramic',
      resolution: { width: 800, height: 400 }
    }
  },
  {
    data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQyIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmNjk5NDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZjAwODA7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmNjk5NDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZGllbnQyKSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5YWo5pmv5Zu+54mHIDI8L3RleHQ+Cjwvc3ZnPg==',
    metadata: {
      timestamp: Date.now() - 6000,
      orientation: { alpha: 45, beta: 0, gamma: 0 },
      step: 2,
      mode: 'panoramic',
      resolution: { width: 800, height: 400 }
    }
  },
  {
    data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQzIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzAwZmY4MDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMwMGZmNDA7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6IzAwZmY4MDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZGllbnQzKSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5YWo5pmv5Zu+54mHIDM8L3RleHQ+Cjwvc3ZnPg==',
    metadata: {
      timestamp: Date.now() - 5000,
      orientation: { alpha: 90, beta: 0, gamma: 0 },
      step: 3,
      mode: 'panoramic',
      resolution: { width: 800, height: 400 }
    }
  },
  {
    data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQ0IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmZmYwMDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZmNjMDA7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmZmYwMDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZGllbnQ0KSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5YWo5pmv5Zu+54mHIDQ8L3RleHQ+Cjwvc3ZnPg==',
    metadata: {
      timestamp: Date.now() - 4000,
      orientation: { alpha: 135, beta: 0, gamma: 0 },
      step: 4,
      mode: 'panoramic',
      resolution: { width: 800, height: 400 }
    }
  },
  {
    data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQ1IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzgwMDBmZjtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiM0MDAwZmY7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6IzgwMDBmZjtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZGllbnQ1KSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5YWo5pmv5Zu+54mHIDU8L3RleHQ+Cjwvc3ZnPg==',
    metadata: {
      timestamp: Date.now() - 3000,
      orientation: { alpha: 180, beta: 0, gamma: 0 },
      step: 5,
      mode: 'panoramic',
      resolution: { width: 800, height: 400 }
    }
  },
  {
    data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQ2IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmODAwMDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmZjQwMDA7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmODAwMDtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZGllbnQ2KSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5YWo5pmv5Zu+54mHIDY8L3RleHQ+Cjwvc3ZnPg==',
    metadata: {
      timestamp: Date.now() - 2000,
      orientation: { alpha: 225, beta: 0, gamma: 0 },
      step: 6,
      mode: 'panoramic',
      resolution: { width: 800, height: 400 }
    }
  },
  {
    data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQ3IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6IzAwZmZmZjtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMwMGNjZmY7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6IzAwZmZmZjtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZGllbnQ3KSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5YWo5pmv5Zu+54mHIDc8L3RleHQ+Cjwvc3ZnPg==',
    metadata: {
      timestamp: Date.now() - 1000,
      orientation: { alpha: 270, beta: 0, gamma: 0 },
      step: 7,
      mode: 'panoramic',
      resolution: { width: 800, height: 400 }
    }
  },
  {
    data: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQ4IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmZmZmZjtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNjY2NjY2M7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZmZmZmZjtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JhZGllbnQ4KSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5YWo5pmv5Zu+54mHIDg8L3RleHQ+Cjwvc3ZnPg==',
    metadata: {
      timestamp: Date.now(),
      orientation: { alpha: 315, beta: 0, gamma: 0 },
      step: 8,
      mode: 'panoramic',
      resolution: { width: 800, height: 400 }
    }
  }
];

// 生成演示用的全景图URL
export const generateDemoPanorama = (): string => {
  // 创建一个简单的全景图SVG
  const panoramaSvg = `
    <svg width="4096" height="2048" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <radialGradient id="skyGradient" cx="50%" cy="30%" r="70%">
          <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
          <stop offset="70%" style="stop-color:#4169E1;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#191970;stop-opacity:1" />
        </radialGradient>
        <linearGradient id="groundGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#228B22;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#006400;stop-opacity:1" />
        </linearGradient>
      </defs>
      
      <!-- 天空 -->
      <rect width="100%" height="60%" fill="url(#skyGradient)"/>
      
      <!-- 地面 -->
      <rect y="60%" width="100%" height="40%" fill="url(#groundGradient)"/>
      
      <!-- 云朵 -->
      <ellipse cx="20%" cy="25%" rx="200" ry="80" fill="white" opacity="0.8"/>
      <ellipse cx="60%" cy="20%" rx="150" ry="60" fill="white" opacity="0.7"/>
      <ellipse cx="85%" cy="30%" rx="180" ry="70" fill="white" opacity="0.6"/>
      
      <!-- 太阳 -->
      <circle cx="80%" cy="20%" r="100" fill="#FFD700" opacity="0.9"/>
      
      <!-- 山脉 -->
      <polygon points="0,1200 800,800 1600,1000 2400,600 3200,900 4096,700 4096,1200" fill="#8B4513" opacity="0.8"/>
      <polygon points="0,1300 600,900 1200,1100 1800,700 2400,1000 3000,800 3600,1200 4096,900 4096,1300" fill="#A0522D" opacity="0.7"/>
      
      <!-- 树木 -->
      <rect x="10%" y="70%" width="40" height="200" fill="#8B4513"/>
      <circle cx="12%" cy="68%" r="80" fill="#228B22"/>
      
      <rect x="25%" y="75%" width="30" height="150" fill="#8B4513"/>
      <circle cx="26.5%" cy="73%" r="60" fill="#32CD32"/>
      
      <rect x="70%" y="72%" width="35" height="180" fill="#8B4513"/>
      <circle cx="71.8%" cy="70%" r="70" fill="#228B22"/>
      
      <!-- 建筑物 -->
      <rect x="40%" y="65%" width="200" height="300" fill="#696969"/>
      <rect x="42%" y="67%" width="40" height="60" fill="#87CEEB"/>
      <rect x="47%" y="67%" width="40" height="60" fill="#87CEEB"/>
      <rect x="52%" y="67%" width="40" height="60" fill="#87CEEB"/>
      
      <!-- 文字标识 -->
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="120" fill="white" text-anchor="middle" opacity="0.8">VR720 演示全景图</text>
      <text x="50%" y="55%" font-family="Arial, sans-serif" font-size="60" fill="white" text-anchor="middle" opacity="0.6">360度全景体验</text>
      
      <!-- 方向指示 -->
      <text x="5%" y="95%" font-family="Arial, sans-serif" font-size="80" fill="white" text-anchor="middle">北</text>
      <text x="25%" y="95%" font-family="Arial, sans-serif" font-size="80" fill="white" text-anchor="middle">东北</text>
      <text x="50%" y="95%" font-family="Arial, sans-serif" font-size="80" fill="white" text-anchor="middle">东</text>
      <text x="75%" y="95%" font-family="Arial, sans-serif" font-size="80" fill="white" text-anchor="middle">东南</text>
      <text x="95%" y="95%" font-family="Arial, sans-serif" font-size="80" fill="white" text-anchor="middle">南</text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(panoramaSvg)))}`;
};
