'use client';

import { House } from '../RealEstate/HouseStructureManager';
import { generateDemoPanorama } from './DemoData';

// 生成不同房间的演示全景图
const generateRoomPanorama = (roomName: string, roomType: string): string => {
  const colors = {
    'living-room': ['#87CEEB', '#4169E1', '#191970'],
    'bedroom': ['#DDA0DD', '#9370DB', '#4B0082'],
    'kitchen': ['#F0E68C', '#DAA520', '#B8860B'],
    'bathroom': ['#E0FFFF', '#00CED1', '#008B8B'],
    'outdoor': ['#98FB98', '#32CD32', '#228B22']
  };

  const roomColors = colors[roomType as keyof typeof colors] || colors['living-room'];
  
  const panoramaSvg = `
    <svg width="4096" height="2048" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <radialGradient id="skyGradient-${roomType}" cx="50%" cy="30%" r="70%">
          <stop offset="0%" style="stop-color:${roomColors[0]};stop-opacity:1" />
          <stop offset="70%" style="stop-color:${roomColors[1]};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${roomColors[2]};stop-opacity:1" />
        </radialGradient>
        <linearGradient id="groundGradient-${roomType}" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#D2B48C;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#8B7355;stop-opacity:1" />
        </linearGradient>
      </defs>
      
      <!-- 天花板/天空 -->
      <rect width="100%" height="50%" fill="url(#skyGradient-${roomType})"/>
      
      <!-- 地面 -->
      <rect y="50%" width="100%" height="50%" fill="url(#groundGradient-${roomType})"/>
      
      <!-- 房间特色元素 -->
      ${roomType === 'living-room' ? `
        <!-- 沙发 -->
        <rect x="30%" y="60%" width="300" height="150" fill="#8B4513" rx="20"/>
        <rect x="32%" y="55%" width="260" height="80" fill="#DEB887" rx="15"/>
        
        <!-- 电视 -->
        <rect x="60%" y="45%" width="200" height="120" fill="#2F2F2F"/>
        <rect x="62%" y="47%" width="196" height="116" fill="#000000"/>
        
        <!-- 窗户 -->
        <rect x="10%" y="30%" width="150" height="200" fill="#87CEEB" opacity="0.7"/>
      ` : ''}
      
      ${roomType === 'kitchen' ? `
        <!-- 橱柜 -->
        <rect x="20%" y="55%" width="400" height="200" fill="#8B4513"/>
        <rect x="22%" y="50%" width="396" height="80" fill="#DEB887"/>
        
        <!-- 冰箱 -->
        <rect x="70%" y="40%" width="120" height="300" fill="#F5F5F5"/>
        
        <!-- 炉灶 -->
        <rect x="40%" y="58%" width="100" height="80" fill="#2F2F2F"/>
      ` : ''}
      
      ${roomType === 'bedroom' ? `
        <!-- 床 -->
        <rect x="35%" y="55%" width="250" height="180" fill="#8B4513"/>
        <rect x="37%" y="50%" width="246" height="100" fill="#F5F5DC"/>
        
        <!-- 衣柜 -->
        <rect x="70%" y="40%" width="150" height="250" fill="#654321"/>
        
        <!-- 窗户 -->
        <rect x="10%" y="35%" width="120" height="180" fill="#E6E6FA" opacity="0.8"/>
      ` : ''}
      
      ${roomType === 'bathroom' ? `
        <!-- 浴缸 -->
        <rect x="60%" y="60%" width="200" height="120" fill="#F0F8FF"/>
        
        <!-- 洗手台 -->
        <rect x="25%" y="65%" width="150" height="80" fill="#F5F5F5"/>
        
        <!-- 镜子 -->
        <rect x="27%" y="45%" width="146" height="100" fill="#E0E0E0" opacity="0.8"/>
      ` : ''}
      
      <!-- 房间标识 -->
      <text x="50%" y="25%" font-family="Arial, sans-serif" font-size="120" fill="white" text-anchor="middle" opacity="0.9">
        ${roomName}
      </text>
      <text x="50%" y="30%" font-family="Arial, sans-serif" font-size="60" fill="white" text-anchor="middle" opacity="0.7">
        ${roomType === 'living-room' ? '客厅' : 
          roomType === 'bedroom' ? '卧室' : 
          roomType === 'kitchen' ? '厨房' : 
          roomType === 'bathroom' ? '浴室' : 
          roomType === 'outdoor' ? '户外' : '房间'}
      </text>
      
      <!-- 导航提示 -->
      <circle cx="20%" cy="50%" r="30" fill="#FFD700" opacity="0.8"/>
      <text x="20%" y="55%" font-family="Arial, sans-serif" font-size="24" fill="black" text-anchor="middle">→</text>
      
      <circle cx="80%" cy="50%" r="30" fill="#FFD700" opacity="0.8"/>
      <text x="80%" y="55%" font-family="Arial, sans-serif" font-size="24" fill="black" text-anchor="middle">→</text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(panoramaSvg)))}`;
};

// 创建演示房屋数据
export const createDemoHouse = (): House => {
  const house: House = {
    id: 'demo-house-1',
    name: '豪华现代别墅',
    address: '123 Luxury Lane, Beverly Hills, CA 90210',
    rooms: [],
    metadata: {
      totalArea: 450,
      bedrooms: 4,
      bathrooms: 3,
      price: 2500000,
      description: '位于比佛利山庄的豪华现代别墅，拥有开放式设计和顶级装修。'
    }
  };

  // 创建房间
  const rooms = [
    {
      id: 'living-room-1',
      name: '主客厅',
      type: 'living-room',
      position: { x: 200, y: 150 },
      connections: ['kitchen-1', 'bedroom-1'],
      metadata: {
        area: 85,
        description: '宽敞明亮的主客厅，配有现代化家具和大型落地窗。',
        features: ['壁炉', '落地窗', '开放式设计']
      }
    },
    {
      id: 'kitchen-1',
      name: '现代厨房',
      type: 'kitchen',
      position: { x: 350, y: 150 },
      connections: ['living-room-1', 'outdoor-1'],
      metadata: {
        area: 45,
        description: '配备顶级电器的现代化厨房，大理石台面和定制橱柜。',
        features: ['大理石台面', '不锈钢电器', '中央岛台']
      }
    },
    {
      id: 'bedroom-1',
      name: '主卧室',
      type: 'bedroom',
      position: { x: 200, y: 300 },
      connections: ['living-room-1', 'bathroom-1'],
      metadata: {
        area: 65,
        description: '豪华主卧室，配有步入式衣柜和私人阳台。',
        features: ['步入式衣柜', '私人阳台', '套间浴室']
      }
    },
    {
      id: 'bathroom-1',
      name: '主浴室',
      type: 'bathroom',
      position: { x: 350, y: 300 },
      connections: ['bedroom-1'],
      metadata: {
        area: 25,
        description: '豪华主浴室，配有独立浴缸和淋浴间。',
        features: ['独立浴缸', '双洗手台', '大理石装修']
      }
    },
    {
      id: 'outdoor-1',
      name: '后花园',
      type: 'outdoor',
      position: { x: 500, y: 150 },
      connections: ['kitchen-1'],
      metadata: {
        area: 120,
        description: '精心设计的后花园，配有游泳池和户外用餐区。',
        features: ['游泳池', '户外厨房', '景观设计']
      }
    }
  ];

  // 为每个房间生成全景图和热点
  house.rooms = rooms.map(room => ({
    ...room,
    panoramaUrl: generateRoomPanorama(room.name, room.type),
    thumbnail: generateRoomPanorama(room.name, room.type),
    hotspots: room.connections.map((connectionId, index) => {
      const connectedRoom = rooms.find(r => r.id === connectionId);
      return {
        id: `hotspot-${room.id}-${connectionId}`,
        position: {
          x: index === 0 ? -3 : 3,
          y: 0,
          z: index === 0 ? -2 : -2
        },
        targetRoomId: connectionId,
        label: `前往 ${connectedRoom?.name || '房间'}`,
        type: 'door' as const
      };
    })
  }));

  return house;
};

// 创建多个演示房屋
export const demoHouses: House[] = [
  createDemoHouse(),
  {
    ...createDemoHouse(),
    id: 'demo-house-2',
    name: '温馨家庭住宅',
    address: '456 Family Street, Pasadena, CA 91101',
    metadata: {
      totalArea: 280,
      bedrooms: 3,
      bathrooms: 2,
      price: 850000,
      description: '位于帕萨迪纳的温馨家庭住宅，适合家庭居住。'
    }
  }
];
