// Enhanced demo house data with multiple viewpoints per room

export interface ViewPoint {
  id: string;
  name: string;
  description?: string;
  panoramaUrl: string;
  position: {
    x: number;
    y: number;
    z: number;
  };
  rotation: {
    yaw: number;
    pitch: number;
  };
}

export interface EnhancedRoom {
  id: string;
  name: string;
  type: string;
  description?: string;
  viewPoints: ViewPoint[];
  hotspots: Array<{
    id: string;
    targetRoomId: string;
    targetViewPointId?: string;
    label: string;
    position: { yaw: number; pitch: number };
  }>;
}

export interface EnhancedHouse {
  id: string;
  name: string;
  address: string;
  description?: string;
  rooms: EnhancedRoom[];
}

export const enhancedDemoHouses: EnhancedHouse[] = [
  {
    id: 'luxury-villa-001',
    name: 'Luxury Modern Villa',
    address: '123 Beverly Hills Drive, Beverly Hills, CA 90210',
    description: 'A stunning 4-bedroom luxury villa with panoramic city views and modern amenities.',
    rooms: [
      {
        id: 'living-room-001',
        name: 'Main Living Room',
        type: 'living-room',
        description: 'Spacious living area with floor-to-ceiling windows and modern furnishings.',
        viewPoints: [
          {
            id: 'living-entrance',
            name: 'Entrance View',
            description: 'View from the main entrance showing the full living space',
            panoramaUrl: 'https://pannellum.org/images/alma.jpg',
            position: { x: 0, y: 0, z: 0 },
            rotation: { yaw: 0, pitch: 0 }
          },
          {
            id: 'living-center',
            name: 'Center View',
            description: 'Central viewpoint showcasing the seating area and fireplace',
            panoramaUrl: 'https://pannellum.org/images/cerro-toco-0.jpg',
            position: { x: 3, y: 0, z: 2 },
            rotation: { yaw: 90, pitch: 0 }
          },
          {
            id: 'living-window',
            name: 'Window View',
            description: 'View from near the windows showing the outdoor terrace',
            panoramaUrl: 'https://pannellum.org/images/jfk.jpg',
            position: { x: 5, y: 0, z: 0 },
            rotation: { yaw: 180, pitch: 0 }
          }
        ],
        hotspots: [
          {
            id: 'to-kitchen',
            targetRoomId: 'kitchen-001',
            targetViewPointId: 'kitchen-entrance',
            label: 'Go to Kitchen',
            position: { yaw: 45, pitch: -10 }
          },
          {
            id: 'to-master-bedroom',
            targetRoomId: 'master-bedroom-001',
            targetViewPointId: 'bedroom-entrance',
            label: 'Go to Master Bedroom',
            position: { yaw: -45, pitch: -5 }
          }
        ]
      },
      {
        id: 'kitchen-001',
        name: 'Gourmet Kitchen',
        type: 'kitchen',
        description: 'Modern kitchen with premium appliances and marble countertops.',
        viewPoints: [
          {
            id: 'kitchen-entrance',
            name: 'Entrance View',
            description: 'View from the kitchen entrance',
            panoramaUrl: 'https://pannellum.org/images/jfk.jpg',
            position: { x: 0, y: 0, z: 0 },
            rotation: { yaw: 0, pitch: 0 }
          },
          {
            id: 'kitchen-island',
            name: 'Island View',
            description: 'View from the kitchen island showing cooking area',
            panoramaUrl: 'https://pannellum.org/images/alma.jpg',
            position: { x: 2, y: 0, z: 1 },
            rotation: { yaw: 120, pitch: 0 }
          },
          {
            id: 'kitchen-dining',
            name: 'Dining Area View',
            description: 'View towards the dining area and breakfast nook',
            panoramaUrl: 'https://pannellum.org/images/cerro-toco-0.jpg',
            position: { x: 1, y: 0, z: 3 },
            rotation: { yaw: 270, pitch: 0 }
          }
        ],
        hotspots: [
          {
            id: 'to-living-room',
            targetRoomId: 'living-room-001',
            targetViewPointId: 'living-center',
            label: 'Back to Living Room',
            position: { yaw: 180, pitch: -10 }
          },
          {
            id: 'to-dining-room',
            targetRoomId: 'dining-room-001',
            targetViewPointId: 'dining-center',
            label: 'Go to Dining Room',
            position: { yaw: 90, pitch: -5 }
          }
        ]
      },
      {
        id: 'master-bedroom-001',
        name: 'Master Bedroom Suite',
        type: 'bedroom',
        description: 'Luxurious master bedroom with walk-in closet and en-suite bathroom.',
        viewPoints: [
          {
            id: 'bedroom-entrance',
            name: 'Entrance View',
            description: 'View from the bedroom entrance',
            panoramaUrl: 'https://pannellum.org/images/cerro-toco-0.jpg',
            position: { x: 0, y: 0, z: 0 },
            rotation: { yaw: 0, pitch: 0 }
          },
          {
            id: 'bedroom-bed',
            name: 'Bed View',
            description: 'View from beside the bed showing the sitting area',
            panoramaUrl: 'https://pannellum.org/images/jfk.jpg',
            position: { x: 3, y: 0, z: 2 },
            rotation: { yaw: 45, pitch: 0 }
          },
          {
            id: 'bedroom-balcony',
            name: 'Balcony View',
            description: 'View towards the private balcony and city views',
            panoramaUrl: 'https://pannellum.org/images/alma.jpg',
            position: { x: 4, y: 0, z: 0 },
            rotation: { yaw: 180, pitch: 0 }
          }
        ],
        hotspots: [
          {
            id: 'to-living-room-from-bedroom',
            targetRoomId: 'living-room-001',
            targetViewPointId: 'living-entrance',
            label: 'Back to Living Room',
            position: { yaw: 180, pitch: -10 }
          },
          {
            id: 'to-master-bathroom',
            targetRoomId: 'master-bathroom-001',
            targetViewPointId: 'bathroom-entrance',
            label: 'Go to En-Suite Bathroom',
            position: { yaw: 90, pitch: -15 }
          }
        ]
      },
      {
        id: 'master-bathroom-001',
        name: 'Master En-Suite Bathroom',
        type: 'bathroom',
        description: 'Spa-like bathroom with soaking tub and separate shower.',
        viewPoints: [
          {
            id: 'bathroom-entrance',
            name: 'Entrance View',
            description: 'View from the bathroom entrance',
            panoramaUrl: 'https://pannellum.org/images/alma.jpg',
            position: { x: 0, y: 0, z: 0 },
            rotation: { yaw: 0, pitch: 0 }
          },
          {
            id: 'bathroom-vanity',
            name: 'Vanity View',
            description: 'View from the double vanity area',
            panoramaUrl: 'https://pannellum.org/images/cerro-toco-0.jpg',
            position: { x: 2, y: 0, z: 1 },
            rotation: { yaw: 90, pitch: 0 }
          },
          {
            id: 'bathroom-tub',
            name: 'Soaking Tub View',
            description: 'View from the soaking tub area with window views',
            panoramaUrl: 'https://pannellum.org/images/jfk.jpg',
            position: { x: 1, y: 0, z: 3 },
            rotation: { yaw: 270, pitch: 0 }
          }
        ],
        hotspots: [
          {
            id: 'to-master-bedroom-from-bathroom',
            targetRoomId: 'master-bedroom-001',
            targetViewPointId: 'bedroom-bed',
            label: 'Back to Master Bedroom',
            position: { yaw: 180, pitch: -10 }
          }
        ]
      },
      {
        id: 'dining-room-001',
        name: 'Formal Dining Room',
        type: 'dining-room',
        description: 'Elegant dining room perfect for entertaining guests.',
        viewPoints: [
          {
            id: 'dining-center',
            name: 'Center View',
            description: 'Central view of the dining table and chandelier',
            panoramaUrl: 'https://pannellum.org/images/jfk.jpg',
            position: { x: 0, y: 0, z: 0 },
            rotation: { yaw: 0, pitch: 0 }
          },
          {
            id: 'dining-buffet',
            name: 'Buffet View',
            description: 'View from the buffet area showing the full dining space',
            panoramaUrl: 'https://pannellum.org/images/alma.jpg',
            position: { x: 3, y: 0, z: 1 },
            rotation: { yaw: 180, pitch: 0 }
          }
        ],
        hotspots: [
          {
            id: 'to-kitchen-from-dining',
            targetRoomId: 'kitchen-001',
            targetViewPointId: 'kitchen-dining',
            label: 'Go to Kitchen',
            position: { yaw: 90, pitch: -10 }
          },
          {
            id: 'to-living-from-dining',
            targetRoomId: 'living-room-001',
            targetViewPointId: 'living-center',
            label: 'Go to Living Room',
            position: { yaw: 270, pitch: -5 }
          }
        ]
      }
    ]
  },
  {
    id: 'modern-condo-002',
    name: 'Downtown Modern Condo',
    address: '456 Urban Plaza, Downtown LA, CA 90012',
    description: 'Contemporary 2-bedroom condo with city views and modern amenities.',
    rooms: [
      {
        id: 'condo-living-001',
        name: 'Open Living Area',
        type: 'living-room',
        description: 'Open concept living space with city views.',
        viewPoints: [
          {
            id: 'condo-living-entrance',
            name: 'Entrance View',
            description: 'View from the main entrance',
            panoramaUrl: 'https://pannellum.org/images/cerro-toco-0.jpg',
            position: { x: 0, y: 0, z: 0 },
            rotation: { yaw: 0, pitch: 0 }
          },
          {
            id: 'condo-living-window',
            name: 'City View',
            description: 'View towards the floor-to-ceiling windows',
            panoramaUrl: 'https://pannellum.org/images/alma.jpg',
            position: { x: 4, y: 0, z: 2 },
            rotation: { yaw: 90, pitch: 0 }
          }
        ],
        hotspots: [
          {
            id: 'to-condo-kitchen',
            targetRoomId: 'condo-kitchen-001',
            targetViewPointId: 'condo-kitchen-main',
            label: 'Go to Kitchen',
            position: { yaw: 45, pitch: -10 }
          },
          {
            id: 'to-condo-bedroom',
            targetRoomId: 'condo-bedroom-001',
            targetViewPointId: 'condo-bedroom-main',
            label: 'Go to Bedroom',
            position: { yaw: -45, pitch: -5 }
          }
        ]
      },
      {
        id: 'condo-kitchen-001',
        name: 'Modern Kitchen',
        type: 'kitchen',
        description: 'Sleek modern kitchen with stainless steel appliances.',
        viewPoints: [
          {
            id: 'condo-kitchen-main',
            name: 'Main View',
            description: 'Main kitchen view showing appliances and counter space',
            panoramaUrl: 'https://pannellum.org/images/jfk.jpg',
            position: { x: 0, y: 0, z: 0 },
            rotation: { yaw: 0, pitch: 0 }
          }
        ],
        hotspots: [
          {
            id: 'to-condo-living-from-kitchen',
            targetRoomId: 'condo-living-001',
            targetViewPointId: 'condo-living-entrance',
            label: 'Back to Living Area',
            position: { yaw: 180, pitch: -10 }
          }
        ]
      },
      {
        id: 'condo-bedroom-001',
        name: 'Master Bedroom',
        type: 'bedroom',
        description: 'Comfortable bedroom with built-in storage.',
        viewPoints: [
          {
            id: 'condo-bedroom-main',
            name: 'Main View',
            description: 'Main bedroom view showing bed and storage',
            panoramaUrl: 'https://pannellum.org/images/alma.jpg',
            position: { x: 0, y: 0, z: 0 },
            rotation: { yaw: 0, pitch: 0 }
          }
        ],
        hotspots: [
          {
            id: 'to-condo-living-from-bedroom',
            targetRoomId: 'condo-living-001',
            targetViewPointId: 'condo-living-window',
            label: 'Back to Living Area',
            position: { yaw: 180, pitch: -10 }
          }
        ]
      }
    ]
  }
];

// Export the first house as default for demos
export const defaultEnhancedHouse = enhancedDemoHouses[0];
