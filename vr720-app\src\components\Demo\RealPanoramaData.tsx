'use client';

// Real panoramic image data (using publicly available panoramic images)
export interface RealPanoramaImage {
  id: string;
  name: string;
  description: string;
  url: string;
  thumbnail?: string;
  category: 'interior' | 'exterior' | 'nature' | 'commercial';
  location?: string;
  photographer?: string;
  resolution: string;
  aspectRatio: number;
}

// Using some publicly available panoramic image URLs
export const realPanoramaImages: RealPanoramaImage[] = [
  {
    id: 'living-room-modern',
    name: 'Modern Living Room',
    description: 'Spacious and bright modern living room with comfortable sofas and contemporary decor',
    url: 'https://cdn.aframe.io/360-image-gallery-boilerplate/img/sechelt.jpg',
    category: 'interior',
    location: 'Modern Residence',
    photographer: 'A-Frame Demo',
    resolution: '2048x1024',
    aspectRatio: 2.0
  },
  {
    id: 'nature-forest',
    name: 'Forest Panorama',
    description: 'Beautiful forest environment showcasing the tranquility and beauty of nature',
    url: 'https://cdn.aframe.io/360-image-gallery-boilerplate/img/cubes.jpg',
    category: 'nature',
    location: 'Natural Forest',
    photographer: 'A-Frame Demo',
    resolution: '2048x1024',
    aspectRatio: 2.0
  },
  {
    id: 'city-view',
    name: 'City Skyline',
    description: 'Spectacular city panorama showcasing the prosperity of modern urban life',
    url: 'https://cdn.aframe.io/360-image-gallery-boilerplate/img/city.jpg',
    category: 'exterior',
    location: 'City Center',
    photographer: 'A-Frame Demo',
    resolution: '2048x1024',
    aspectRatio: 2.0
  }
];

// Generate more demo panoramic images
export const generateMoreDemoPanoramas = (): RealPanoramaImage[] => {
  const baseImages = [
    {
      name: 'Luxury Bedroom',
      description: 'Carefully designed master bedroom with high-end furniture and elegant decor',
      category: 'interior' as const,
      location: 'Luxury Villa'
    },
    {
      name: 'Modern Kitchen',
      description: 'Modern kitchen equipped with top-tier appliances, marble countertops and custom cabinets',
      category: 'interior' as const,
      location: 'Modern Residence'
    },
    {
      name: 'Outdoor Garden',
      description: 'Carefully designed backyard with swimming pool and outdoor dining area',
      category: 'exterior' as const,
      location: 'Private Residence'
    },
    {
      name: 'Office Space',
      description: 'Modern office environment with open design promoting team collaboration',
      category: 'commercial' as const,
      location: 'Commercial Building'
    },
    {
      name: 'Seaside View',
      description: 'Magnificent seaside panorama with blue sky, white clouds and azure sea',
      category: 'nature' as const,
      location: 'Seaside Resort'
    }
  ];

  return baseImages.map((img, index) => ({
    id: `demo-${index + 4}`,
    name: img.name,
    description: img.description,
    url: generateCustomPanorama(img.name, img.category),
    category: img.category,
    location: img.location,
    photographer: 'VR720 Demo',
    resolution: '4096x2048',
    aspectRatio: 2.0
  }));
};

// Generate custom panoramic images
const generateCustomPanorama = (name: string, category: string): string => {
  const colors = {
    interior: {
      primary: '#F5F5DC',
      secondary: '#DEB887',
      accent: '#8B4513'
    },
    exterior: {
      primary: '#87CEEB',
      secondary: '#4169E1',
      accent: '#228B22'
    },
    nature: {
      primary: '#98FB98',
      secondary: '#32CD32',
      accent: '#006400'
    },
    commercial: {
      primary: '#E6E6FA',
      secondary: '#9370DB',
      accent: '#4B0082'
    }
  };

  const colorScheme = colors[category as keyof typeof colors] || colors.interior;

  const panoramaSvg = `
    <svg width="4096" height="2048" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <radialGradient id="skyGradient-${category}" cx="50%" cy="30%" r="70%">
          <stop offset="0%" style="stop-color:${colorScheme.primary};stop-opacity:1" />
          <stop offset="70%" style="stop-color:${colorScheme.secondary};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${colorScheme.accent};stop-opacity:1" />
        </radialGradient>
        <linearGradient id="groundGradient-${category}" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#D2B48C;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#8B7355;stop-opacity:1" />
        </linearGradient>
      </defs>
      
      <!-- 天空/天花板 -->
      <rect width="100%" height="50%" fill="url(#skyGradient-${category})"/>
      
      <!-- 地面 -->
      <rect y="50%" width="100%" height="50%" fill="url(#groundGradient-${category})"/>
      
      <!-- 装饰元素 -->
      ${category === 'interior' ? `
        <!-- 家具轮廓 -->
        <rect x="20%" y="60%" width="300" height="150" fill="#8B4513" rx="20" opacity="0.8"/>
        <rect x="60%" y="55%" width="200" height="120" fill="#2F2F2F" opacity="0.7"/>
        <rect x="10%" y="30%" width="150" height="200" fill="#87CEEB" opacity="0.6"/>
      ` : category === 'exterior' ? `
        <!-- 建筑轮廓 -->
        <rect x="30%" y="40%" width="400" height="300" fill="#696969" opacity="0.8"/>
        <rect x="70%" y="35%" width="300" height="350" fill="#A0A0A0" opacity="0.7"/>
        <!-- 树木 -->
        <circle cx="15%" cy="70%" r="80" fill="#228B22" opacity="0.8"/>
        <circle cx="85%" cy="65%" r="100" fill="#32CD32" opacity="0.7"/>
      ` : category === 'nature' ? `
        <!-- 山脉 -->
        <polygon points="0,1200 800,800 1600,1000 2400,600 3200,900 4096,700 4096,1200" fill="#8B4513" opacity="0.8"/>
        <!-- 云朵 -->
        <ellipse cx="20%" cy="25%" rx="200" ry="80" fill="white" opacity="0.8"/>
        <ellipse cx="70%" cy="20%" rx="180" ry="70" fill="white" opacity="0.6"/>
      ` : `
        <!-- 办公设备 -->
        <rect x="25%" y="55%" width="500" height="200" fill="#C0C0C0" opacity="0.8"/>
        <rect x="65%" y="45%" width="250" height="150" fill="#2F2F2F" opacity="0.7"/>
      `}
      
      <!-- 主标题 -->
      <text x="50%" y="35%" font-family="Arial, sans-serif" font-size="150" fill="white" text-anchor="middle" opacity="0.9">
        ${name}
      </text>
      
      <!-- 副标题 -->
      <text x="50%" y="42%" font-family="Arial, sans-serif" font-size="80" fill="white" text-anchor="middle" opacity="0.7">
        VR720 演示全景
      </text>
      
      <!-- 导航提示点 -->
      <circle cx="20%" cy="50%" r="40" fill="#FFD700" opacity="0.9">
        <animate attributeName="r" values="30;50;30" dur="2s" repeatCount="indefinite"/>
      </circle>
      <text x="20%" y="58%" font-family="Arial, sans-serif" font-size="32" fill="black" text-anchor="middle" font-weight="bold">
        点击
      </text>
      
      <circle cx="80%" cy="50%" r="40" fill="#FFD700" opacity="0.9">
        <animate attributeName="r" values="30;50;30" dur="2s" repeatCount="indefinite" begin="1s"/>
      </circle>
      <text x="80%" y="58%" font-family="Arial, sans-serif" font-size="32" fill="black" text-anchor="middle" font-weight="bold">
        导航
      </text>
      
      <!-- Quality badge -->
      <rect x="85%" y="5%" width="200" height="80" fill="rgba(0,0,0,0.7)" rx="10"/>
      <text x="95%" y="8%" font-family="Arial, sans-serif" font-size="32" fill="#FFD700" text-anchor="middle" font-weight="bold">
        4K HD
      </text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(panoramaSvg)))}`;
};

// Get all demo panoramic images
export const getAllDemoPanoramas = (): RealPanoramaImage[] => {
  return [...realPanoramaImages, ...generateMoreDemoPanoramas()];
};

// Filter images by category
export const getPanoramasByCategory = (category: string): RealPanoramaImage[] => {
  const allImages = getAllDemoPanoramas();
  if (category === 'all') return allImages;
  return allImages.filter(img => img.category === category);
};

// Create real estate specific panorama collection
export const createRealEstateCollection = (): RealPanoramaImage[] => {
  return [
    {
      id: 'luxury-living-room',
      name: 'Luxury Living Room',
      description: 'Spacious luxury living room with Italian leather sofas, crystal chandelier and marble flooring',
      url: generateCustomPanorama('Luxury Living Room', 'interior'),
      category: 'interior',
      location: 'Beverly Hills Mansion',
      photographer: 'VR720 Real Estate',
      resolution: '8192x4096',
      aspectRatio: 2.0
    },
    {
      id: 'master-bedroom',
      name: 'Master Suite',
      description: 'Luxurious master bedroom with walk-in closet, private balcony and ensuite bathroom',
      url: generateCustomPanorama('Master Suite', 'interior'),
      category: 'interior',
      location: 'Beverly Hills Mansion',
      photographer: 'VR720 Real Estate',
      resolution: '8192x4096',
      aspectRatio: 2.0
    },
    {
      id: 'gourmet-kitchen',
      name: 'Gourmet Kitchen',
      description: 'Professional-grade kitchen with top-tier stainless steel appliances, marble countertops and center island',
      url: generateCustomPanorama('Gourmet Kitchen', 'interior'),
      category: 'interior',
      location: 'Beverly Hills Mansion',
      photographer: 'VR720 Real Estate',
      resolution: '8192x4096',
      aspectRatio: 2.0
    },
    {
      id: 'backyard-oasis',
      name: 'Backyard Oasis',
      description: 'Private backyard paradise with infinity pool, outdoor kitchen and landscaped gardens',
      url: generateCustomPanorama('Backyard Oasis', 'exterior'),
      category: 'exterior',
      location: 'Beverly Hills Mansion',
      photographer: 'VR720 Real Estate',
      resolution: '8192x4096',
      aspectRatio: 2.0
    }
  ];
};
