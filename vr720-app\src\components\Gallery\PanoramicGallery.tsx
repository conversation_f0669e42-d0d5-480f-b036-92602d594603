'use client';

import React, { useState } from 'react';
import { 
  Eye, 
  Grid, 
  X, 
  Play,
  Image as ImageIcon,
  Home,
  ChevronRight
} from 'lucide-react';
import { PANORAMIC_IMAGES, getPanoramicCategories, getPanoramicImagesByCategory } from '@/utils/localImageManager';
import { MobileVRViewer } from '@/components/VR/MobileVRViewer';

interface PanoramicGalleryProps {
  onClose: () => void;
  onImageSelect?: (panoramaUrl: string, title: string) => void;
}

export const PanoramicGallery: React.FC<PanoramicGalleryProps> = ({
  onClose,
  onImageSelect
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [showVRViewer, setShowVRViewer] = useState(false);
  const [selectedPanorama, setSelectedPanorama] = useState<string>('');
  const [selectedTitle, setSelectedTitle] = useState<string>('');

  const categories = ['All', ...getPanoramicCategories()];
  const filteredImages = selectedCategory === 'All' 
    ? PANORAMIC_IMAGES 
    : getPanoramicImagesByCategory(selectedCategory);

  // Handle VR viewing
  const handleViewVR = (panoramaUrl: string, title: string) => {
    setSelectedPanorama(panoramaUrl);
    setSelectedTitle(title);
    setShowVRViewer(true);
  };

  // Handle image selection
  const handleSelectImage = (panoramaUrl: string, title: string) => {
    if (onImageSelect) {
      onImageSelect(panoramaUrl, title);
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Panoramic Gallery</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1"
          >
            <X size={24} />
          </button>
        </div>

        {/* Category Filter */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
          <p className="text-sm text-gray-600 mt-2">
            {filteredImages.length} panoramic images available
          </p>
        </div>

        {/* Image Grid */}
        <div className="p-4 max-h-[60vh] overflow-y-auto">
          {filteredImages.length === 0 ? (
            <div className="text-center py-12">
              <ImageIcon size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Images Found</h3>
              <p className="text-gray-600 mb-4">
                No panoramic images found in the selected category.
              </p>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
                <h4 className="font-medium text-yellow-800 mb-2">Setup Required</h4>
                <p className="text-sm text-yellow-700">
                  Please copy your panoramic images to the public/panoramas folder. 
                  See setup-local-images.md for detailed instructions.
                </p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredImages.map((image) => (
                <div key={image.id} className="bg-gray-50 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                  {/* Image Preview */}
                  <div className="aspect-video bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center relative">
                    <img
                      src={image.thumbnail || image.publicPath}
                      alt={image.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // Fallback to a placeholder if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 hover:opacity-100">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleViewVR(image.publicPath, image.name)}
                          className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full"
                          title="View in VR"
                        >
                          <Eye size={20} />
                        </button>
                        {onImageSelect && (
                          <button
                            onClick={() => handleSelectImage(image.publicPath, image.name)}
                            className="bg-green-600 hover:bg-green-700 text-white p-2 rounded-full"
                            title="Select Image"
                          >
                            <ChevronRight size={20} />
                          </button>
                        )}
                      </div>
                    </div>
                    
                    {/* Category Badge */}
                    <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                      {image.category}
                    </div>
                  </div>

                  {/* Image Info */}
                  <div className="p-4">
                    <h3 className="font-medium text-gray-900 mb-1">{image.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">{image.description}</p>
                    
                    {/* Metadata */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{image.metadata.width}x{image.metadata.height}</span>
                      <span>{image.metadata.fileSize}</span>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex space-x-2 mt-3">
                      <button
                        onClick={() => handleViewVR(image.publicPath, image.name)}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm flex items-center justify-center"
                      >
                        <Play size={14} className="mr-1" />
                        VR View
                      </button>
                      {onImageSelect && (
                        <button
                          onClick={() => handleSelectImage(image.publicPath, image.name)}
                          className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-3 rounded text-sm flex items-center justify-center"
                        >
                          <Home size={14} className="mr-1" />
                          Use
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            High-quality 360° panoramic images for VR tours
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 hover:text-gray-900"
            >
              Close
            </button>
          </div>
        </div>
      </div>

      {/* VR Viewer */}
      {showVRViewer && selectedPanorama && (
        <MobileVRViewer
          panoramaUrl={selectedPanorama}
          title={selectedTitle}
          description="Mobile-optimized 720° panoramic view"
          onClose={() => setShowVRViewer(false)}
        />
      )}
    </div>
  );
};
