'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Home } from 'lucide-react';

interface OptimizedImageProps {
  src?: string;
  alt: string;
  className?: string;
  fallbackIcon?: React.ReactNode;
  placeholder?: string;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}

export const OptimizedImage = ({
  src,
  alt,
  className = '',
  fallbackIcon = <Home size={32} className="text-blue-500" />,
  placeholder,
  priority = false,
  onLoad,
  onError
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || !containerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before the image comes into view
        threshold: 0.1
      }
    );

    observer.observe(containerRef.current);

    return () => observer.disconnect();
  }, [priority]);

  const handleImageLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  // Generate thumbnail URL from original image URL
  const getThumbnailUrl = (originalUrl: string) => {
    if (!originalUrl) return '';
    
    // If it's already a thumbnail, return as is
    if (originalUrl.includes('/thumbnails/')) {
      return originalUrl;
    }
    
    // Convert panorama URL to thumbnail URL
    const filename = originalUrl.split('/').pop();
    if (filename) {
      return `/thumbnails/${filename}`;
    }
    
    return originalUrl;
  };

  const imageUrl = src ? getThumbnailUrl(src) : '';

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden bg-gradient-to-br from-blue-100 to-blue-200 ${className}`}
    >
      {/* Loading placeholder */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          {placeholder ? (
            <div className="text-center">
              <div className="animate-pulse bg-blue-200 rounded w-8 h-8 mx-auto mb-2"></div>
              <div className="text-xs text-blue-600">{placeholder}</div>
            </div>
          ) : (
            <div className="animate-pulse">
              <div className="bg-blue-200 rounded-full w-8 h-8"></div>
            </div>
          )}
        </div>
      )}

      {/* Error state or no image */}
      {(hasError || !src) && !isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          {fallbackIcon}
        </div>
      )}

      {/* Actual image */}
      {src && isInView && (
        <img
          ref={imgRef}
          src={imageUrl}
          alt={alt}
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            isLoading ? 'opacity-0' : 'opacity-100'
          }`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
        />
      )}

      {/* Loading overlay */}
      {isLoading && isInView && src && (
        <div className="absolute inset-0 bg-blue-100/50 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mb-2"></div>
            <div className="text-xs text-blue-600">Loading...</div>
          </div>
        </div>
      )}
    </div>
  );
};

// Progressive image component for better UX
export const ProgressiveImage = ({
  src,
  lowQualitySrc,
  alt,
  className = '',
  fallbackIcon,
  priority = false,
  onLoad,
  onError
}) => {
  const [highQualityLoaded, setHighQualityLoaded] = useState(false);
  const [lowQualityLoaded, setLowQualityLoaded] = useState(false);

  const handleLowQualityLoad = () => {
    setLowQualityLoaded(true);
  };

  const handleHighQualityLoad = () => {
    setHighQualityLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    onError?.();
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Low quality image (thumbnail) */}
      {lowQualitySrc && (
        <img
          src={lowQualitySrc}
          alt={alt}
          className={`absolute inset-0 w-full h-full object-cover filter blur-sm transition-opacity duration-300 ${
            lowQualityLoaded && !highQualityLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={handleLowQualityLoad}
          loading={priority ? 'eager' : 'lazy'}
        />
      )}

      {/* High quality image */}
      {src && (
        <OptimizedImage
          src={src}
          alt={alt}
          className="absolute inset-0"
          fallbackIcon={fallbackIcon}
          priority={priority}
          onLoad={handleHighQualityLoad}
          onError={handleError}
        />
      )}

      {/* Loading state */}
      {!lowQualityLoaded && !highQualityLoaded && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
          <div className="animate-pulse">
            {fallbackIcon ?? <Home size={32} className="text-blue-500" />}
          </div>
        </div>
      )}
    </div>
  );
};
