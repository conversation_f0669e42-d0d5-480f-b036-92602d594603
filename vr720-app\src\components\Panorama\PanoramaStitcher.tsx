'use client';

import React, { useState, useCallback } from 'react';
import { Loader2, Download, Eye, Settings } from 'lucide-react';

interface PhotoData {
  data: string;
  metadata: {
    timestamp: number;
    orientation: { alpha: number; beta: number; gamma: number };
    step: number;
    resolution: { width: number; height: number };
  };
}

interface PanoramaStitcherProps {
  photos: PhotoData[];
  onPanoramaReady: (panoramaUrl: string) => void;
}

export default function PanoramaStitcher({ photos, onPanoramaReady }: PanoramaStitcherProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [panoramaUrl, setPanoramaUrl] = useState<string>('');
  const [processingStep, setProcessingStep] = useState('');

  // Simplified panorama stitching algorithm
  const stitchPanorama = useCallback(async () => {
    if (photos.length < 4) {
      alert('At least 4 photos are required to generate a panorama');
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setProcessingStep('Preparing images...');

    try {
      // Create canvas for stitching
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Unable to create canvas context');

      // Set panorama dimensions (2:1 ratio, suitable for 360-degree panorama)
      const panoramaWidth = 4096;
      const panoramaHeight = 2048;
      canvas.width = panoramaWidth;
      canvas.height = panoramaHeight;

      // Fill with black background
      ctx.fillStyle = '#000000';
      ctx.fillRect(0, 0, panoramaWidth, panoramaHeight);

      setProgress(10);
      setProcessingStep('Loading images...');

      // Load all images
      const images = await Promise.all(
        photos.map(photo => {
          return new Promise<HTMLImageElement>((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = photo.data;
          });
        })
      );

      setProgress(30);
      setProcessingStep('Calculating stitching positions...');

      // Sort by shooting steps
      const sortedPhotos = photos
        .map((photo, index) => ({ photo, image: images[index] }))
        .sort((a, b) => a.photo.metadata.step - b.photo.metadata.step);

      setProgress(50);
      setProcessingStep('Stitching images...');

      // Simple horizontal stitching algorithm
      const segmentWidth = panoramaWidth / sortedPhotos.length;

      sortedPhotos.forEach(({ image }, index) => {
        const x = index * segmentWidth;
        const y = 0;

        // Calculate scale ratio to fit height
        const scale = panoramaHeight / image.height;
        const scaledWidth = image.width * scale;

        // Draw image
        ctx.drawImage(
          image,
          x,
          y,
          Math.min(segmentWidth, scaledWidth),
          panoramaHeight
        );
        
        setProgress(50 + (index / sortedPhotos.length) * 40);
      });

      setProgress(90);
      setProcessingStep('Generating panorama...');

      // Apply simple edge blending
      await applyEdgeBlending(ctx, panoramaWidth, panoramaHeight, sortedPhotos.length);

      setProgress(95);
      setProcessingStep('Finalizing...');

      // Generate final panorama URL
      const panoramaDataUrl = canvas.toDataURL('image/jpeg', 0.9);
      setPanoramaUrl(panoramaDataUrl);
      onPanoramaReady(panoramaDataUrl);

      setProgress(100);
      setProcessingStep('Complete!');

      setTimeout(() => {
        setIsProcessing(false);
      }, 1000);

    } catch (error) {
      console.error('Panorama stitching failed:', error);
      alert('Panorama stitching failed, please try again');
      setIsProcessing(false);
    }
  }, [photos, onPanoramaReady]);

  // Simple edge blending algorithm
  const applyEdgeBlending = async (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number,
    segments: number
  ) => {
    const segmentWidth = width / segments;
    const blendWidth = 50; // Blend area width

    for (let i = 1; i < segments; i++) {
      const x = i * segmentWidth;

      // Create gradient mask
      const gradient = ctx.createLinearGradient(x - blendWidth, 0, x + blendWidth, 0);
      gradient.addColorStop(0, 'rgba(0,0,0,0)');
      gradient.addColorStop(0.5, 'rgba(0,0,0,0.5)');
      gradient.addColorStop(1, 'rgba(0,0,0,0)');
      
      ctx.globalCompositeOperation = 'multiply';
      ctx.fillStyle = gradient;
      ctx.fillRect(x - blendWidth, 0, blendWidth * 2, height);
      ctx.globalCompositeOperation = 'source-over';
    }
  };

  // Download panorama
  const downloadPanorama = useCallback(() => {
    if (!panoramaUrl) return;
    
    const link = document.createElement('a');
    link.download = `panorama_${Date.now()}.jpg`;
    link.href = panoramaUrl;
    link.click();
  }, [panoramaUrl]);

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold mb-6 text-center">Panorama Generation</h2>

        {/* Photo preview */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Captured Photos ({photos.length} photos)</h3>
          <div className="grid grid-cols-4 gap-4">
            {photos.map((photo, index) => (
              <div key={index} className="relative">
                <img
                  src={photo.data}
                  alt={`Photo ${index + 1}`}
                  className="w-full h-24 object-cover rounded border"
                />
                <div className="absolute top-1 right-1 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  {photo.metadata.step}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Processing controls */}
        <div className="mb-6">
          {!isProcessing && !panoramaUrl && (
            <button
              onClick={stitchPanorama}
              disabled={photos.length < 4}
              className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white py-3 px-6 rounded-lg font-semibold transition-colors"
            >
              {photos.length < 4 ? 'Need at least 4 photos' : 'Start Generating Panorama'}
            </button>
          )}

          {isProcessing && (
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-2">
                <Loader2 className="animate-spin" size={20} />
                <span className="text-gray-600">{processingStep}</span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              
              <div className="text-center text-sm text-gray-500">
                {progress.toFixed(0)}% Complete
              </div>
            </div>
          )}
        </div>

        {/* Panorama preview */}
        {panoramaUrl && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Generated Panorama</h3>
            <div className="relative">
              <img
                src={panoramaUrl}
                alt="Generated panorama"
                className="w-full h-64 object-cover rounded border"
              />
            </div>

            <div className="flex space-x-4">
              <button
                onClick={downloadPanorama}
                className="flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded transition-colors"
              >
                <Download size={16} />
                <span>Download Panorama</span>
              </button>

              <button
                onClick={() => onPanoramaReady(panoramaUrl)}
                className="flex items-center space-x-2 bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded transition-colors"
              >
                <Eye size={16} />
                <span>View VR Effect</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
