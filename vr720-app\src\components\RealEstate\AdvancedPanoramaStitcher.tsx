'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Loader2, Download, <PERSON>, Settings, AlertTriangle, CheckCircle, RotateCcw } from 'lucide-react';

interface PhotoData {
  data: string;
  metadata: {
    timestamp: number;
    orientation: { alpha: number; beta: number; gamma: number };
    step: number;
    roomType?: string;
    roomName?: string;
    resolution: { width: number; height: number };
    qualityScore?: number;
    warnings?: string[];
  };
}

interface AdvancedPanoramaStitcherProps {
  photos: PhotoData[];
  onPanoramaReady: (panoramaUrl: string, metadata: any) => void;
}

interface StitchingQuality {
  overallScore: number;
  issues: string[];
  recommendations: string[];
}

export default function AdvancedPanoramaStitcher({ photos, onPanoramaReady }: AdvancedPanoramaStitcherProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [panoramaUrl, setPanoramaUrl] = useState<string>('');
  const [processingStep, setProcessingStep] = useState('');
  const [stitchingQuality, setStitchingQuality] = useState<StitchingQuality | null>(null);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [stitchingSettings, setStitchingSettings] = useState({
    outputResolution: '4K',
    blendingMode: 'multiband',
    exposureCompensation: true,
    colorCorrection: true,
    distortionCorrection: true
  });

  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Analyze photo quality and shooting conditions
  const analyzePhotos = useCallback(() => {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let totalScore = 0;

    photos.forEach((photo, index) => {
      const score = photo.metadata.qualityScore || 0;
      totalScore += score;

      if (score < 70) {
        issues.push(`Photo ${index + 1} has low quality (${score} points)`);
      }

      if (photo.metadata.warnings && photo.metadata.warnings.length > 0) {
        issues.push(`Photo ${index + 1}: ${photo.metadata.warnings.join(', ')}`);
      }
    });

    // Check photo count
    if (photos.length < 6) {
      issues.push('Insufficient photos, recommend at least 6 photos');
      recommendations.push('Add more photos from different angles for better stitching');
    }

    // Check angle coverage
    const angles = photos.map(p => p.metadata.orientation.alpha).sort((a, b) => a - b);
    const gaps = [];
    for (let i = 1; i < angles.length; i++) {
      const gap = angles[i] - angles[i - 1];
      if (gap > 60) {
        gaps.push(gap);
      }
    }
    
    if (gaps.length > 0) {
      issues.push('Uneven angle coverage with large gaps');
      recommendations.push('Take additional photos in areas with large gaps');
    }

    const overallScore = Math.round(totalScore / photos.length);
    
    if (overallScore >= 85) {
      recommendations.push('Excellent photo quality, can generate high-quality panorama');
    } else if (overallScore >= 70) {
      recommendations.push('Good photo quality, recommend enabling advanced processing options');
    } else {
      recommendations.push('Recommend retaking some low-quality photos');
    }

    return { overallScore, issues, recommendations };
  }, [photos]);

  // Advanced panorama stitching algorithm
  const stitchPanorama = useCallback(async () => {
    if (photos.length < 4) {
      alert('At least 4 photos are required to generate a panorama');
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setProcessingStep('Analyzing photo quality...');

    try {
      // Analyze photo quality
      const quality = analyzePhotos();
      setStitchingQuality(quality);
      setProgress(10);

      setProcessingStep('Preprocessing images...');

      // Create high-resolution canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Unable to create canvas context');

      // Determine output resolution based on settings
      const resolutions = {
        '2K': { width: 2048, height: 1024 },
        '4K': { width: 4096, height: 2048 },
        '8K': { width: 8192, height: 4096 }
      };
      
      const resolution = resolutions[stitchingSettings.outputResolution as keyof typeof resolutions];
      canvas.width = resolution.width;
      canvas.height = resolution.height;

      // Fill black background
      ctx.fillStyle = '#000000';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      setProgress(20);

      setProcessingStep('Loading and preprocessing images...');

      // Load all images and preprocess
      const processedImages = await Promise.all(
        photos.map(async (photo, index) => {
          return new Promise<{image: HTMLImageElement, metadata: any}>((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
              // Apply image enhancement
              if (stitchingSettings.exposureCompensation || stitchingSettings.colorCorrection) {
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                if (tempCtx) {
                  tempCanvas.width = img.width;
                  tempCanvas.height = img.height;

                  // Exposure compensation
                  if (stitchingSettings.exposureCompensation) {
                    tempCtx.filter = 'brightness(1.1) contrast(1.05)';
                  }

                  // Color correction
                  if (stitchingSettings.colorCorrection) {
                    tempCtx.filter += ' saturate(1.1) hue-rotate(2deg)';
                  }

                  tempCtx.drawImage(img, 0, 0);
                  tempCtx.filter = 'none';

                  // Create new image object
                  const enhancedImg = new Image();
                  enhancedImg.onload = () => resolve({image: enhancedImg, metadata: photo.metadata});
                  enhancedImg.src = tempCanvas.toDataURL('image/jpeg', 0.95);
                } else {
                  resolve({image: img, metadata: photo.metadata});
                }
              } else {
                resolve({image: img, metadata: photo.metadata});
              }
            };
            img.onerror = reject;
            img.src = photo.data;
          });
        })
      );

      setProgress(40);
      setProcessingStep('Calculating optimal stitching parameters...');

      // Sort by shooting step
      const sortedImages = processedImages
        .sort((a, b) => a.metadata.step - b.metadata.step);

      setProgress(50);
      setProcessingStep('Executing advanced stitching algorithm...');

      // Advanced stitching algorithm - using overlap detection
      const segmentWidth = canvas.width / sortedImages.length;
      const overlapRatio = 0.15; // 15% overlap
      
      for (let i = 0; i < sortedImages.length; i++) {
        const { image } = sortedImages[i];
        
        // Calculate position and size
        const x = i * segmentWidth * (1 - overlapRatio);
        const width = segmentWidth * (1 + overlapRatio);
        const scale = canvas.height / image.height;
        const scaledWidth = Math.min(width, image.width * scale);

        // Apply perspective correction (simplified)
        if (stitchingSettings.distortionCorrection) {
          ctx.save();

          // Simple perspective transformation
          const skew = (i - sortedImages.length / 2) * 0.02;
          ctx.transform(1, skew, 0, 1, 0, 0);
        }

        // Draw image
        ctx.drawImage(
          image,
          x,
          0,
          scaledWidth,
          canvas.height
        );
        
        if (stitchingSettings.distortionCorrection) {
          ctx.restore();
        }
        
        setProgress(50 + (i / sortedImages.length) * 30);
      }

      setProgress(80);
      setProcessingStep('Applying advanced blending algorithms...');

      // Multi-band blending
      if (stitchingSettings.blendingMode === 'multiband') {
        await applyMultibandBlending(ctx, canvas.width, canvas.height, sortedImages.length);
      } else {
        await applyLinearBlending(ctx, canvas.width, canvas.height, sortedImages.length);
      }

      setProgress(90);
      setProcessingStep('Optimization and post-processing...');

      // Final optimization
      await applyFinalOptimization(ctx, canvas.width, canvas.height);

      setProgress(95);
      setProcessingStep('Generating final panorama...');

      // Generate final panorama URL
      const panoramaDataUrl = canvas.toDataURL('image/jpeg', 0.92);
      setPanoramaUrl(panoramaDataUrl);

      // Generate metadata
      const panoramaMetadata = {
        resolution: { width: canvas.width, height: canvas.height },
        sourcePhotos: photos.length,
        roomType: photos[0]?.metadata.roomType,
        roomName: photos[0]?.metadata.roomName,
        qualityScore: quality.overallScore,
        stitchingSettings,
        timestamp: Date.now()
      };
      
      onPanoramaReady(panoramaDataUrl, panoramaMetadata);

      setProgress(100);
      setProcessingStep('Complete!');

      setTimeout(() => {
        setIsProcessing(false);
      }, 1000);

    } catch (error) {
      console.error('Panorama stitching failed:', error);
      alert('Panorama stitching failed, please try again');
      setIsProcessing(false);
    }
  }, [photos, onPanoramaReady, stitchingSettings, analyzePhotos]);

  // Multi-band blending algorithm
  const applyMultibandBlending = async (
    ctx: CanvasRenderingContext2D, 
    width: number, 
    height: number, 
    segments: number
  ) => {
    const segmentWidth = width / segments;
    const blendWidth = segmentWidth * 0.1;

    for (let i = 1; i < segments; i++) {
      const x = i * segmentWidth;
      
      // Create Gaussian blur mask
      const gradient = ctx.createLinearGradient(x - blendWidth, 0, x + blendWidth, 0);
      gradient.addColorStop(0, 'rgba(255,255,255,0)');
      gradient.addColorStop(0.5, 'rgba(255,255,255,0.8)');
      gradient.addColorStop(1, 'rgba(255,255,255,0)');
      
      ctx.globalCompositeOperation = 'soft-light';
      ctx.fillStyle = gradient;
      ctx.fillRect(x - blendWidth, 0, blendWidth * 2, height);
      ctx.globalCompositeOperation = 'source-over';
    }
  };

  // Linear blending algorithm
  const applyLinearBlending = async (
    ctx: CanvasRenderingContext2D, 
    width: number, 
    height: number, 
    segments: number
  ) => {
    const segmentWidth = width / segments;
    const blendWidth = 50;

    for (let i = 1; i < segments; i++) {
      const x = i * segmentWidth;
      
      const gradient = ctx.createLinearGradient(x - blendWidth, 0, x + blendWidth, 0);
      gradient.addColorStop(0, 'rgba(0,0,0,0)');
      gradient.addColorStop(0.5, 'rgba(0,0,0,0.3)');
      gradient.addColorStop(1, 'rgba(0,0,0,0)');
      
      ctx.globalCompositeOperation = 'multiply';
      ctx.fillStyle = gradient;
      ctx.fillRect(x - blendWidth, 0, blendWidth * 2, height);
      ctx.globalCompositeOperation = 'source-over';
    }
  };

  // Final optimization
  const applyFinalOptimization = async (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => {
    // Apply slight sharpening and contrast enhancement
    const imageData = ctx.getImageData(0, 0, width, height);
    const data = imageData.data;

    // Simple sharpening filter
    for (let i = 0; i < data.length; i += 4) {
      data[i] = Math.min(255, data[i] * 1.02);     // R
      data[i + 1] = Math.min(255, data[i + 1] * 1.02); // G
      data[i + 2] = Math.min(255, data[i + 2] * 1.02); // B
    }
    
    ctx.putImageData(imageData, 0, 0);
  };

  // Download panorama
  const downloadPanorama = useCallback(() => {
    if (!panoramaUrl) return;
    
    const link = document.createElement('a');
    const roomName = photos[0]?.metadata.roomName || 'panorama';
    link.download = `${roomName}_panorama_${Date.now()}.jpg`;
    link.href = panoramaUrl;
    link.click();
  }, [panoramaUrl, photos]);

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-3xl font-bold mb-6 text-center text-gray-800">
          🏠 Professional Real Estate Panorama Stitching
        </h2>

        {/* Photo preview and quality analysis */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-gray-700">
              Captured Photos ({photos.length} photos)
            </h3>
            {photos[0]?.metadata.roomName && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                {photos[0].metadata.roomName}
              </div>
            )}
          </div>
          
          <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-3 mb-4">
            {photos.map((photo, index) => (
              <div key={index} className="relative group">
                <img
                  src={photo.data}
                  alt={`Photo ${index + 1}`}
                  className="w-full h-20 object-cover rounded border-2 border-gray-200 group-hover:border-blue-400 transition-colors"
                />
                <div className="absolute top-1 right-1 bg-black/70 text-white text-xs px-1 py-0.5 rounded">
                  {photo.metadata.step}
                </div>
                {photo.metadata.qualityScore && (
                  <div className={`absolute bottom-1 left-1 text-xs px-1 py-0.5 rounded ${
                    photo.metadata.qualityScore >= 85 ? 'bg-green-500 text-white' :
                    photo.metadata.qualityScore >= 70 ? 'bg-yellow-500 text-black' :
                    'bg-red-500 text-white'
                  }`}>
                    {photo.metadata.qualityScore}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Quality analysis results */}
        {stitchingQuality && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-semibold text-gray-700">Quality Analysis</h4>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                stitchingQuality.overallScore >= 85 ? 'bg-green-100 text-green-800' :
                stitchingQuality.overallScore >= 70 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                Score: {stitchingQuality.overallScore}/100
              </div>
            </div>
            
            {stitchingQuality.issues.length > 0 && (
              <div className="mb-3">
                <div className="flex items-center mb-2">
                  <AlertTriangle size={16} className="text-yellow-600 mr-2" />
                  <span className="font-medium text-gray-700">Issues Found:</span>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  {stitchingQuality.issues.map((issue, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-yellow-500 mr-2">•</span>
                      {issue}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            <div>
              <div className="flex items-center mb-2">
                <CheckCircle size={16} className="text-green-600 mr-2" />
                <span className="font-medium text-gray-700">Recommendations:</span>
              </div>
              <ul className="text-sm text-gray-600 space-y-1">
                {stitchingQuality.recommendations.map((rec, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 mr-2">•</span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* Advanced settings */}
        <div className="mb-6">
          <button
            onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
            className="flex items-center text-gray-700 hover:text-gray-900 transition-colors"
          >
            <Settings size={20} className="mr-2" />
            Advanced Settings
            <span className="ml-2 text-sm">
              {showAdvancedSettings ? '▼' : '▶'}
            </span>
          </button>
          
          {showAdvancedSettings && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Output Resolution
                </label>
                <select
                  value={stitchingSettings.outputResolution}
                  onChange={(e) => setStitchingSettings(prev => ({
                    ...prev,
                    outputResolution: e.target.value
                  }))}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="2K">2K (2048×1024)</option>
                  <option value="4K">4K (4096×2048)</option>
                  <option value="8K">8K (8192×4096)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Blending Mode
                </label>
                <select
                  value={stitchingSettings.blendingMode}
                  onChange={(e) => setStitchingSettings(prev => ({
                    ...prev,
                    blendingMode: e.target.value
                  }))}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="linear">Linear Blending</option>
                  <option value="multiband">Multiband Blending</option>
                </select>
              </div>
              
              <div className="md:col-span-2 space-y-3">
                {[
                  { key: 'exposureCompensation', label: 'Exposure Compensation' },
                  { key: 'colorCorrection', label: 'Color Correction' },
                  { key: 'distortionCorrection', label: 'Distortion Correction' }
                ].map(({ key, label }) => (
                  <label key={key} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={stitchingSettings[key as keyof typeof stitchingSettings] as boolean}
                      onChange={(e) => setStitchingSettings(prev => ({
                        ...prev,
                        [key]: e.target.checked
                      }))}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">{label}</span>
                  </label>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Processing controls */}
        <div className="mb-6">
          {!isProcessing && !panoramaUrl && (
            <button
              onClick={stitchPanorama}
              disabled={photos.length < 4}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white py-4 px-6 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 disabled:transform-none"
            >
              {photos.length < 4 ? 'Need at least 4 photos' : '🚀 Start Professional Panorama Stitching'}
            </button>
          )}

          {isProcessing && (
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-3">
                <Loader2 className="animate-spin text-blue-600" size={24} />
                <span className="text-gray-700 font-medium">{processingStep}</span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-4">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-4 rounded-full transition-all duration-500"
                  style={{ width: `${progress}%` }}
                />
              </div>
              
              <div className="text-center text-sm text-gray-600">
                {progress.toFixed(0)}% Complete
              </div>
            </div>
          )}
        </div>

        {/* Panorama preview */}
        {panoramaUrl && (
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-700">Generated Professional Panorama</h3>
            <div className="relative">
              <img
                src={panoramaUrl}
                alt="Generated panorama"
                className="w-full h-80 object-cover rounded-lg border shadow-lg"
              />
              <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                {stitchingSettings.outputResolution} Resolution
              </div>
            </div>

            <div className="flex flex-wrap gap-4">
              <button
                onClick={downloadPanorama}
                className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg transition-colors"
              >
                <Download size={20} />
                <span>Download Panorama</span>
              </button>
              
              <button
                onClick={() => onPanoramaReady(panoramaUrl, {
                  resolution: stitchingSettings.outputResolution,
                  roomType: photos[0]?.metadata.roomType,
                  roomName: photos[0]?.metadata.roomName
                })}
                className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg transition-colors"
              >
                <Eye size={20} />
                <span>VR Panorama Experience</span>
              </button>

              <button
                onClick={() => {
                  setPanoramaUrl('');
                  setStitchingQuality(null);
                  setProgress(0);
                }}
                className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg transition-colors"
              >
                <RotateCcw size={20} />
                <span>Re-stitch</span>
              </button>
            </div>
          </div>
        )}
      </div>
      
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
}
