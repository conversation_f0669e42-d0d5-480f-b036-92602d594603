'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Home, MapPin, Move, RotateCcw, ZoomIn, ZoomOut, Maximize, Info, Navigation } from 'lucide-react';

// Enhanced room interface with multiple viewpoints
interface ViewPoint {
  id: string;
  name: string;
  description?: string;
  panoramaUrl: string;
  position: {
    x: number;
    y: number;
    z: number;
  };
  rotation: {
    yaw: number;
    pitch: number;
  };
}

interface EnhancedRoom {
  id: string;
  name: string;
  type: string;
  description?: string;
  viewPoints: ViewPoint[];
  hotspots: Array<{
    id: string;
    targetRoomId: string;
    targetViewPointId?: string;
    label: string;
    position: { yaw: number; pitch: number };
  }>;
}

interface EnhancedHouse {
  id: string;
  name: string;
  address: string;
  description?: string;
  rooms: EnhancedRoom[];
}

interface EnhancedHouseTourViewerProps {
  house: EnhancedHouse;
  startingRoomId?: string;
  startingViewPointId?: string;
  onBack: () => void;
  onRoomChange?: (roomId: string, viewPointId?: string) => void;
}

export default function EnhancedHouseTourViewer({
  house,
  startingRoomId,
  startingViewPointId,
  onBack,
  onRoomChange
}: EnhancedHouseTourViewerProps) {
  const [currentRoomId, setCurrentRoomId] = useState(startingRoomId || house.rooms[0]?.id || '');
  const [currentViewPointId, setCurrentViewPointId] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [showRoomList, setShowRoomList] = useState(false);
  const [showViewPoints, setShowViewPoints] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const viewerRef = useRef<HTMLDivElement>(null);

  const currentRoom = house.rooms.find(room => room.id === currentRoomId);
  const currentViewPoint = currentRoom?.viewPoints.find(vp => vp.id === currentViewPointId) || currentRoom?.viewPoints[0];

  useEffect(() => {
    if (currentRoom && !currentViewPointId) {
      setCurrentViewPointId(currentRoom.viewPoints[0]?.id || '');
    }
  }, [currentRoom, currentViewPointId]);

  useEffect(() => {
    if (startingViewPointId) {
      setCurrentViewPointId(startingViewPointId);
    }
  }, [startingViewPointId]);

  // Navigate to different room
  const navigateToRoom = (roomId: string, viewPointId?: string) => {
    setIsLoading(true);
    setCurrentRoomId(roomId);
    
    const targetRoom = house.rooms.find(room => room.id === roomId);
    const targetViewPointId = viewPointId || targetRoom?.viewPoints[0]?.id || '';
    setCurrentViewPointId(targetViewPointId);
    
    onRoomChange?.(roomId, targetViewPointId);
    setShowRoomList(false);
    
    // Simulate loading time
    setTimeout(() => setIsLoading(false), 1000);
  };

  // Navigate to different viewpoint in same room
  const navigateToViewPoint = (viewPointId: string) => {
    setIsLoading(true);
    setCurrentViewPointId(viewPointId);
    setShowViewPoints(false);
    
    // Simulate loading time
    setTimeout(() => setIsLoading(false), 500);
  };

  // Get demo panorama URL (in real app, this would come from the viewpoint data)
  const getPanoramaUrl = () => {
    if (currentViewPoint?.panoramaUrl) {
      return currentViewPoint.panoramaUrl;
    }
    
    // Demo panorama URLs based on room type and viewpoint
    const demoUrls = {
      'living-room': [
        'https://pannellum.org/images/alma.jpg',
        'https://pannellum.org/images/cerro-toco-0.jpg'
      ],
      'kitchen': [
        'https://pannellum.org/images/jfk.jpg',
        'https://pannellum.org/images/alma.jpg'
      ],
      'bedroom': [
        'https://pannellum.org/images/cerro-toco-0.jpg',
        'https://pannellum.org/images/jfk.jpg'
      ],
      'bathroom': [
        'https://pannellum.org/images/alma.jpg',
        'https://pannellum.org/images/cerro-toco-0.jpg'
      ]
    };
    
    const roomType = currentRoom?.type as keyof typeof demoUrls || 'living-room';
    const viewPointIndex = currentRoom?.viewPoints.findIndex(vp => vp.id === currentViewPointId) || 0;
    return demoUrls[roomType]?.[viewPointIndex] || demoUrls['living-room'][0];
  };

  useEffect(() => {
    if (currentViewPoint && viewerRef.current) {
      setIsLoading(true);

      // Simple image display instead of Pannellum for now
      const img = document.createElement('img');
      img.src = getPanoramaUrl();
      img.style.width = '100%';
      img.style.height = '100%';
      img.style.objectFit = 'cover';
      img.alt = `${currentRoom?.name} - ${currentViewPoint.name}`;

      img.onload = () => {
        if (viewerRef.current) {
          viewerRef.current.innerHTML = '';
          viewerRef.current.appendChild(img);
        }
        setIsLoading(false);
      };

      img.onerror = () => {
        if (viewerRef.current) {
          viewerRef.current.innerHTML = `
            <div class="flex items-center justify-center h-full bg-gray-900 text-white">
              <div class="text-center">
                <h3 class="text-xl font-semibold mb-2">Demo Panorama View</h3>
                <p class="text-gray-300">${currentRoom?.name} - ${currentViewPoint.name}</p>
                <p class="text-sm text-gray-400 mt-4">360° panoramic view would be displayed here</p>
              </div>
            </div>
          `;
        }
        setIsLoading(false);
      };
    }
  }, [currentViewPoint, currentRoom]);

  if (!currentRoom || !currentViewPoint) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <h2 className="text-2xl font-bold mb-4">Room Not Found</h2>
          <p className="text-gray-300 mb-6">The requested room could not be loaded.</p>
          <button
            onClick={onBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-screen bg-black overflow-hidden">
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-50">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
            <h3 className="text-xl font-semibold mb-2">Loading Virtual Tour...</h3>
            <p className="text-gray-300">
              {currentRoom.name} - {currentViewPoint.name}
            </p>
          </div>
        </div>
      )}

      {/* Panorama viewer */}
      <div ref={viewerRef} className="w-full h-full" />

      {/* Top navigation bar */}
      <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/70 to-transparent p-4 z-40">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-colors"
            >
              <ArrowLeft size={20} />
            </button>
            
            <div className="text-white">
              <h1 className="text-lg font-semibold">{house.name}</h1>
              <p className="text-sm text-gray-300">{house.address}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowInfo(!showInfo)}
              className="bg-black/50 hover:bg-black/70 text-white p-3 rounded-full transition-colors"
            >
              <Info size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Bottom control panel */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 z-40">
        <div className="flex items-center justify-between">
          {/* Current location info */}
          <div className="text-white">
            <h3 className="text-lg font-semibold">{currentRoom.name}</h3>
            <p className="text-sm text-gray-300">{currentViewPoint.name}</p>
            {currentViewPoint.description && (
              <p className="text-xs text-gray-400 mt-1">{currentViewPoint.description}</p>
            )}
          </div>

          {/* Control buttons */}
          <div className="flex items-center space-x-2">
            {/* Viewpoints in current room */}
            {currentRoom.viewPoints.length > 1 && (
              <button
                onClick={() => setShowViewPoints(!showViewPoints)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
              >
                <Move size={16} />
                <span>Viewpoints ({currentRoom.viewPoints.length})</span>
              </button>
            )}

            {/* Room navigation */}
            <button
              onClick={() => setShowRoomList(!showRoomList)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
            >
              <Home size={16} />
              <span>Rooms ({house.rooms.length})</span>
            </button>
          </div>
        </div>
      </div>

      {/* Room list modal */}
      {showRoomList && (
        <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Select Room</h3>
            <div className="space-y-3">
              {house.rooms.map((room) => (
                <button
                  key={room.id}
                  onClick={() => navigateToRoom(room.id)}
                  className={`w-full text-left p-4 rounded-lg border transition-colors ${
                    room.id === currentRoomId
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold text-gray-900">{room.name}</h4>
                      <p className="text-sm text-gray-600 capitalize">{room.type.replace('-', ' ')}</p>
                      {room.description && (
                        <p className="text-xs text-gray-500 mt-1">{room.description}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <span className="text-xs text-gray-500">
                        {room.viewPoints.length} viewpoint{room.viewPoints.length !== 1 ? 's' : ''}
                      </span>
                    </div>
                  </div>
                </button>
              ))}
            </div>
            <button
              onClick={() => setShowRoomList(false)}
              className="w-full mt-4 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Viewpoints modal */}
      {showViewPoints && (
        <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Viewpoints in {currentRoom.name}
            </h3>
            <div className="space-y-3">
              {currentRoom.viewPoints.map((viewPoint) => (
                <button
                  key={viewPoint.id}
                  onClick={() => navigateToViewPoint(viewPoint.id)}
                  className={`w-full text-left p-4 rounded-lg border transition-colors ${
                    viewPoint.id === currentViewPointId
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div>
                    <h4 className="font-semibold text-gray-900">{viewPoint.name}</h4>
                    {viewPoint.description && (
                      <p className="text-sm text-gray-600 mt-1">{viewPoint.description}</p>
                    )}
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <MapPin size={12} className="mr-1" />
                      <span>
                        Position: ({viewPoint.position.x}, {viewPoint.position.y}, {viewPoint.position.z})
                      </span>
                    </div>
                  </div>
                </button>
              ))}
            </div>
            <button
              onClick={() => setShowViewPoints(false)}
              className="w-full mt-4 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Info panel */}
      {showInfo && (
        <div className="absolute top-20 right-4 bg-white rounded-xl p-4 max-w-sm shadow-lg z-40">
          <h4 className="font-semibold text-gray-900 mb-2">Tour Information</h4>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>Property:</strong> {house.name}</p>
            <p><strong>Address:</strong> {house.address}</p>
            <p><strong>Current Room:</strong> {currentRoom.name}</p>
            <p><strong>Viewpoint:</strong> {currentViewPoint.name}</p>
            <p><strong>Total Rooms:</strong> {house.rooms.length}</p>
            <p><strong>Total Viewpoints:</strong> {house.rooms.reduce((sum, room) => sum + room.viewPoints.length, 0)}</p>
          </div>
          
          <div className="mt-4 pt-3 border-t border-gray-200">
            <h5 className="font-medium text-gray-900 mb-2">Navigation Tips:</h5>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Drag to look around</li>
              <li>• Scroll to zoom in/out</li>
              <li>• Click hotspots to navigate</li>
              <li>• Use room/viewpoint buttons</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
