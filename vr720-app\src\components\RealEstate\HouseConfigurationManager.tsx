'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Trash2, Upload, Eye, Save, Home, ArrowLeft } from 'lucide-react';
import { House, Room, Hotspot } from './HouseStructureManager';
import { useHouseConfiguration } from '@/hooks/useDatabase';
import { roomTemplates, houseTemplates, generateDefaultLayout } from './HouseTemplates';

interface HouseConfigurationManagerProps {
  onBack: () => void;
  onStartTour: (house: House, startingRoomId?: string) => void;
}

// Use imported template data

export default function HouseConfigurationManager({ onBack, onStartTour }: HouseConfigurationManagerProps) {
  const {
    images: uploadedImages,
    loading,
    error,
    saveHouseConfiguration,
    fetchUserPanoramicImages
  } = useHouseConfiguration();

  const [currentHouse, setCurrentHouse] = useState<any>({
    name: 'My Property',
    address: 'Enter address',
    bedrooms: 0,
    bathrooms: 0,
    totalArea: 0,
    price: 0,
    currency: 'USD',
    propertyType: 'single-family',
    status: 'draft',
    features: [],
    isPublic: false,
    viewCount: 0,
    metadata: {},
    rooms: []
  });

  const [showImageSelector, setShowImageSelector] = useState(false);
  const [currentEditingRoom, setCurrentEditingRoom] = useState<string>('');
  const [saving, setSaving] = useState(false);

  // Load user's panoramic images
  useEffect(() => {
    fetchUserPanoramicImages();
  }, [fetchUserPanoramicImages]);

  // 从模板创建房屋
  const createFromTemplate = useCallback((template: typeof houseTemplates[0]) => {
    const newHouse = generateDefaultLayout(template);
    setCurrentHouse(newHouse);
  }, []);

  // 添加房间
  const addRoom = useCallback((templateId: string) => {
    const template = roomTemplates.find(t => t.id === templateId);
    if (!template) return;

    const newRoom: Room = {
      id: `room_${Date.now()}`,
      name: template.name,
      type: template.type,
      position: { 
        x: 100 + currentHouse.rooms.length * 50, 
        y: 100 + currentHouse.rooms.length * 30 
      },
      connections: [],
      hotspots: [],
      metadata: {
        description: template.description
      }
    };

    setCurrentHouse(prev => ({
      ...prev,
      rooms: [...prev.rooms, newRoom]
    }));
  }, [currentHouse.rooms.length]);

  // 删除房间
  const deleteRoom = useCallback((roomId: string) => {
    if (window.confirm('确定要删除这个房间吗？')) {
      setCurrentHouse(prev => ({
        ...prev,
        rooms: prev.rooms.filter(room => room.id !== roomId)
      }));
    }
  }, []);

  // 为房间分配全景图
  const assignPanoramaToRoom = useCallback((roomId: string, imageUrl: string) => {
    setCurrentHouse(prev => ({
      ...prev,
      rooms: prev.rooms.map(room => 
        room.id === roomId 
          ? { ...room, panoramaUrl: imageUrl, thumbnail: imageUrl }
          : room
      )
    }));
    setShowImageSelector(false);
  }, []);

  // 保存房屋配置
  const saveConfiguration = useCallback(async () => {
    setSaving(true);
    try {
      const savedHouse = await saveHouseConfiguration(currentHouse);
      if (savedHouse) {
        setCurrentHouse(savedHouse);
        alert('房屋配置已保存！');
      } else {
        alert('保存失败，请重试');
      }
    } catch (err) {
      console.error('Save error:', err);
      alert('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  }, [currentHouse, saveHouseConfiguration]);

  // 开始房屋漫游
  const startHouseTour = useCallback((startingRoomId?: string) => {
    const roomsWithPanoramas = currentHouse.rooms.filter((room: any) => room.panoramaImageId);

    if (roomsWithPanoramas.length === 0) {
      alert('请先为至少一个房间分配全景图片');
      return;
    }

    onStartTour(currentHouse, startingRoomId ?? roomsWithPanoramas[0].id);
  }, [currentHouse, onStartTour]);

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm p-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <button
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            返回
          </button>
          
          <h1 className="text-2xl font-bold text-gray-800">Property Configuration Manager</h1>

          <div className="flex space-x-3">
            <button
              onClick={() => startHouseTour()}
              disabled={currentHouse.rooms.filter(r => r.panoramaUrl).length === 0}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Eye size={16} className="inline mr-1" />
              Start Tour
            </button>

            <button
              onClick={saveConfiguration}
              disabled={saving}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Save size={16} className="inline mr-1" />
              {saving ? 'Saving...' : 'Save Configuration'}
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-6">
        {/* Property basic information */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Property Basic Information</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Property Name</label>
              <input
                type="text"
                value={currentHouse.name}
                onChange={(e) => setCurrentHouse(prev => ({ ...prev, name: e.target.value }))}
                className="w-full p-3 border border-gray-300 rounded-lg"
                placeholder="e.g., Luxury Villa"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
              <input
                type="text"
                value={currentHouse.address}
                onChange={(e) => setCurrentHouse(prev => ({ ...prev, address: e.target.value }))}
                className="w-full p-3 border border-gray-300 rounded-lg"
                placeholder="e.g., 123 Main St, Beverly Hills, CA"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Total Area (sq ft)</label>
              <input
                type="number"
                value={currentHouse.metadata.totalArea || ''}
                onChange={(e) => setCurrentHouse(prev => ({
                  ...prev,
                  metadata: { ...prev.metadata, totalArea: Number(e.target.value) }
                }))}
                className="w-full p-3 border border-gray-300 rounded-lg"
                placeholder="e.g., 3000"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price (USD)</label>
              <input
                type="number"
                value={currentHouse.metadata.price || ''}
                onChange={(e) => setCurrentHouse(prev => ({
                  ...prev,
                  metadata: { ...prev.metadata, price: Number(e.target.value) }
                }))}
                className="w-full p-3 border border-gray-300 rounded-lg"
                placeholder="e.g., 1500000"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Property Description</label>
            <textarea
              value={currentHouse.metadata.description || ''}
              onChange={(e) => setCurrentHouse(prev => ({
                ...prev,
                metadata: { ...prev.metadata, description: e.target.value }
              }))}
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-lg"
              placeholder="Describe the property's features and highlights..."
            />
          </div>
        </div>

        {/* Quick templates */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Quick Setup</h2>
          <p className="text-gray-600 mb-4">Choose a property template to quickly start configuration</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {houseTemplates.map((template) => (
              <button
                key={template.id}
                onClick={() => createFromTemplate(template)}
                className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all text-left"
              >
                <h3 className="font-semibold text-gray-800 mb-2">{template.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                <p className="text-xs text-blue-600">{template.rooms.length} rooms</p>
              </button>
            ))}
          </div>
        </div>

        {/* Room management */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-800">
              Room Configuration ({currentHouse.rooms.length})
            </h2>

            <div className="flex space-x-2">
              <select
                onChange={(e) => e.target.value && addRoom(e.target.value)}
                value=""
                className="px-3 py-2 border border-gray-300 rounded-lg"
              >
                <option value="">Add Room...</option>
                {roomTemplates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.icon} {template.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {currentHouse.rooms.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <Home size={48} className="mx-auto mb-4 text-gray-400" />
              <p className="text-lg mb-2">No rooms added yet</p>
              <p className="text-sm">Choose a property template above or manually add rooms to start configuration</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {currentHouse.rooms.map((room) => (
                <div
                  key={room.id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-gray-800">{room.name}</h3>
                    <div className="flex space-x-1">
                      <button
                        onClick={() => {
                          setCurrentEditingRoom(room.id);
                          setShowImageSelector(true);
                        }}
                        className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                        title="Assign Panorama"
                      >
                        <Upload size={16} />
                      </button>
                      
                      {room.panoramaUrl && (
                        <button
                          onClick={() => startHouseTour(room.id)}
                          className="p-1 text-green-600 hover:bg-green-100 rounded"
                          title="Start tour from this room"
                        >
                          <Eye size={16} />
                        </button>
                      )}
                      
                      <button
                        onClick={() => deleteRoom(room.id)}
                        className="p-1 text-red-600 hover:bg-red-100 rounded"
                        title="Delete room"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                  
                  {room.panoramaUrl ? (
                    <div className="relative mb-3">
                      <img
                        src={room.panoramaUrl}
                        alt={room.name}
                        className="w-full h-24 object-cover rounded"
                      />
                      <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                        Configured
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-100 h-24 rounded flex items-center justify-center mb-3">
                      <div className="text-center text-gray-500">
                        <Upload size={20} className="mx-auto mb-1" />
                        <p className="text-xs">No panorama assigned</p>
                      </div>
                    </div>
                  )}

                  <div className="text-sm text-gray-600">
                    <p className="mb-1">Type: {room.type}</p>
                    {room.metadata?.description && (
                      <p className="text-xs">{room.metadata.description}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Image selector modal */}
      {showImageSelector && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden">
            <div className="bg-blue-600 text-white p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Select Panoramic Image</h3>
                <button
                  onClick={() => setShowImageSelector(false)}
                  className="text-white hover:text-gray-200"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {uploadedImages.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500 mb-4">No images uploaded yet</p>
                  <button
                    onClick={() => {
                      setShowImageSelector(false);
                      // Navigate to upload page
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg"
                  >
                    Upload Images
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {uploadedImages.map((image) => (
                    <button
                      key={image.id}
                      onClick={() => assignPanoramaToRoom(currentEditingRoom, image.url)}
                      className="group relative overflow-hidden rounded-lg border-2 border-gray-200 hover:border-blue-400 transition-colors"
                    >
                      <img
                        src={image.url}
                        alt={image.name}
                        className="w-full h-24 object-cover group-hover:scale-105 transition-transform"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity text-white text-sm font-medium">
                          Select
                        </div>
                      </div>
                      <div className="p-2">
                        <p className="text-xs text-gray-600 truncate">{image.name}</p>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
