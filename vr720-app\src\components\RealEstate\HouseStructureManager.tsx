'use client';

import React, { useState, useCallback } from 'react';
import { Plus, Edit, Trash2, Home, Eye, Map, Save, Upload } from 'lucide-react';

export interface Room {
  id: string;
  name: string;
  type: string;
  panoramaUrl?: string;
  thumbnail?: string;
  position: { x: number; y: number };
  connections: string[]; // IDs of connected rooms
  hotspots: Hotspot[];
  metadata: {
    area?: number;
    description?: string;
    features?: string[];
  };
}

export interface Hotspot {
  id: string;
  position: { x: number; y: number; z: number }; // 3D position in panorama
  targetRoomId: string;
  label: string;
  type: 'door' | 'window' | 'stairs' | 'custom';
}

export interface House {
  id: string;
  name: string;
  address: string;
  rooms: Room[];
  floorPlan?: string;
  metadata: {
    totalArea?: number;
    bedrooms?: number;
    bathrooms?: number;
    price?: number;
    description?: string;
  };
}

interface HouseStructureManagerProps {
  house: House;
  onHouseUpdate: (house: House) => void;
  onStartTour: (startingRoomId: string) => void;
}

export default function HouseStructureManager({ 
  house, 
  onHouseUpdate, 
  onStartTour 
}: HouseStructureManagerProps) {
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [showFloorPlan, setShowFloorPlan] = useState(true);

  // 添加新房间
  const addRoom = useCallback(() => {
    const newRoom: Room = {
      id: `room_${Date.now()}`,
      name: `房间 ${house.rooms.length + 1}`,
      type: 'living-room',
      position: { x: 100 + house.rooms.length * 50, y: 100 + house.rooms.length * 30 },
      connections: [],
      hotspots: [],
      metadata: {}
    };

    const updatedHouse = {
      ...house,
      rooms: [...house.rooms, newRoom]
    };

    onHouseUpdate(updatedHouse);
    setSelectedRoom(newRoom);
    setIsEditing(true);
  }, [house, onHouseUpdate]);

  // 更新房间信息
  const updateRoom = useCallback((roomId: string, updates: Partial<Room>) => {
    const updatedRooms = house.rooms.map(room =>
      room.id === roomId ? { ...room, ...updates } : room
    );

    const updatedHouse = {
      ...house,
      rooms: updatedRooms
    };

    onHouseUpdate(updatedHouse);
    
    if (selectedRoom?.id === roomId) {
      setSelectedRoom({ ...selectedRoom, ...updates });
    }
  }, [house, onHouseUpdate, selectedRoom]);

  // 删除房间
  const deleteRoom = useCallback((roomId: string) => {
    if (window.confirm('确定要删除这个房间吗？')) {
      const updatedRooms = house.rooms.filter(room => room.id !== roomId);
      
      // 移除其他房间中对此房间的连接
      const cleanedRooms = updatedRooms.map(room => ({
        ...room,
        connections: room.connections.filter(id => id !== roomId),
        hotspots: room.hotspots.filter(hotspot => hotspot.targetRoomId !== roomId)
      }));

      const updatedHouse = {
        ...house,
        rooms: cleanedRooms
      };

      onHouseUpdate(updatedHouse);
      
      if (selectedRoom?.id === roomId) {
        setSelectedRoom(null);
      }
    }
  }, [house, onHouseUpdate, selectedRoom]);

  // 连接两个房间
  const connectRooms = useCallback((roomId1: string, roomId2: string) => {
    const updatedRooms = house.rooms.map(room => {
      if (room.id === roomId1 && !room.connections.includes(roomId2)) {
        return { ...room, connections: [...room.connections, roomId2] };
      }
      if (room.id === roomId2 && !room.connections.includes(roomId1)) {
        return { ...room, connections: [...room.connections, roomId1] };
      }
      return room;
    });

    const updatedHouse = {
      ...house,
      rooms: updatedRooms
    };

    onHouseUpdate(updatedHouse);
  }, [house, onHouseUpdate]);

  // 添加热点
  const addHotspot = useCallback((roomId: string, hotspot: Omit<Hotspot, 'id'>) => {
    const newHotspot: Hotspot = {
      ...hotspot,
      id: `hotspot_${Date.now()}`
    };

    updateRoom(roomId, {
      hotspots: [...(house.rooms.find(r => r.id === roomId)?.hotspots || []), newHotspot]
    });
  }, [house.rooms, updateRoom]);

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 标题栏 */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                🏠 {house.name}
              </h1>
              <p className="text-gray-600">{house.address}</p>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                <span>📐 {house.metadata.totalArea || 0} 平方米</span>
                <span>🛏️ {house.metadata.bedrooms || 0} 卧室</span>
                <span>🚿 {house.metadata.bathrooms || 0} 浴室</span>
                {house.metadata.price && (
                  <span>💰 ${house.metadata.price.toLocaleString()}</span>
                )}
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowFloorPlan(!showFloorPlan)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  showFloorPlan 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <Map size={20} className="inline mr-2" />
                平面图
              </button>
              
              <button
                onClick={addRoom}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Plus size={20} className="inline mr-2" />
                添加房间
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 房间列表 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">
                房间列表 ({house.rooms.length})
              </h2>
              
              <div className="space-y-3">
                {house.rooms.map((room) => (
                  <div
                    key={room.id}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedRoom?.id === room.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedRoom(room)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Home size={20} className="text-gray-500 mr-3" />
                        <div>
                          <h3 className="font-medium text-gray-800">{room.name}</h3>
                          <p className="text-sm text-gray-500">{room.type}</p>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        {room.panoramaUrl && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onStartTour(room.id);
                            }}
                            className="p-2 text-green-600 hover:bg-green-100 rounded-full transition-colors"
                            title="开始漫游"
                          >
                            <Eye size={16} />
                          </button>
                        )}
                        
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedRoom(room);
                            setIsEditing(true);
                          }}
                          className="p-2 text-blue-600 hover:bg-blue-100 rounded-full transition-colors"
                          title="编辑"
                        >
                          <Edit size={16} />
                        </button>
                        
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteRoom(room.id);
                          }}
                          className="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors"
                          title="删除"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                    
                    {room.panoramaUrl && (
                      <div className="mt-3">
                        <img
                          src={room.thumbnail || room.panoramaUrl}
                          alt={room.name}
                          className="w-full h-20 object-cover rounded"
                        />
                      </div>
                    )}
                    
                    <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                      <span>连接: {room.connections.length} 个房间</span>
                      <span>热点: {room.hotspots.length} 个</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className="lg:col-span-2">
            {showFloorPlan ? (
              /* 平面图视图 */
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">房屋平面图</h2>
                
                <div className="relative bg-gray-50 rounded-lg h-96 overflow-hidden">
                  {/* 简化的平面图 */}
                  <svg className="w-full h-full" viewBox="0 0 800 600">
                    {/* 房间 */}
                    {house.rooms.map((room) => (
                      <g key={room.id}>
                        <rect
                          x={room.position.x}
                          y={room.position.y}
                          width="120"
                          height="80"
                          fill={selectedRoom?.id === room.id ? "#3B82F6" : "#E5E7EB"}
                          stroke="#6B7280"
                          strokeWidth="2"
                          className="cursor-pointer hover:fill-blue-200 transition-colors"
                          onClick={() => setSelectedRoom(room)}
                        />
                        <text
                          x={room.position.x + 60}
                          y={room.position.y + 45}
                          textAnchor="middle"
                          className="text-sm font-medium fill-gray-800 pointer-events-none"
                        >
                          {room.name}
                        </text>
                        
                        {/* 连接线 */}
                        {room.connections.map((connectionId) => {
                          const connectedRoom = house.rooms.find(r => r.id === connectionId);
                          if (!connectedRoom) return null;
                          
                          return (
                            <line
                              key={`${room.id}-${connectionId}`}
                              x1={room.position.x + 60}
                              y1={room.position.y + 40}
                              x2={connectedRoom.position.x + 60}
                              y2={connectedRoom.position.y + 40}
                              stroke="#10B981"
                              strokeWidth="3"
                              strokeDasharray="5,5"
                            />
                          );
                        })}
                      </g>
                    ))}
                  </svg>
                  
                  {/* 图例 */}
                  <div className="absolute top-4 right-4 bg-white p-3 rounded-lg shadow-md">
                    <div className="text-sm space-y-2">
                      <div className="flex items-center">
                        <div className="w-4 h-4 bg-gray-300 border border-gray-400 mr-2"></div>
                        <span>房间</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-4 h-1 bg-green-500 mr-2"></div>
                        <span>连接</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              /* 房间详情视图 */
              selectedRoom && (
                <div className="bg-white rounded-lg shadow-lg p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-gray-800">
                      房间详情: {selectedRoom.name}
                    </h2>
                    <button
                      onClick={() => setIsEditing(!isEditing)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <Edit size={20} className="inline mr-2" />
                      {isEditing ? '保存' : '编辑'}
                    </button>
                  </div>

                  {isEditing ? (
                    /* 编辑模式 */
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            房间名称
                          </label>
                          <input
                            type="text"
                            value={selectedRoom.name}
                            onChange={(e) => updateRoom(selectedRoom.id, { name: e.target.value })}
                            className="w-full p-3 border border-gray-300 rounded-lg"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            房间类型
                          </label>
                          <select
                            value={selectedRoom.type}
                            onChange={(e) => updateRoom(selectedRoom.id, { type: e.target.value })}
                            className="w-full p-3 border border-gray-300 rounded-lg"
                          >
                            <option value="living-room">客厅</option>
                            <option value="bedroom">卧室</option>
                            <option value="kitchen">厨房</option>
                            <option value="bathroom">浴室</option>
                            <option value="outdoor">户外</option>
                            <option value="garage">车库</option>
                          </select>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          描述
                        </label>
                        <textarea
                          value={selectedRoom.metadata.description || ''}
                          onChange={(e) => updateRoom(selectedRoom.id, {
                            metadata: { ...selectedRoom.metadata, description: e.target.value }
                          })}
                          rows={3}
                          className="w-full p-3 border border-gray-300 rounded-lg"
                          placeholder="描述这个房间的特点..."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          面积 (平方米)
                        </label>
                        <input
                          type="number"
                          value={selectedRoom.metadata.area || ''}
                          onChange={(e) => updateRoom(selectedRoom.id, {
                            metadata: { ...selectedRoom.metadata, area: Number(e.target.value) }
                          })}
                          className="w-full p-3 border border-gray-300 rounded-lg"
                        />
                      </div>
                    </div>
                  ) : (
                    /* 查看模式 */
                    <div className="space-y-6">
                      {selectedRoom.panoramaUrl && (
                        <div>
                          <h3 className="text-lg font-medium text-gray-800 mb-3">全景预览</h3>
                          <div className="relative">
                            <img
                              src={selectedRoom.thumbnail || selectedRoom.panoramaUrl}
                              alt={selectedRoom.name}
                              className="w-full h-48 object-cover rounded-lg"
                            />
                            <button
                              onClick={() => onStartTour(selectedRoom.id)}
                              className="absolute inset-0 bg-black/50 text-white flex items-center justify-center rounded-lg opacity-0 hover:opacity-100 transition-opacity"
                            >
                              <Eye size={32} className="mr-2" />
                              开始VR漫游
                            </button>
                          </div>
                        </div>
                      )}

                      <div>
                        <h3 className="text-lg font-medium text-gray-800 mb-3">房间信息</h3>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">类型:</span>
                            <span className="ml-2 font-medium">{selectedRoom.type}</span>
                          </div>
                          {selectedRoom.metadata.area && (
                            <div>
                              <span className="text-gray-500">面积:</span>
                              <span className="ml-2 font-medium">{selectedRoom.metadata.area} m²</span>
                            </div>
                          )}
                          <div>
                            <span className="text-gray-500">连接房间:</span>
                            <span className="ml-2 font-medium">{selectedRoom.connections.length}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">导航热点:</span>
                            <span className="ml-2 font-medium">{selectedRoom.hotspots.length}</span>
                          </div>
                        </div>
                        
                        {selectedRoom.metadata.description && (
                          <div className="mt-4">
                            <span className="text-gray-500">描述:</span>
                            <p className="mt-1 text-gray-800">{selectedRoom.metadata.description}</p>
                          </div>
                        )}
                      </div>

                      {selectedRoom.connections.length > 0 && (
                        <div>
                          <h3 className="text-lg font-medium text-gray-800 mb-3">连接的房间</h3>
                          <div className="grid grid-cols-2 gap-3">
                            {selectedRoom.connections.map((connectionId) => {
                              const connectedRoom = house.rooms.find(r => r.id === connectionId);
                              if (!connectedRoom) return null;
                              
                              return (
                                <div
                                  key={connectionId}
                                  className="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                                  onClick={() => setSelectedRoom(connectedRoom)}
                                >
                                  <div className="flex items-center">
                                    <Home size={16} className="text-gray-500 mr-2" />
                                    <span className="font-medium">{connectedRoom.name}</span>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
