// House template data

export interface RoomTemplate {
  id: string;
  name: string;
  type: string;
  icon: string;
  description: string;
  suggestedConnections: string[];
  defaultPosition?: { x: number; y: number };
  metadata?: {
    area?: number;
    features?: string[];
    tips?: string[];
  };
}

export interface HouseTemplate {
  id: string;
  name: string;
  description: string;
  type: 'single-family' | 'condo' | 'townhouse' | 'mansion' | 'studio';
  bedrooms: number;
  bathrooms: number;
  totalArea: number;
  priceRange: { min: number; max: number };
  rooms: string[];
  layout: 'open' | 'traditional' | 'modern' | 'luxury';
  features: string[];
}

// Room template library
export const roomTemplates: RoomTemplate[] = [
  {
    id: 'living-room',
    name: 'Living Room',
    type: 'living-room',
    icon: '🛋️',
    description: 'Main space for family gatherings and relaxation',
    suggestedConnections: ['kitchen', 'dining-room', 'hallway', 'entrance'],
    defaultPosition: { x: 200, y: 200 },
    metadata: {
      area: 25,
      features: ['Sofa', 'TV', 'Coffee table', 'Floor-to-ceiling windows'],
      tips: ['Ensure adequate natural light', 'Avoid backlighting when shooting', 'Show complete furniture layout']
    }
  },
  {
    id: 'kitchen',
    name: 'Kitchen',
    type: 'kitchen',
    icon: '🍳',
    description: 'Cooking and meal preparation area',
    suggestedConnections: ['living-room', 'dining-room', 'pantry'],
    defaultPosition: { x: 350, y: 200 },
    metadata: {
      area: 15,
      features: ['Cabinets', 'Island', 'Appliances', 'Sink'],
      tips: ['Show modern kitchen equipment', 'Highlight storage space', 'Pay attention to lighting effects']
    }
  },
  {
    id: 'master-bedroom',
    name: 'Master Bedroom',
    type: 'bedroom',
    icon: '🛏️',
    description: 'Primary sleeping and resting space',
    suggestedConnections: ['master-bathroom', 'walk-in-closet', 'hallway'],
    defaultPosition: { x: 200, y: 50 },
    metadata: {
      area: 20,
      features: ['King bed', 'Wardrobe', 'Vanity', 'Balcony'],
      tips: ['Show bedding texture', 'Highlight privacy and comfort', 'Display storage space']
    }
  },
  {
    id: 'master-bathroom',
    name: 'Master Bathroom',
    type: 'bathroom',
    icon: '🚿',
    description: 'Private bathroom for master bedroom',
    suggestedConnections: ['master-bedroom'],
    defaultPosition: { x: 350, y: 50 },
    metadata: {
      area: 8,
      features: ['Shower', 'Bathtub', 'Double vanity', 'Mirror'],
      tips: ['Show luxury bathroom facilities', 'Pay attention to tile and finish details', 'Ensure adequate lighting']
    }
  },
  {
    id: 'guest-bedroom',
    name: 'Guest Bedroom',
    type: 'bedroom',
    icon: '🛌',
    description: 'Guest accommodation room',
    suggestedConnections: ['hallway', 'guest-bathroom'],
    defaultPosition: { x: 50, y: 50 },
    metadata: {
      area: 15,
      features: ['Single/Double bed', 'Wardrobe', 'Desk', 'Window'],
      tips: ['Show room versatility', 'Highlight comfortable accommodation environment']
    }
  },
  {
    id: 'guest-bathroom',
    name: 'Guest Bathroom',
    type: 'bathroom',
    icon: '🚽',
    description: 'Bathroom for guest use',
    suggestedConnections: ['hallway', 'guest-bedroom'],
    defaultPosition: { x: 50, y: 150 },
    metadata: {
      area: 5,
      features: ['Shower', 'Vanity', 'Mirror', 'Storage cabinet'],
      tips: ['Show practicality and cleanliness', 'Highlight space efficiency']
    }
  },
  {
    id: 'dining-room',
    name: 'Dining Room',
    type: 'dining-room',
    icon: '🍽️',
    description: 'Formal dining space',
    suggestedConnections: ['kitchen', 'living-room'],
    defaultPosition: { x: 350, y: 300 },
    metadata: {
      area: 12,
      features: ['Dining table', 'Dining chairs', 'China cabinet', 'Chandelier'],
      tips: ['Show dining atmosphere', 'Highlight formal space feel', 'Pay attention to tableware and decor']
    }
  },
  {
    id: 'office',
    name: 'Home Office',
    type: 'office',
    icon: '💼',
    description: 'Work and study space',
    suggestedConnections: ['hallway', 'living-room'],
    defaultPosition: { x: 50, y: 300 },
    metadata: {
      area: 10,
      features: ['Desk', 'Bookshelf', 'Office chair', 'Computer'],
      tips: ['Show professional work environment', 'Highlight books and office equipment', 'Pay attention to lighting']
    }
  },
  {
    id: 'hallway',
    name: 'Hallway',
    type: 'hallway',
    icon: '🚪',
    description: 'Passage connecting various rooms',
    suggestedConnections: ['living-room', 'bedrooms', 'bathrooms'],
    defaultPosition: { x: 200, y: 125 },
    metadata: {
      area: 8,
      features: ['Corridor', 'Storage cabinet', 'Artwork', 'Lighting'],
      tips: ['Show house flow design', 'Highlight space connectivity']
    }
  },
  {
    id: 'garage',
    name: 'Garage',
    type: 'garage',
    icon: '🚗',
    description: 'Parking and storage space',
    suggestedConnections: ['hallway', 'kitchen', 'entrance'],
    defaultPosition: { x: 500, y: 200 },
    metadata: {
      area: 30,
      features: ['Parking space', 'Storage shelves', 'Workbench', 'Automatic door'],
      tips: ['Show parking convenience', 'Highlight storage function', 'Display garage door and facilities']
    }
  },
  {
    id: 'backyard',
    name: 'Backyard',
    type: 'outdoor',
    icon: '🌳',
    description: 'Outdoor leisure and entertainment space',
    suggestedConnections: ['kitchen', 'living-room', 'patio'],
    defaultPosition: { x: 200, y: 400 },
    metadata: {
      area: 50,
      features: ['Lawn', 'Garden', 'Outdoor furniture', 'BBQ area'],
      tips: ['Show outdoor living charm', 'Highlight landscape design', 'Pay attention to weather and lighting']
    }
  },
  {
    id: 'entrance',
    name: 'Entrance/Foyer',
    type: 'entrance',
    icon: '🚪',
    description: 'Main entrance of the house',
    suggestedConnections: ['living-room', 'hallway'],
    defaultPosition: { x: 200, y: 350 },
    metadata: {
      area: 6,
      features: ['Front door', 'Shoe cabinet', 'Coat rack', 'Decor'],
      tips: ['Show first impression', 'Highlight welcoming feel', 'Pay attention to foyer cleanliness']
    }
  },
  {
    id: 'walk-in-closet',
    name: 'Walk-in Closet',
    type: 'closet',
    icon: '👗',
    description: 'Large clothing storage space',
    suggestedConnections: ['master-bedroom'],
    defaultPosition: { x: 300, y: 100 },
    metadata: {
      area: 8,
      features: ['Hangers', 'Drawers', 'Shoe rack', 'Mirror'],
      tips: ['Show luxury storage feel', 'Highlight space organization', 'Display clothing presentation effect']
    }
  },
  {
    id: 'pantry',
    name: 'Pantry',
    type: 'storage',
    icon: '📦',
    description: 'Food and supplies storage space',
    suggestedConnections: ['kitchen'],
    defaultPosition: { x: 400, y: 150 },
    metadata: {
      area: 4,
      features: ['Storage shelves', 'Refrigeration equipment', 'Organization system'],
      tips: ['Show storage practicality', 'Highlight space efficiency']
    }
  },
  {
    id: 'laundry-room',
    name: 'Laundry Room',
    type: 'utility',
    icon: '🧺',
    description: 'Laundry and cleaning supplies storage',
    suggestedConnections: ['hallway', 'garage'],
    defaultPosition: { x: 450, y: 100 },
    metadata: {
      area: 6,
      features: ['Washer', 'Dryer', 'Utility sink', 'Storage cabinet'],
      tips: ['Show household convenience', 'Highlight modern equipment']
    }
  },
  {
    id: 'patio',
    name: 'Patio/Balcony',
    type: 'outdoor',
    icon: '🌅',
    description: 'Outdoor leisure platform',
    suggestedConnections: ['living-room', 'master-bedroom', 'backyard'],
    defaultPosition: { x: 100, y: 400 },
    metadata: {
      area: 15,
      features: ['Outdoor furniture', 'Shade facilities', 'Railing', 'Landscape'],
      tips: ['Show outdoor leisure experience', 'Highlight scenic views', 'Pay attention to safety facilities']
    }
  }
];

// House template library
export const houseTemplates: HouseTemplate[] = [
  {
    id: 'luxury-mansion',
    name: 'Luxury Villa',
    description: 'High-end luxury residence with complete amenities',
    type: 'mansion',
    bedrooms: 4,
    bathrooms: 3,
    totalArea: 400,
    priceRange: { min: 2000000, max: 5000000 },
    layout: 'luxury',
    rooms: [
      'entrance', 'living-room', 'kitchen', 'dining-room',
      'master-bedroom', 'master-bathroom', 'walk-in-closet',
      'guest-bedroom', 'guest-bedroom', 'guest-bathroom',
      'office', 'hallway', 'garage', 'backyard', 'patio',
      'laundry-room', 'pantry'
    ],
    features: [
      'Luxury finishes', 'Smart home', 'Private garden', 'Two-car garage',
      'Walk-in closet', 'Private office', 'Outdoor entertainment area'
    ]
  },
  {
    id: 'family-home',
    name: 'Family Home',
    description: 'Standard residence suitable for family living',
    type: 'single-family',
    bedrooms: 3,
    bathrooms: 2,
    totalArea: 250,
    priceRange: { min: 800000, max: 1500000 },
    layout: 'traditional',
    rooms: [
      'entrance', 'living-room', 'kitchen', 'dining-room',
      'master-bedroom', 'master-bathroom', 'guest-bedroom',
      'guest-bedroom', 'guest-bathroom', 'hallway',
      'garage', 'backyard'
    ],
    features: [
      'Open kitchen', 'Family activity area', 'Private backyard',
      'Ample storage space', 'Modern amenities'
    ]
  },
  {
    id: 'modern-condo',
    name: 'Modern Condo',
    description: 'Modern apartment in city center',
    type: 'condo',
    bedrooms: 2,
    bathrooms: 2,
    totalArea: 120,
    priceRange: { min: 600000, max: 1200000 },
    layout: 'modern',
    rooms: [
      'entrance', 'living-room', 'kitchen', 'master-bedroom',
      'master-bathroom', 'guest-bedroom', 'guest-bathroom',
      'hallway', 'patio'
    ],
    features: [
      'Open design', 'Floor-to-ceiling windows', 'Modern kitchen',
      'City views', 'Convenient transportation'
    ]
  },
  {
    id: 'townhouse',
    name: 'Townhouse',
    description: 'Multi-level townhouse design',
    type: 'townhouse',
    bedrooms: 3,
    bathrooms: 2,
    totalArea: 180,
    priceRange: { min: 700000, max: 1300000 },
    layout: 'traditional',
    rooms: [
      'entrance', 'living-room', 'kitchen', 'dining-room',
      'master-bedroom', 'master-bathroom', 'guest-bedroom',
      'guest-bathroom', 'office', 'hallway', 'garage', 'patio'
    ],
    features: [
      'Multi-level design', 'Private garage', 'Small garden',
      'Community environment', 'Modern finishes'
    ]
  },
  {
    id: 'studio-loft',
    name: 'Studio Apartment',
    description: 'Open-plan work and living space',
    type: 'studio',
    bedrooms: 1,
    bathrooms: 1,
    totalArea: 60,
    priceRange: { min: 400000, max: 800000 },
    layout: 'open',
    rooms: [
      'entrance', 'living-room', 'kitchen', 'master-bedroom',
      'master-bathroom'
    ],
    features: [
      'Open design', 'High ceilings', 'Industrial style',
      'Multi-functional space', 'Urban convenience'
    ]
  }
];

// Get recommended room templates based on house type
export const getRoomTemplatesForHouseType = (houseType: string): RoomTemplate[] => {
  const template = houseTemplates.find(t => t.id === houseType);
  if (!template) return roomTemplates;

  return roomTemplates.filter(room => template.rooms.includes(room.id));
};

// Generate default layout for house
export const generateDefaultLayout = (houseTemplate: HouseTemplate): any => {
  const rooms = houseTemplate.rooms.map((roomId, index) => {
    const roomTemplate = roomTemplates.find(r => r.id === roomId);
    if (!roomTemplate) return null;

    // Adjust layout based on house type
    let position = roomTemplate.defaultPosition || { x: 100, y: 100 };

    // Add offset for duplicate room types
    const sameTypeRooms = houseTemplate.rooms.filter(r => r === roomId);
    if (sameTypeRooms.length > 1) {
      const typeIndex = houseTemplate.rooms.slice(0, index).filter(r => r === roomId).length;
      position = {
        x: position.x + typeIndex * 100,
        y: position.y + typeIndex * 50
      };
    }

    return {
      id: `${roomId}_${Date.now()}_${index}`,
      name: roomTemplate.name,
      type: roomTemplate.type,
      position,
      connections: [],
      hotspots: [],
      metadata: {
        description: roomTemplate.description,
        ...roomTemplate.metadata
      }
    };
  }).filter(Boolean);

  return {
    id: `house_${Date.now()}`,
    name: houseTemplate.name,
    address: 'Enter address',
    rooms,
    metadata: {
      totalArea: houseTemplate.totalArea,
      bedrooms: houseTemplate.bedrooms,
      bathrooms: houseTemplate.bathrooms,
      price: houseTemplate.priceRange.min,
      description: houseTemplate.description,
      features: houseTemplate.features
    }
  };
};
