'use client';

import React, { useState, useCallback } from 'react';
import { ArrowLeft, Home, Map, Info, Share, Heart, Calendar } from 'lucide-react';
import { House, Room } from './HouseStructureManager';
import AFrameViewer from '../VR/AFrameViewer';

interface HouseTourViewerProps {
  house: House;
  startingRoomId?: string;
  onBack: () => void;
  onRoomChange?: (roomId: string) => void;
}

export default function HouseTourViewer({ 
  house, 
  startingRoomId, 
  onBack, 
  onRoomChange 
}: HouseTourViewerProps) {
  const [currentRoomId, setCurrentRoomId] = useState(
    startingRoomId || house.rooms.find(r => r.panoramaUrl)?.[0]?.id || house.rooms[0]?.id
  );
  const [showMiniMap, setShowMiniMap] = useState(false);
  const [showRoomInfo, setShowRoomInfo] = useState(true);

  const currentRoom = house.rooms.find(r => r.id === currentRoomId);

  // 切换房间
  const navigateToRoom = useCallback((roomId: string) => {
    const targetRoom = house.rooms.find(r => r.id === roomId);
    if (targetRoom && targetRoom.panoramaUrl) {
      setCurrentRoomId(roomId);
      onRoomChange?.(roomId);
    }
  }, [house.rooms, onRoomChange]);

  // 生成热点数据 - 自动为连接的房间创建热点
  const generateHotspots = useCallback(() => {
    if (!currentRoom) return [];

    // 使用现有的热点
    const existingHotspots = currentRoom.hotspots.map(hotspot => ({
      id: hotspot.id,
      position: hotspot.position,
      label: hotspot.label,
      onClick: () => navigateToRoom(hotspot.targetRoomId)
    }));

    // 为连接的房间自动生成热点（如果没有现有热点）
    if (existingHotspots.length === 0 && currentRoom.connections.length > 0) {
      const autoHotspots = currentRoom.connections
        .map((connectionId, index) => {
          const connectedRoom = house.rooms.find(r => r.id === connectionId);
          if (!connectedRoom || !connectedRoom.panoramaUrl) return null;

          // 根据索引分布热点位置
          const angle = (index * 360) / currentRoom.connections.length;
          const x = Math.cos((angle * Math.PI) / 180) * 3;
          const z = Math.sin((angle * Math.PI) / 180) * 3;

          return {
            id: `auto-hotspot-${connectionId}`,
            position: { x, y: 0, z },
            label: `前往 ${connectedRoom.name}`,
            onClick: () => navigateToRoom(connectionId)
          };
        })
        .filter(Boolean);

      return autoHotspots as any[];
    }

    return existingHotspots;
  }, [currentRoom, house.rooms, navigateToRoom]);

  if (!currentRoom) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <p className="text-xl mb-4">房间不存在</p>
          <button
            onClick={onBack}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  if (!currentRoom.panoramaUrl) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-center">
          <p className="text-xl mb-4">此房间暂无全景图</p>
          <button
            onClick={onBack}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-screen bg-black overflow-hidden">
      {/* VR查看器 */}
      <AFrameViewer
        panoramaUrl={currentRoom.panoramaUrl}
        onBack={onBack}
        onReset={() => window.location.reload()}
        hotspots={generateHotspots()}
        roomInfo={{
          name: currentRoom.name,
          type: currentRoom.type,
          description: currentRoom.metadata.description
        }}
      />

      {/* 顶部控制栏 */}
      <div className="absolute top-0 left-0 right-0 z-50 bg-gradient-to-b from-black/70 to-transparent p-4">
        <div className="flex items-center justify-between">
          <button
            onClick={onBack}
            className="bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors backdrop-blur-sm"
          >
            <ArrowLeft size={24} />
          </button>
          
          <div className="text-white text-center">
            <h1 className="text-xl font-bold">{house.name}</h1>
            <p className="text-sm text-gray-300">{house.address}</p>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => setShowMiniMap(!showMiniMap)}
              className={`p-3 rounded-full transition-colors backdrop-blur-sm ${
                showMiniMap ? 'bg-blue-500 text-white' : 'bg-black/50 text-white hover:bg-black/70'
              }`}
              title="小地图"
            >
              <Map size={20} />
            </button>
            
            <button
              onClick={() => setShowRoomInfo(!showRoomInfo)}
              className={`p-3 rounded-full transition-colors backdrop-blur-sm ${
                showRoomInfo ? 'bg-blue-500 text-white' : 'bg-black/50 text-white hover:bg-black/70'
              }`}
              title="房间信息"
            >
              <Info size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* 房间信息面板 */}
      {showRoomInfo && (
        <div className="absolute top-20 left-4 z-40 bg-black/80 text-white p-4 rounded-lg backdrop-blur-sm max-w-sm">
          <div className="flex items-center mb-3">
            <Home size={20} className="mr-2 text-blue-400" />
            <h3 className="text-lg font-semibold">{currentRoom.name}</h3>
          </div>
          
          <div className="space-y-2 text-sm">
            <div>
              <span className="text-gray-400">类型:</span>
              <span className="ml-2">{currentRoom.type}</span>
            </div>
            
            {currentRoom.metadata.area && (
              <div>
                <span className="text-gray-400">面积:</span>
                <span className="ml-2">{currentRoom.metadata.area} m²</span>
              </div>
            )}
            
            {currentRoom.metadata.description && (
              <div>
                <span className="text-gray-400">描述:</span>
                <p className="mt-1 text-gray-200">{currentRoom.metadata.description}</p>
              </div>
            )}
            
            {currentRoom.connections.length > 0 && (
              <div>
                <span className="text-gray-400">可前往:</span>
                <div className="mt-1 space-y-1">
                  {currentRoom.connections.map(connectionId => {
                    const connectedRoom = house.rooms.find(r => r.id === connectionId);
                    if (!connectedRoom) return null;
                    
                    return (
                      <button
                        key={connectionId}
                        onClick={() => navigateToRoom(connectionId)}
                        className="block w-full text-left px-2 py-1 bg-blue-600/30 hover:bg-blue-600/50 rounded text-sm transition-colors"
                        disabled={!connectedRoom.panoramaUrl}
                      >
                        {connectedRoom.name}
                        {!connectedRoom.panoramaUrl && (
                          <span className="text-gray-400 ml-2">(无全景图)</span>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 小地图 */}
      {showMiniMap && (
        <div className="absolute top-20 right-4 z-40 bg-black/80 text-white p-4 rounded-lg backdrop-blur-sm">
          <h4 className="text-sm font-semibold mb-3 text-center">房屋平面图</h4>
          
          <div className="relative bg-gray-800 rounded w-64 h-48 overflow-hidden">
            <svg className="w-full h-full" viewBox="0 0 400 300">
              {/* 房间 */}
              {house.rooms.map((room) => (
                <g key={room.id}>
                  <rect
                    x={room.position.x * 0.5}
                    y={room.position.y * 0.5}
                    width="60"
                    height="40"
                    fill={room.id === currentRoomId ? "#3B82F6" : room.panoramaUrl ? "#10B981" : "#6B7280"}
                    stroke="#E5E7EB"
                    strokeWidth="1"
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => room.panoramaUrl && navigateToRoom(room.id)}
                  />
                  <text
                    x={room.position.x * 0.5 + 30}
                    y={room.position.y * 0.5 + 25}
                    textAnchor="middle"
                    className="text-xs fill-white pointer-events-none"
                  >
                    {room.name.length > 6 ? room.name.substring(0, 6) + '...' : room.name}
                  </text>
                  
                  {/* 连接线 */}
                  {room.connections.map((connectionId) => {
                    const connectedRoom = house.rooms.find(r => r.id === connectionId);
                    if (!connectedRoom) return null;
                    
                    return (
                      <line
                        key={`${room.id}-${connectionId}`}
                        x1={room.position.x * 0.5 + 30}
                        y1={room.position.y * 0.5 + 20}
                        x2={connectedRoom.position.x * 0.5 + 30}
                        y2={connectedRoom.position.y * 0.5 + 20}
                        stroke="#10B981"
                        strokeWidth="2"
                        strokeDasharray="3,3"
                      />
                    );
                  })}
                </g>
              ))}
            </svg>
            
            {/* 图例 */}
            <div className="absolute bottom-2 left-2 text-xs space-y-1">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 mr-2"></div>
                <span>当前</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 mr-2"></div>
                <span>可访问</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-gray-500 mr-2"></div>
                <span>无全景</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 底部操作栏 */}
      <div className="absolute bottom-0 left-0 right-0 z-50 bg-gradient-to-t from-black/70 to-transparent p-4">
        <div className="flex items-center justify-center space-x-6">
          <button
            className="bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors backdrop-blur-sm"
            title="收藏"
          >
            <Heart size={20} />
          </button>
          
          <button
            className="bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors backdrop-blur-sm"
            title="分享"
          >
            <Share size={20} />
          </button>
          
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors backdrop-blur-sm font-medium"
            title="预约看房"
          >
            <Calendar size={20} className="inline mr-2" />
            预约看房
          </button>
          
          <div className="bg-black/50 text-white px-4 py-2 rounded-lg backdrop-blur-sm">
            <div className="text-center">
              <div className="text-lg font-bold">
                ${house.metadata.price?.toLocaleString() || '面议'}
              </div>
              <div className="text-xs text-gray-300">
                {house.metadata.totalArea} m² • {house.metadata.bedrooms}室{house.metadata.bathrooms}卫
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 导航提示 */}
      {currentRoom.hotspots.length > 0 && (
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-40 bg-black/70 text-white px-4 py-2 rounded-lg backdrop-blur-sm text-sm">
          点击金色球体可前往其他房间
        </div>
      )}
    </div>
  );
}
