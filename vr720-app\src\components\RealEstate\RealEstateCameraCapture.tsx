'use client';

import React, { useRef, useState, useCallback, useEffect } from 'react';
import { Camera, Download, RotateCcw, CheckCircle, Grid, Sun, AlertTriangle, Info } from 'lucide-react';
import { RoomType } from './RoomTypeSelector';

interface RealEstateCameraCaptureProps {
  onPhotoCapture: (photoData: string, metadata: any) => void;
  onBack?: () => void;
  roomType?: RoomType;
  currentStep?: number;
  totalSteps?: number;
}

export default function RealEstateCameraCapture({
  onPhotoCapture,
  onBack,
  roomType,
  currentStep = 1,
  totalSteps = 8
}: RealEstateCameraCaptureProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const [deviceOrientation, setDeviceOrientation] = useState({ alpha: 0, beta: 0, gamma: 0 });
  const [showGrid, setShowGrid] = useState(true);
  const [exposureMode, setExposureMode] = useState<'auto' | 'manual'>('auto');
  const [qualityWarnings, setQualityWarnings] = useState<string[]>([]);
  const [permissionDenied, setPermissionDenied] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  // Check if device supports camera
  const checkCameraSupport = useCallback(() => {
    if (!navigator.mediaDevices?.getUserMedia) {
      alert('Your device does not support camera functionality');
      return false;
    }
    return true;
  }, []);

  // Start camera
  const startCamera = useCallback(async () => {
    if (!checkCameraSupport()) return;

    setIsInitializing(true);
    setPermissionDenied(false);

    try {
      // 移动设备优化的约束条件
      const constraints = {
        video: {
          facingMode: 'environment',
          width: { ideal: 1920, min: 640 },
          height: { ideal: 1080, min: 480 },
          frameRate: { ideal: 30, min: 15 }
        },
        audio: false
      };

      console.log('Requesting camera access for real estate capture...');
      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;

        // 等待视频加载
        videoRef.current.onloadedmetadata = () => {
          console.log('Real estate camera stream loaded');
          setIsStreaming(true);
          setIsInitializing(false);
          checkImageQuality();
        };

        // 处理播放错误
        videoRef.current.onerror = (error) => {
          console.error('Video error:', error);
          setIsInitializing(false);
          alert('视频播放失败，请重试');
        };

        // 移动设备需要用户交互才能播放
        try {
          await videoRef.current.play();
        } catch (playError) {
          console.error('Play error:', playError);
          // 静默处理，用户点击后会自动播放
        }
      }
    } catch (error) {
      console.error('Camera access error:', error);
      setIsInitializing(false);

      if (error instanceof Error && error.name === 'NotAllowedError') {
        setPermissionDenied(true);
        return;
      }

      let errorMessage = 'Unable to access camera';
      if (error instanceof Error) {
        switch (error.name) {
          case 'NotFoundError':
            errorMessage = 'Camera device not found. Please ensure your device has a camera.';
            break;
          case 'NotSupportedError':
            errorMessage = 'Your browser does not support camera functionality. Please use the latest version of Chrome, Safari, or Firefox.';
            break;
          case 'NotReadableError':
            errorMessage = 'Camera is being used by another application. Please close other camera apps and try again.';
            break;
          case 'OverconstrainedError':
            errorMessage = 'Camera does not support the requested settings. Trying with lower requirements...';
            // Try with more relaxed constraints
            setTimeout(() => startCameraWithFallback(), 1000);
            return;
          default:
            errorMessage = `Camera access failed: ${error.message}`;
        }
      }

      alert(errorMessage);
    }
  }, [checkCameraSupport]);

  // 降级相机启动（用于不支持高分辨率的设备）
  const startCameraWithFallback = useCallback(async () => {
    try {
      const fallbackConstraints = {
        video: {
          facingMode: 'environment',
          width: { ideal: 1280, min: 320 },
          height: { ideal: 720, min: 240 }
        },
        audio: false
      };

      console.log('Trying fallback camera constraints...');
      const stream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          setIsStreaming(true);
          checkImageQuality();
        };
        await videoRef.current.play();
      }
    } catch (fallbackError) {
      console.error('Fallback camera failed:', fallbackError);
      alert('相机启动失败，请检查设备和权限设置');
    }
  }, []);

  // 停止相机
  const stopCamera = useCallback(() => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      setIsStreaming(false);
    }
  }, []);

  // 检查图像质量
  const checkImageQuality = useCallback(() => {
    if (!videoRef.current) return;
    
    const warnings: string[] = [];
    const video = videoRef.current;
    
    // 检查分辨率
    if (video.videoWidth < 1280 || video.videoHeight < 720) {
      warnings.push('分辨率较低，建议使用更高分辨率');
    }
    
    // 检查设备倾斜
    if (Math.abs(deviceOrientation.beta) > 15 || Math.abs(deviceOrientation.gamma) > 15) {
      warnings.push('设备倾斜过大，请保持水平');
    }
    
    setQualityWarnings(warnings);
  }, [deviceOrientation]);

  // 拍照
  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // 应用图像增强
    context.filter = 'contrast(1.1) brightness(1.05) saturate(1.1)';
    context.drawImage(video, 0, 0);
    
    // 重置滤镜
    context.filter = 'none';

    const photoData = canvas.toDataURL('image/jpeg', 0.92);
    const metadata = {
      timestamp: Date.now(),
      orientation: deviceOrientation,
      step: currentStep,
      roomType: roomType?.id || 'unknown',
      roomName: roomType?.name || 'Unknown Room',
      resolution: {
        width: canvas.width,
        height: canvas.height
      },
      qualityScore: calculateQualityScore(),
      warnings: qualityWarnings
    };

    setCapturedPhotos(prev => [...prev, photoData]);
    onPhotoCapture(photoData, metadata);
  }, [onPhotoCapture, currentStep, deviceOrientation, roomType, qualityWarnings]);

  // Calculate quality score
  const calculateQualityScore = useCallback(() => {
    let score = 100;

    // Deduct points based on warnings
    score -= qualityWarnings.length * 10;

    // Score based on device stability
    const tilt = Math.abs(deviceOrientation.beta) + Math.abs(deviceOrientation.gamma);
    if (tilt > 20) score -= 20;
    else if (tilt > 10) score -= 10;

    return Math.max(score, 0);
  }, [qualityWarnings, deviceOrientation]);

  // Listen to device orientation
  useEffect(() => {
    const handleOrientation = (event: DeviceOrientationEvent) => {
      setDeviceOrientation({
        alpha: event.alpha || 0,
        beta: event.beta || 0,
        gamma: event.gamma || 0
      });
    };

    if (window.DeviceOrientationEvent) {
      window.addEventListener('deviceorientation', handleOrientation);
      return () => window.removeEventListener('deviceorientation', handleOrientation);
    }
  }, []);

  // 定期检查质量
  useEffect(() => {
    if (isStreaming) {
      const interval = setInterval(checkImageQuality, 1000);
      return () => clearInterval(interval);
    }
  }, [isStreaming, checkImageQuality]);

  // 权限被拒绝时的UI
  if (permissionDenied) {
    return (
      <div className="relative w-full h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white p-8 max-w-md">
          <div className="text-6xl mb-6">📷</div>
          <h2 className="text-2xl font-bold mb-4">需要相机权限</h2>
          <p className="text-gray-300 mb-6 leading-relaxed">
            为了拍摄房地产全景照片，我们需要访问您的相机。
            请在浏览器设置中允许相机访问权限。
          </p>

          <div className="space-y-4">
            <button
              onClick={startCamera}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg transition-colors"
            >
              重新请求权限
            </button>

            <button
              onClick={onBack}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg transition-colors"
            >
              返回
            </button>
          </div>

          <div className="mt-6 text-sm text-gray-400">
            <p className="mb-2">如何允许相机权限：</p>
            <div className="text-left space-y-1">
              <p>• Chrome: 点击地址栏左侧的🔒图标</p>
              <p>• Safari: 设置 → Safari → 相机</p>
              <p>• Firefox: 点击地址栏左侧的盾牌图标</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Initializing UI
  if (isInitializing) {
    return (
      <div className="relative w-full h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold mb-2">Starting Camera...</h2>
          <p className="text-gray-300">Please wait, connecting to your camera device</p>
        </div>
      </div>
    );
  }

  // Camera not started UI
  if (!isStreaming) {
    return (
      <div className="relative w-full h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white p-8 max-w-md">
          <div className="text-6xl mb-6">📸</div>
          <h2 className="text-2xl font-bold mb-4">Professional Real Estate Photography</h2>
          <p className="text-gray-300 mb-6 leading-relaxed">
            Use professional shooting guidance and quality detection features
            to capture high-quality panoramic photos for your real estate projects.
          </p>

          <button
            onClick={startCamera}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-4 px-8 rounded-lg transition-colors text-lg font-semibold"
          >
            Start Capturing
          </button>

          <button
            onClick={onBack}
            className="w-full mt-4 bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg transition-colors"
          >
            Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-screen bg-black overflow-hidden">
      {/* Video preview */}
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted
        className="w-full h-full object-cover"
        onClick={() => {
          // On mobile devices, clicking video can trigger playback
          if (videoRef.current?.paused) {
            videoRef.current.play().catch(console.error);
          }
        }}
      />

      {/* Hidden canvas for photo capture */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Grid lines */}
      {showGrid && isStreaming && (
        <div className="absolute inset-0 pointer-events-none">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="grid" width="33.33" height="33.33" patternUnits="userSpaceOnUse">
                <path d="M 33.33 0 L 33.33 33.33 M 0 33.33 L 33.33 33.33" 
                      fill="none" stroke="rgba(255,255,255,0.3)" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
      )}

      {/* Room information and shooting guidance */}
      {roomType && (
        <div className="absolute top-4 left-4 right-4 bg-black/80 text-white p-4 rounded-lg backdrop-blur-sm">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <div className="text-blue-400 mr-2">{roomType.icon}</div>
              <span className="font-semibold">{roomType.name}</span>
            </div>
            <span className="text-sm bg-blue-600 px-2 py-1 rounded">
              {currentStep}/{totalSteps}
            </span>
          </div>
          
          <div className="text-sm text-gray-300 mb-2">
            Recommended Height: {roomType.recommendedHeight}
          </div>
          
          <div className="w-full bg-gray-600 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>
      )}

      {/* Quality warnings */}
      {qualityWarnings.length > 0 && (
        <div className="absolute top-32 left-4 right-4 bg-yellow-600/90 text-white p-3 rounded-lg backdrop-blur-sm">
          <div className="flex items-center mb-2">
            <AlertTriangle size={16} className="mr-2" />
            <span className="font-semibold">Photography Tips</span>
          </div>
          {qualityWarnings.map((warning, index) => (
            <div key={index} className="text-sm">• {warning}</div>
          ))}
        </div>
      )}

      {/* Device orientation indicator */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <div className="w-32 h-32 border-2 border-white/30 rounded-full flex items-center justify-center">
          <div 
            className="w-4 h-4 bg-blue-500 rounded-full transition-transform duration-100"
            style={{ 
              transform: `rotate(${deviceOrientation.alpha}deg) translateY(-40px)` 
            }}
          />
          <div className="absolute text-white text-xs">
            {Math.round(deviceOrientation.alpha)}°
          </div>
        </div>
      </div>

      {/* Control buttons */}
      <div className="absolute bottom-8 left-0 right-0">
        {/* Top toolbar */}
        <div className="flex justify-center mb-4 space-x-4">
          <button
            onClick={() => setShowGrid(!showGrid)}
            className={`p-3 rounded-full transition-colors ${
              showGrid ? 'bg-blue-500 text-white' : 'bg-black/50 text-white'
            }`}
            title="网格线"
          >
            <Grid size={20} />
          </button>
          
          <button
            onClick={() => setExposureMode(exposureMode === 'auto' ? 'manual' : 'auto')}
            className={`p-3 rounded-full transition-colors ${
              exposureMode === 'manual' ? 'bg-yellow-500 text-white' : 'bg-black/50 text-white'
            }`}
            title="Exposure Mode"
          >
            <Sun size={20} />
          </button>
        </div>

        {/* Main control buttons */}
        <div className="flex justify-center items-center space-x-8">
          {!isStreaming ? (
            <button
              onClick={startCamera}
              className="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-full transition-colors shadow-lg"
            >
              <Camera size={32} />
            </button>
          ) : (
            <>
              <button
                onClick={stopCamera}
                className="bg-red-500 hover:bg-red-600 text-white p-3 rounded-full transition-colors"
              >
                <RotateCcw size={24} />
              </button>
              
              <button
                onClick={capturePhoto}
                className="bg-white hover:bg-gray-100 text-black p-6 rounded-full transition-colors shadow-lg relative"
              >
                <Camera size={40} />
                {qualityWarnings.length === 0 && (
                  <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <CheckCircle size={16} className="text-white" />
                  </div>
                )}
              </button>
              
              {capturedPhotos.length > 0 && (
                <div className="relative bg-green-500 text-white p-3 rounded-full">
                  <CheckCircle size={24} />
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
                    {capturedPhotos.length}
                  </span>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Captured photos preview */}
      {capturedPhotos.length > 0 && (
        <div className="absolute bottom-32 left-4 right-4">
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {capturedPhotos.map((photo, index) => (
              <div key={index} className="relative flex-shrink-0">
                <img
                  src={photo}
                  alt={`拍摄 ${index + 1}`}
                  className="w-16 h-16 object-cover rounded border-2 border-white/50"
                />
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">{index + 1}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
