'use client';

import React, { useState } from 'react';
import { Home, Bed, ChefHat, Bath, Car, TreePine, Building, ArrowRight } from 'lucide-react';

export interface RoomType {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  tips: string[];
  recommendedHeight: string;
  lightingTips: string;
}

const roomTypes: RoomType[] = [
  {
    id: 'living-room',
    name: 'Living Room',
    icon: <Home size={32} />,
    description: 'Showcase the main living space of the home',
    tips: [
      'Stand in the center of the room to capture all major furniture',
      'Avoid backlighting, utilize natural light',
      'Ensure TV, sofa and key furniture are in view',
      'Pay attention to ceiling decorations and lighting'
    ],
    recommendedHeight: '5.2 feet (eye level height)',
    lightingTips: 'Turn on all lights and open curtains for natural light'
  },
  {
    id: 'bedroom',
    name: 'Bedroom',
    icon: <Bed size={32} />,
    description: 'Display the private and comfortable resting space',
    tips: [
      'Stand at the foot of the bed to show the entire bedroom layout',
      'Ensure the bed is neat with pillows arranged properly',
      'Show wardrobes, dressing tables and storage spaces',
      'Pay attention to windows and lighting conditions'
    ],
    recommendedHeight: '4.9 feet (slightly lower than living room)',
    lightingTips: 'Use warm lighting to create a comfortable atmosphere'
  },
  {
    id: 'kitchen',
    name: 'Kitchen',
    icon: <ChefHat size={32} />,
    description: 'Showcase the cooking and dining areas',
    tips: [
      'Stand in front of the kitchen island or counter',
      'Show cabinets, appliances and workspace',
      'Ensure countertops are clean and clutter-free',
      'Highlight kitchen functionality and storage space'
    ],
    recommendedHeight: '4.9 feet (counter height)',
    lightingTips: 'Turn on all lighting, including under-cabinet lights'
  },
  {
    id: 'bathroom',
    name: 'Bathroom',
    icon: <Bath size={32} />,
    description: 'Showcase bathroom facilities and space',
    tips: [
      'Stand at the doorway or center position',
      'Show bathtub, shower, vanity and other fixtures',
      'Ensure mirrors are clean and towels are neat',
      'Pay attention to ventilation and lighting'
    ],
    recommendedHeight: '4.6 feet (adapted for smaller spaces)',
    lightingTips: 'Use bright white light to ensure clear details'
  },
  {
    id: 'outdoor',
    name: 'Outdoor Space',
    icon: <TreePine size={32} />,
    description: 'Showcase garden, balcony or patio areas',
    tips: [
      'Choose a position that shows the entire outdoor space',
      'Display landscaping, furniture and functional areas',
      'Pay attention to weather and lighting conditions',
      'Highlight outdoor living possibilities'
    ],
    recommendedHeight: '5.2 feet (standard height)',
    lightingTips: 'Utilize natural light, avoid harsh shadows'
  },
  {
    id: 'garage',
    name: 'Garage',
    icon: <Car size={32} />,
    description: 'Showcase parking and storage space',
    tips: [
      'Stand at the garage entrance',
      'Show parking spaces and storage areas',
      'Ensure the floor is clean',
      'Highlight the practicality of the space'
    ],
    recommendedHeight: '5.2 feet (standard height)',
    lightingTips: 'Turn on all lighting to ensure bright space'
  }
];

interface RoomTypeSelectorProps {
  onRoomSelect: (roomType: RoomType) => void;
  onSkip: () => void;
}

export default function RoomTypeSelector({ onRoomSelect, onSkip }: RoomTypeSelectorProps) {
  const [selectedRoom, setSelectedRoom] = useState<RoomType | null>(null);

  const handleRoomClick = (room: RoomType) => {
    setSelectedRoom(room);
  };

  const handleConfirm = () => {
    if (selectedRoom) {
      onRoomSelect(selectedRoom);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Select Room Type</h1>
          <p className="text-blue-200">Choose the room type you want to photograph for professional shooting guidance</p>
        </div>

        {/* Room type grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
          {roomTypes.map((room) => (
            <button
              key={room.id}
              onClick={() => handleRoomClick(room)}
              className={`p-6 rounded-xl border-2 transition-all duration-300 transform hover:scale-105 ${
                selectedRoom?.id === room.id
                  ? 'border-yellow-400 bg-yellow-400/20 text-white'
                  : 'border-white/20 bg-white/10 text-white hover:border-white/40 hover:bg-white/20'
              }`}
            >
              <div className="flex flex-col items-center text-center">
                <div className={`mb-3 ${selectedRoom?.id === room.id ? 'text-yellow-300' : 'text-blue-300'}`}>
                  {room.icon}
                </div>
                <h3 className="font-semibold mb-1">{room.name}</h3>
                <p className="text-sm opacity-80">{room.description}</p>
              </div>
            </button>
          ))}
        </div>

        {/* Selected room details */}
        {selectedRoom && (
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6 border border-white/20">
            <div className="flex items-center mb-4">
              <div className="text-yellow-300 mr-3">{selectedRoom.icon}</div>
              <h2 className="text-2xl font-bold text-white">{selectedRoom.name}</h2>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-blue-200 mb-3">📸 Photography Tips</h3>
                <ul className="space-y-2">
                  {selectedRoom.tips.map((tip, index) => (
                    <li key={index} className="text-white text-sm flex items-start">
                      <span className="text-green-400 mr-2">•</span>
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-blue-200 mb-3">⚙️ Camera Settings</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-yellow-300 font-medium">Recommended Height:</span>
                    <span className="text-white ml-2">{selectedRoom.recommendedHeight}</span>
                  </div>
                  <div>
                    <span className="text-yellow-300 font-medium">Lighting Tips:</span>
                    <span className="text-white ml-2">{selectedRoom.lightingTips}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-center space-x-4">
          <button
            onClick={onSkip}
            className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            Skip Selection
          </button>
          
          {selectedRoom && (
            <button
              onClick={handleConfirm}
              className="px-8 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white rounded-lg font-semibold transition-all duration-300 flex items-center"
            >
              Start Capturing {selectedRoom.name}
              <ArrowRight size={20} className="ml-2" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
