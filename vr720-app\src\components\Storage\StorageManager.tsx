import React, { useState, useEffect } from 'react';
import { indexedDBStorage } from '@/utils/indexedDBStorage';
import { getStorageInfo, migrateImagesToIndexedDB, isMigrationNeeded } from '@/utils/storageMigration';

interface StorageInfo {
  localStorage: {
    used: number;
    imageCount: number;
    quota: number;
  };
  indexedDB: {
    used: number;
    quota: number;
    imageCount: number;
  };
}

const StorageManager: React.FC = () => {
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCleaningUp, setIsCleaningUp] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  const loadStorageInfo = async () => {
    try {
      setIsLoading(true);
      const info = await getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Failed to load storage info:', error);
      setMessage({ type: 'error', text: 'Failed to load storage information' });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadStorageInfo();
  }, []);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getUsagePercentage = (used: number, quota: number): number => {
    if (quota === 0) return 0;
    return Math.min((used / quota) * 100, 100);
  };

  const handleCleanupOldImages = async () => {
    try {
      setIsCleaningUp(true);
      setMessage(null);
      
      const deletedCount = await indexedDBStorage.cleanupOldImages(7); // Delete images older than 7 days
      
      if (deletedCount > 0) {
        setMessage({ 
          type: 'success', 
          text: `Successfully cleaned up ${deletedCount} old images` 
        });
      } else {
        setMessage({ 
          type: 'info', 
          text: 'No old images found to clean up' 
        });
      }
      
      // Refresh storage info
      await loadStorageInfo();
    } catch (error) {
      console.error('Failed to cleanup old images:', error);
      setMessage({ type: 'error', text: 'Failed to cleanup old images' });
    } finally {
      setIsCleaningUp(false);
    }
  };

  const handleMigrateFromLocalStorage = async () => {
    try {
      setIsMigrating(true);
      setMessage(null);
      
      const result = await migrateImagesToIndexedDB();
      
      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: `Successfully migrated ${result.migratedCount} images from localStorage` 
        });
      } else {
        setMessage({ 
          type: 'error', 
          text: `Migration failed: ${result.errors.join(', ')}` 
        });
      }
      
      // Refresh storage info
      await loadStorageInfo();
    } catch (error) {
      console.error('Failed to migrate from localStorage:', error);
      setMessage({ type: 'error', text: 'Failed to migrate from localStorage' });
    } finally {
      setIsMigrating(false);
    }
  };

  const handleClearAllImages = async () => {
    if (!confirm('Are you sure you want to delete all images? This action cannot be undone.')) {
      return;
    }

    try {
      setIsLoading(true);
      setMessage(null);
      
      const success = await indexedDBStorage.clearAllImages();
      
      if (success) {
        setMessage({ 
          type: 'success', 
          text: 'Successfully cleared all images' 
        });
      } else {
        setMessage({ type: 'error', text: 'Failed to clear all images' });
      }
      
      // Refresh storage info
      await loadStorageInfo();
    } catch (error) {
      console.error('Failed to clear all images:', error);
      setMessage({ type: 'error', text: 'Failed to clear all images' });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && !storageInfo) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
          📊 Storage Management
        </h2>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading storage information...</span>
        </div>
      </div>
    );
  }

  const getMessageClass = (type: string) => {
    switch (type) {
      case 'error': return 'border-red-500 bg-red-50 text-red-700';
      case 'success': return 'border-green-500 bg-green-50 text-green-700';
      default: return 'border-blue-500 bg-blue-50 text-blue-700';
    }
  };

  return (
    <div className="space-y-4">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-6 flex items-center gap-2">
          📊 Storage Management
        </h2>
        <div className="space-y-6">
          {message && (
            <div className={`border-l-4 p-4 rounded ${getMessageClass(message.type)}`}>
              {message.text}
            </div>
          )}

          {storageInfo && (
            <>
              {/* IndexedDB Storage */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  💾 <h3 className="font-semibold">IndexedDB Storage (Primary)</h3>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Used: {formatBytes(storageInfo.indexedDB.used)}</span>
                    <span>Quota: {formatBytes(storageInfo.indexedDB.quota)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${getUsagePercentage(storageInfo.indexedDB.used, storageInfo.indexedDB.quota)}%` }}
                    ></div>
                  </div>
                  <div className="text-sm text-gray-600">
                    Images stored: {storageInfo.indexedDB.imageCount}
                  </div>
                </div>
              </div>

              {/* LocalStorage Info */}
              {storageInfo.localStorage.imageCount > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    🗄️ <h3 className="font-semibold">LocalStorage (Legacy)</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used: {formatBytes(storageInfo.localStorage.used)}</span>
                      <span>Quota: {formatBytes(storageInfo.localStorage.quota)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-orange-600 h-2 rounded-full"
                        style={{ width: `${getUsagePercentage(storageInfo.localStorage.used, storageInfo.localStorage.quota)}%` }}
                      ></div>
                    </div>
                    <div className="text-sm text-gray-600">
                      Images stored: {storageInfo.localStorage.imageCount}
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3">
            <button
              onClick={loadStorageInfo}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              🔄 {isLoading ? 'Refreshing...' : 'Refresh'}
            </button>

            <button
              onClick={handleCleanupOldImages}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isCleaningUp || isLoading}
            >
              🗑️ {isCleaningUp ? 'Cleaning...' : 'Cleanup Old Images'}
            </button>

            {storageInfo && isMigrationNeeded() && (
              <button
                onClick={handleMigrateFromLocalStorage}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isMigrating || isLoading}
              >
                📦 {isMigrating ? 'Migrating...' : 'Migrate from LocalStorage'}
              </button>
            )}

            <button
              onClick={handleClearAllImages}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              🗑️ Clear All Images
            </button>
          </div>

          {/* Storage Tips */}
          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Tips:</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>IndexedDB can store much larger amounts of data than localStorage</li>
              <li>Old images (7+ days) can be automatically cleaned up to save space</li>
              <li>Large panoramic images may use significant storage space</li>
              <li>Consider clearing unused images regularly for optimal performance</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StorageManager;
