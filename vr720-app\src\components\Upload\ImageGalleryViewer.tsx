'use client';

import React, { useState, useCallback } from 'react';
import { ArrowLeft, Grid, List, Search, AlertCircle, Loader2, Settings } from 'lucide-react';
import ImageUploader from './ImageUploader';
import AFrameViewer from '../VR/AFrameViewer';
import PresetPanoramaSelector from './PresetPanoramaSelector';
import StorageManager from './StorageManager';
import { RealPanoramaImage } from '../Demo/RealPanoramaData';
import { usePersistedImages, UploadedImage } from '@/hooks/usePersistedState';

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  name: string;
  size: number;
  type: string;
  uploadTime: number;
  metadata?: {
    width?: number;
    height?: number;
    aspectRatio?: number;
  };
}

interface ImageGalleryViewerProps {
  onBack: () => void;
}

type ViewMode = 'upload' | 'gallery' | 'vr';
type SortBy = 'name' | 'size' | 'date' | 'ratio';

export default function ImageGalleryViewer({ onBack }: ImageGalleryViewerProps) {
  // 使用持久化图片状态
  const {
    images: allImages,
    isLoading: imagesLoading,
    error: imagesError,
    addImages,
    removeImage,
    clearImages,
    getImageStats,
    clearError
  } = usePersistedImages();

  const [currentMode, setCurrentMode] = useState<ViewMode>('upload');
  const [selectedImage, setSelectedImage] = useState<UploadedImage | null>(null);
  const [selectedPresetImage, setSelectedPresetImage] = useState<RealPanoramaImage | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<SortBy>('date');
  const [filterType, setFilterType] = useState<'all' | 'panoramic' | 'regular'>('all');
  const [showPresetSelector, setShowPresetSelector] = useState(false);
  const [showStorageManager, setShowStorageManager] = useState(false);

  // 处理图片上传
  const handleImagesUploaded = useCallback(async (newImages: UploadedImage[]) => {
    const success = await addImages(newImages);
    if (success && newImages.length > 0) {
      setCurrentMode('gallery');
    }
  }, [addImages]);

  // 选择图片进行VR查看
  const handleImageSelect = useCallback((image: UploadedImage) => {
    setSelectedImage(image);
    setSelectedPresetImage(null);
    setCurrentMode('vr');
  }, []);

  // 选择预设图片进行VR查看
  const handlePresetImageSelect = useCallback((image: RealPanoramaImage) => {
    setSelectedPresetImage(image);
    setSelectedImage(null);
    setShowPresetSelector(false);
    setCurrentMode('vr');
  }, []);

  // 过滤和排序图片
  const filteredAndSortedImages = useCallback(() => {
    let filtered = allImages;

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(img =>
        img.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // 类型过滤
    if (filterType !== 'all') {
      filtered = filtered.filter(img => {
        const ratio = img.metadata?.aspectRatio || 1;
        if (filterType === 'panoramic') {
          return ratio > 1.8; // 全景图通常是2:1或更宽
        } else {
          return ratio <= 1.8; // 普通图片
        }
      });
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'size':
          return b.size - a.size;
        case 'date':
          return b.uploadTime - a.uploadTime;
        case 'ratio':
          return (b.metadata?.aspectRatio || 1) - (a.metadata?.aspectRatio || 1);
        default:
          return 0;
      }
    });

    return filtered;
  }, [allImages, searchTerm, filterType, sortBy]);

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // 渲染上传页面
  const renderUploadPage = () => (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow-sm p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <button
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            返回
          </button>
          
          <h1 className="text-xl font-semibold text-gray-800">图片上传与VR查看</h1>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setShowPresetSelector(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              📸 预设图片
            </button>

            <button
              onClick={() => setShowStorageManager(true)}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Settings size={16} className="inline mr-1" />
              存储管理
            </button>

            {allImages.length > 0 && (
              <button
                onClick={() => setCurrentMode('gallery')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                查看图库 ({allImages.length})
              </button>
            )}
          </div>
        </div>
      </div>
      
      <div className="p-6">
        {/* 加载状态 */}
        {imagesLoading && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center">
              <Loader2 className="animate-spin text-blue-600 mr-3" size={20} />
              <span className="text-blue-800">正在加载已保存的图片...</span>
            </div>
          </div>
        )}

        {/* 错误提示 */}
        {imagesError && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="text-red-600 mr-3" size={20} />
                <span className="text-red-800">{imagesError}</span>
              </div>
              <button
                onClick={clearError}
                className="text-red-600 hover:text-red-800 text-sm font-medium"
              >
                关闭
              </button>
            </div>
          </div>
        )}

        {/* 存储统计信息 */}
        {allImages.length > 0 && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-green-800 mb-1">图片库统计</h3>
                <p className="text-sm text-green-700">
                  已保存 {getImageStats().total} 张图片，
                  其中全景图 {getImageStats().panoramic} 张，
                  普通图片 {getImageStats().regular} 张
                </p>
              </div>
              <button
                onClick={() => {
                  if (window.confirm('确定要清空所有已保存的图片吗？此操作不可恢复。')) {
                    clearImages();
                  }
                }}
                className="text-red-600 hover:text-red-800 text-sm font-medium"
              >
                清空全部
              </button>
            </div>
          </div>
        )}

        <ImageUploader
          onImagesUploaded={handleImagesUploaded}
          onImageSelect={handleImageSelect}
          maxFiles={50}
          maxFileSize={100}
        />
      </div>
    </div>
  );

  // 渲染图库页面
  const renderGalleryPage = () => {
    const images = filteredAndSortedImages();
    
    return (
      <div className="min-h-screen bg-gray-100">
        {/* 顶部工具栏 */}
        <div className="bg-white shadow-sm p-4">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={() => setCurrentMode('upload')}
                className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft size={20} className="mr-2" />
                返回上传
              </button>
              
              <h1 className="text-xl font-semibold text-gray-800">
                图片库 ({images.length}/{allImages.length})
              </h1>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                  className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  {viewMode === 'grid' ? <List size={20} /> : <Grid size={20} />}
                </button>
              </div>
            </div>
            
            {/* 搜索和过滤 */}
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex-1 min-w-64">
                <div className="relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="搜索图片..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as typeof filterType)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">所有图片</option>
                <option value="panoramic">全景图片</option>
                <option value="regular">普通图片</option>
              </select>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortBy)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="date">按日期排序</option>
                <option value="name">按名称排序</option>
                <option value="size">按大小排序</option>
                <option value="ratio">按比例排序</option>
              </select>
            </div>
          </div>
        </div>
        
        {/* 图片网格/列表 */}
        <div className="p-6">
          <div className="max-w-6xl mx-auto">
            {images.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  {allImages.length === 0 ? '还没有上传任何图片' : '没有找到匹配的图片'}
                </div>
                <button
                  onClick={() => setCurrentMode('upload')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
                >
                  {allImages.length === 0 ? '开始上传' : '返回上传更多'}
                </button>
              </div>
            ) : (
              <div className={
                viewMode === 'grid' 
                  ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'
                  : 'space-y-4'
              }>
                {images.map((image) => (
                  <div
                    key={image.id}
                    className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer ${
                      viewMode === 'list' ? 'flex items-center p-4' : ''
                    }`}
                    onClick={() => handleImageSelect(image)}
                  >
                    <div className={viewMode === 'list' ? 'w-24 h-16 flex-shrink-0 mr-4' : 'aspect-video'}>
                      <img
                        src={image.url}
                        alt={image.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    <div className={viewMode === 'list' ? 'flex-1' : 'p-4'}>
                      <h3 className="font-medium text-gray-800 truncate mb-1">
                        {image.name}
                      </h3>
                      
                      <div className="text-sm text-gray-500 space-y-1">
                        <div>大小: {formatFileSize(image.size)}</div>
                        {image.metadata && (
                          <div>
                            {image.metadata.width} × {image.metadata.height}
                          </div>
                        )}
                        <div className="flex items-center">
                          <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                            (image.metadata?.aspectRatio || 1) > 1.8 ? 'bg-green-500' : 'bg-blue-500'
                          }`} />
                          {(image.metadata?.aspectRatio || 1) > 1.8 ? '全景图' : '普通图片'}
                        </div>
                      </div>
                      
                      {viewMode === 'grid' && (
                        <div className="flex space-x-2 mt-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleImageSelect(image);
                            }}
                            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors text-sm"
                          >
                            VR查看
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (window.confirm('确定要删除这张图片吗？')) {
                                removeImage(image.id);
                              }
                            }}
                            className="bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded transition-colors text-sm"
                          >
                            删除
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 渲染VR查看页面
  const renderVRPage = () => {
    const currentImage = selectedImage || selectedPresetImage;

    if (!currentImage) {
      return (
        <div className="min-h-screen bg-black flex items-center justify-center">
          <div className="text-white text-center">
            <p className="text-xl mb-4">没有选择图片</p>
            <button
              onClick={() => setCurrentMode(allImages.length > 0 ? 'gallery' : 'upload')}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors"
            >
              {allImages.length > 0 ? '返回图库' : '返回上传'}
            </button>
          </div>
        </div>
      );
    }

    // 处理预设图片
    if (selectedPresetImage) {
      return (
        <AFrameViewer
          panoramaUrl={selectedPresetImage.url}
          onBack={() => setCurrentMode('upload')}
          onReset={() => window.location.reload()}
          roomInfo={{
            name: selectedPresetImage.name,
            type: selectedPresetImage.category,
            description: selectedPresetImage.description
          }}
        />
      );
    }

    // 处理上传的图片
    if (selectedImage) {
      return (
        <AFrameViewer
          panoramaUrl={selectedImage.url}
          onBack={() => setCurrentMode('gallery')}
          onReset={() => window.location.reload()}
          roomInfo={{
            name: selectedImage.name,
            type: 'uploaded-image',
            description: `${selectedImage.metadata?.width || 0} × ${selectedImage.metadata?.height || 0} | ${formatFileSize(selectedImage.size)}`
          }}
        />
      );
    }

    return null;
  };

  // 主渲染逻辑
  const mainContent = () => {
    switch (currentMode) {
      case 'upload':
        return renderUploadPage();
      case 'gallery':
        return renderGalleryPage();
      case 'vr':
        return renderVRPage();
      default:
        return renderUploadPage();
    }
  };

  return (
    <>
      {mainContent()}

      {/* 预设图片选择器 */}
      {showPresetSelector && (
        <PresetPanoramaSelector
          onImageSelect={handlePresetImageSelect}
          onClose={() => setShowPresetSelector(false)}
        />
      )}

      {/* 存储管理器 */}
      {showStorageManager && (
        <StorageManager
          onClose={() => setShowStorageManager(false)}
        />
      )}
    </>
  );
}
