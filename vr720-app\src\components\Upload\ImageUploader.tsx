'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Upload, X, Eye, Download, FileImage, AlertCircle, CheckCircle } from 'lucide-react';

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  name: string;
  size: number;
  type: string;
  uploadTime: number;
  isProcessing?: boolean;
  metadata?: {
    width?: number;
    height?: number;
    aspectRatio?: number;
  };
}

interface ImageUploaderProps {
  onImagesUploaded: (images: UploadedImage[]) => Promise<void> | void;
  onImageSelect: (image: UploadedImage) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  maxFileSize?: number; // in MB
}

export default function ImageUploader({
  onImagesUploaded,
  onImageSelect,
  maxFiles = 10,
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  maxFileSize = 50
}: ImageUploaderProps) {
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 验证文件
  const validateFile = useCallback((file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `不支持的文件类型: ${file.type}`;
    }
    
    if (file.size > maxFileSize * 1024 * 1024) {
      return `文件大小超过限制: ${(file.size / 1024 / 1024).toFixed(1)}MB > ${maxFileSize}MB`;
    }
    
    return null;
  }, [acceptedTypes, maxFileSize]);

  // 获取图片元数据
  const getImageMetadata = useCallback((file: File): Promise<{width: number, height: number, aspectRatio: number}> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight,
          aspectRatio: img.naturalWidth / img.naturalHeight
        });
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }, []);

  // 处理文件上传
  const handleFiles = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    const newErrors: string[] = [];
    const validFiles: File[] = [];

    // 检查文件数量限制
    if (uploadedImages.length + fileArray.length > maxFiles) {
      newErrors.push(`最多只能上传 ${maxFiles} 个文件`);
      setErrors(newErrors);
      return;
    }

    // 验证每个文件
    for (const file of fileArray) {
      const error = validateFile(file);
      if (error) {
        newErrors.push(`${file.name}: ${error}`);
      } else {
        validFiles.push(file);
      }
    }

    if (newErrors.length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors([]);
    setIsUploading(true);
    setUploadProgress(0);

    try {
      const newImages: UploadedImage[] = [];
      
      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];
        setUploadProgress((i / validFiles.length) * 100);
        
        // 获取图片元数据
        const metadata = await getImageMetadata(file);
        
        // 创建图片对象
        const imageUrl = URL.createObjectURL(file);
        const uploadedImage: UploadedImage = {
          id: `img_${Date.now()}_${i}`,
          file,
          url: imageUrl,
          name: file.name,
          size: file.size,
          type: file.type,
          uploadTime: Date.now(),
          metadata
        };
        
        newImages.push(uploadedImage);
      }

      setUploadProgress(100);
      setUploadedImages(prev => [...prev, ...newImages]);

      // 支持异步上传处理
      try {
        await onImagesUploaded(newImages);
      } catch (uploadError) {
        console.error('Failed to process uploaded images:', uploadError);
        setErrors(['图片处理失败，请重试']);
      }

      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 500);

    } catch (error) {
      console.error('上传失败:', error);
      setErrors(['上传过程中发生错误，请重试']);
      setIsUploading(false);
    }
  }, [uploadedImages.length, maxFiles, validateFile, getImageMetadata, onImagesUploaded]);

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  }, [handleFiles]);

  // 文件选择处理
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = '';
  }, [handleFiles]);

  // 删除图片
  const removeImage = useCallback((imageId: string) => {
    setUploadedImages(prev => {
      const updated = prev.filter(img => img.id !== imageId);
      // 释放URL对象
      const removedImage = prev.find(img => img.id === imageId);
      if (removedImage) {
        URL.revokeObjectURL(removedImage.url);
      }
      return updated;
    });
  }, []);

  // 清空所有图片
  const clearAllImages = useCallback(() => {
    uploadedImages.forEach(img => URL.revokeObjectURL(img.url));
    setUploadedImages([]);
    setErrors([]);
  }, [uploadedImages]);

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">
            📸 上传全景图片
          </h2>
          {uploadedImages.length > 0 && (
            <button
              onClick={clearAllImages}
              className="text-red-600 hover:text-red-800 text-sm font-medium"
            >
              清空所有
            </button>
          )}
        </div>

        {/* 上传区域 */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${
            isDragging
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={acceptedTypes.join(',')}
            onChange={handleFileSelect}
            className="hidden"
          />
          
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <Upload size={32} className="text-blue-600" />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">
                拖拽图片到这里或点击上传
              </h3>
              <p className="text-gray-500 text-sm mb-4">
                支持 JPEG、PNG、WebP 格式，最大 {maxFileSize}MB
              </p>
              
              <button
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                {isUploading ? '上传中...' : '选择文件'}
              </button>
            </div>
            
            <div className="text-xs text-gray-400">
              已上传 {uploadedImages.length}/{maxFiles} 个文件
            </div>
          </div>
        </div>

        {/* 上传进度 */}
        {isUploading && (
          <div className="mt-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-600">上传进度</span>
              <span className="text-sm text-gray-600">{uploadProgress.toFixed(0)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {errors.length > 0 && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center mb-2">
              <AlertCircle size={20} className="text-red-600 mr-2" />
              <span className="font-medium text-red-800">上传错误</span>
            </div>
            <ul className="text-sm text-red-700 space-y-1">
              {errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 已上传的图片列表 */}
        {uploadedImages.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-semibold text-gray-700 mb-4">
              已上传的图片 ({uploadedImages.length})
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {uploadedImages.map((image) => (
                <div
                  key={image.id}
                  className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors"
                >
                  <div className="relative mb-3">
                    <img
                      src={image.url}
                      alt={image.name}
                      className="w-full h-32 object-cover rounded cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => onImageSelect(image)}
                    />
                    <button
                      onClick={() => removeImage(image.id)}
                      className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1 rounded-full transition-colors"
                    >
                      <X size={16} />
                    </button>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 truncate">
                        {image.name}
                      </span>
                      <FileImage size={16} className="text-gray-400" />
                    </div>
                    
                    <div className="text-xs text-gray-500 space-y-1">
                      <div>大小: {formatFileSize(image.size)}</div>
                      {image.metadata && (
                        <div>
                          尺寸: {image.metadata.width} × {image.metadata.height}
                        </div>
                      )}
                      <div>
                        比例: {image.metadata?.aspectRatio ? 
                          (image.metadata.aspectRatio > 1.8 ? '全景图' : '普通图片') : 
                          '未知'
                        }
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 pt-2">
                      <button
                        onClick={() => onImageSelect(image)}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-3 rounded transition-colors"
                      >
                        <Eye size={14} className="inline mr-1" />
                        VR查看
                      </button>
                      
                      <a
                        href={image.url}
                        download={image.name}
                        className="bg-gray-600 hover:bg-gray-700 text-white text-xs py-2 px-3 rounded transition-colors"
                      >
                        <Download size={14} />
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center mb-2">
            <CheckCircle size={20} className="text-blue-600 mr-2" />
            <span className="font-medium text-blue-800">使用说明</span>
          </div>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• 支持拖拽上传，可同时选择多个文件</li>
            <li>• 推荐上传2:1比例的全景图片获得最佳VR效果</li>
            <li>• 上传后点击"VR查看"按钮即可进入沉浸式体验</li>
            <li>• 支持JPEG、PNG、WebP格式，文件大小不超过{maxFileSize}MB</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
