'use client';

import React, { useState, useRef, useCallback } from 'react';
import {
  Upload,
  Camera,
  X,
  Check,
  Image as ImageIcon,
  Eye,
  Download,
  RotateCw,
  Trash2,
  AlertTriangle,
  Info
} from 'lucide-react';
import { stitchImagesToPanorama, validateImagesForStitching, getOptimalPanoramaDimensions } from '@/utils/panoramaStitcher';

interface PhotoUploaderProps {
  onPhotosUploaded: (photos: string[]) => void;
  onPanoramaCreated: (panoramaUrl: string) => void;
  onClose: () => void;
  maxPhotos?: number;
}

interface UploadedPhoto {
  id: string;
  url: string;
  name: string;
  size: number;
}

export const PhotoUploader: React.FC<PhotoUploaderProps> = ({
  onPhotosUploaded,
  onPanoramaCreated,
  onClose,
  maxPhotos = 10
}) => {
  const [uploadedPhotos, setUploadedPhotos] = useState<UploadedPhoto[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCreatingPanorama, setIsCreatingPanorama] = useState(false);
  const [panoramaProgress, setPanoramaProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    setIsProcessing(true);
    const newPhotos: UploadedPhoto[] = [];

    Array.from(files).forEach((file, index) => {
      if (file.type.startsWith('image/') && uploadedPhotos.length + newPhotos.length < maxPhotos) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const url = e.target?.result as string;
          newPhotos.push({
            id: `${Date.now()}-${index}`,
            url,
            name: file.name,
            size: file.size
          });

          // If all files are processed
          if (newPhotos.length === Math.min(files.length, maxPhotos - uploadedPhotos.length)) {
            setUploadedPhotos(prev => [...prev, ...newPhotos]);
            setIsProcessing(false);
          }
        };
        reader.readAsDataURL(file);
      }
    });
  }, [uploadedPhotos.length, maxPhotos]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  // Handle file input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  }, [handleFileSelect]);

  // Remove photo
  const removePhoto = useCallback((id: string) => {
    setUploadedPhotos(prev => prev.filter(photo => photo.id !== id));
  }, []);

  // Create panorama from photos
  const createPanorama = useCallback(async () => {
    if (uploadedPhotos.length === 0) return;

    setIsCreatingPanorama(true);
    setPanoramaProgress(0);

    try {
      // Validate images for stitching
      const photoUrls = uploadedPhotos.map(photo => photo.url);
      const validation = validateImagesForStitching(photoUrls);

      if (!validation.valid) {
        throw new Error('Images are not suitable for panorama creation');
      }

      setPanoramaProgress(25);

      // Stitch images into panorama
      const result = await stitchImagesToPanorama(photoUrls, {
        outputWidth: 4096,
        outputHeight: 2048
      });

      setPanoramaProgress(75);

      if (!result.success) {
        throw new Error(result.error || 'Failed to create panorama');
      }

      setPanoramaProgress(100);

      // Return both individual photos and the panorama
      onPhotosUploaded(photoUrls);
      onPanoramaCreated(result.panoramaUrl);

      setTimeout(() => {
        onClose();
      }, 500);

    } catch (error) {
      console.error('Panorama creation failed:', error);
      alert('Failed to create panorama: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsCreatingPanorama(false);
      setPanoramaProgress(0);
    }
  }, [uploadedPhotos, onPhotosUploaded, onPanoramaCreated, onClose]);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Upload Photos</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 max-h-[70vh] overflow-y-auto">
          {/* Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragging 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="flex flex-col items-center">
              <Upload size={48} className="text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Upload Property Photos
              </h3>
              <p className="text-gray-600 mb-4">
                Drag and drop photos here, or click to select files
              </p>
              <button
                onClick={() => fileInputRef.current?.click()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center"
              >
                <Camera size={20} className="mr-2" />
                Select Photos
              </button>
              <p className="text-sm text-gray-500 mt-2">
                Maximum {maxPhotos} photos • JPG, PNG, WebP
              </p>
            </div>
          </div>

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            onChange={handleInputChange}
            className="hidden"
          />

          {/* Processing indicator */}
          {isProcessing && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <RotateCw size={20} className="text-blue-600 animate-spin mr-2" />
                <span className="text-blue-800">Processing photos...</span>
              </div>
            </div>
          )}

          {/* Panorama creation progress */}
          {isCreatingPanorama && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center mb-2">
                <Eye size={20} className="text-green-600 mr-2" />
                <span className="text-green-800 font-medium">Creating 360° Panorama...</span>
              </div>
              <div className="w-full bg-green-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${panoramaProgress}%` }}
                />
              </div>
              <p className="text-sm text-green-700 mt-1">{panoramaProgress}% complete</p>
            </div>
          )}

          {/* Uploaded Photos */}
          {uploadedPhotos.length > 0 && (
            <div className="mt-6">
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Uploaded Photos ({uploadedPhotos.length}/{maxPhotos})
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {uploadedPhotos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={photo.url}
                        alt={photo.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    {/* Photo overlay */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            const link = document.createElement('a');
                            link.download = photo.name;
                            link.href = photo.url;
                            link.click();
                          }}
                          className="p-2 bg-white/20 rounded-full hover:bg-white/30 text-white"
                          title="Download"
                        >
                          <Download size={16} />
                        </button>
                        <button
                          onClick={() => removePhoto(photo.id)}
                          className="p-2 bg-red-500/80 rounded-full hover:bg-red-500 text-white"
                          title="Remove"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>

                    {/* Photo info */}
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {photo.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(photo.size)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h5 className="font-medium text-gray-900 mb-2">Tips for Best Results:</h5>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Take photos from multiple angles around the room</li>
              <li>• Ensure good lighting and avoid shadows</li>
              <li>• Keep the camera level and steady</li>
              <li>• Overlap photos by 30-50% for better stitching</li>
              <li>• Use landscape orientation for wider coverage</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {uploadedPhotos.length} of {maxPhotos} photos uploaded
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 hover:text-gray-900"
            >
              Cancel
            </button>
            <button
              onClick={createPanorama}
              disabled={uploadedPhotos.length === 0 || isCreatingPanorama}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg flex items-center"
            >
              {isCreatingPanorama ? (
                <>
                  <RotateCw size={20} className="mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Eye size={20} className="mr-2" />
                  Create 360° Panorama
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
