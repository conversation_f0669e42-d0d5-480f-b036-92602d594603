'use client';

import React, { useState } from 'react';
import { Eye, Download, Filter, Grid, List } from 'lucide-react';
import { RealPanoramaImage, getAllDemoPanoramas, getPanoramasByCategory, createRealEstateCollection } from '../Demo/RealPanoramaData';

interface PresetPanoramaSelectorProps {
  onImageSelect: (image: RealPanoramaImage) => void;
  onClose: () => void;
}

export default function PresetPanoramaSelector({ onImageSelect, onClose }: PresetPanoramaSelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showRealEstate, setShowRealEstate] = useState(false);

  const categories = [
    { id: 'all', name: '全部', icon: '🌐' },
    { id: 'interior', name: '室内', icon: '🏠' },
    { id: 'exterior', name: '户外', icon: '🌳' },
    { id: 'nature', name: '自然', icon: '🏔️' },
    { id: 'commercial', name: '商业', icon: '🏢' }
  ];

  const images = showRealEstate 
    ? createRealEstateCollection()
    : getPanoramasByCategory(selectedCategory);

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">选择预设全景图片</h2>
              <p className="text-blue-100">选择一张全景图片开始您的VR体验</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl font-bold"
            >
              ×
            </button>
          </div>
        </div>

        {/* 控制栏 */}
        <div className="bg-gray-50 p-4 border-b">
          <div className="flex flex-wrap items-center justify-between gap-4">
            {/* 类别选择 */}
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">类别:</span>
              <div className="flex space-x-1">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {category.icon} {category.name}
                  </button>
                ))}
              </div>
            </div>

            {/* 特殊集合 */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowRealEstate(!showRealEstate)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  showRealEstate
                    ? 'bg-green-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border'
                }`}
              >
                🏠 房地产专用
              </button>
            </div>

            {/* 视图模式 */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="p-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                {viewMode === 'grid' ? <List size={20} /> : <Grid size={20} />}
              </button>
            </div>
          </div>
        </div>

        {/* 图片网格 */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {images.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">该类别下暂无图片</div>
              <button
                onClick={() => setSelectedCategory('all')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
              >
                查看全部图片
              </button>
            </div>
          ) : (
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'
                : 'space-y-4'
            }>
              {images.map((image) => (
                <div
                  key={image.id}
                  className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-105 ${
                    viewMode === 'list' ? 'flex items-center p-4' : ''
                  }`}
                  onClick={() => onImageSelect(image)}
                >
                  <div className={viewMode === 'list' ? 'w-32 h-20 flex-shrink-0 mr-4' : 'aspect-video'}>
                    <img
                      src={image.url}
                      alt={image.name}
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                  
                  <div className={viewMode === 'list' ? 'flex-1' : 'p-4'}>
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-800 truncate">
                        {image.name}
                      </h3>
                      <span className={`inline-block w-3 h-3 rounded-full ${
                        image.category === 'interior' ? 'bg-blue-500' :
                        image.category === 'exterior' ? 'bg-green-500' :
                        image.category === 'nature' ? 'bg-emerald-500' :
                        'bg-purple-500'
                      }`} />
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {image.description}
                    </p>
                    
                    <div className="text-xs text-gray-500 space-y-1">
                      {image.location && (
                        <div>📍 {image.location}</div>
                      )}
                      <div>📐 {image.resolution}</div>
                      {image.photographer && (
                        <div>📷 {image.photographer}</div>
                      )}
                    </div>
                    
                    {viewMode === 'grid' && (
                      <div className="flex space-x-2 mt-3">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onImageSelect(image);
                          }}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-3 rounded transition-colors"
                        >
                          <Eye size={14} className="inline mr-1" />
                          VR查看
                        </button>
                        
                        <a
                          href={image.url}
                          download={`${image.name}.jpg`}
                          onClick={(e) => e.stopPropagation()}
                          className="bg-gray-600 hover:bg-gray-700 text-white text-sm py-2 px-3 rounded transition-colors"
                        >
                          <Download size={14} />
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 底部信息 */}
        <div className="bg-gray-50 p-4 border-t">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div>
              显示 {images.length} 张图片
              {showRealEstate && (
                <span className="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                  房地产专用集合
                </span>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span>图例:</span>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-xs">室内</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-xs">户外</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                  <span className="text-xs">自然</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-xs">商业</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
