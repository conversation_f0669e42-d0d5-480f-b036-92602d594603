'use client';

import React, { useState } from 'react';
import { HardDrive, Download, Upload, Trash2, Alert<PERSON>riangle, CheckCircle, X } from 'lucide-react';
import { useStorageUsage, usePersistedImages } from '@/hooks/usePersistedState';
import { exportAllData, importAllData, clearAllAppData } from '@/utils/localStorage';

interface StorageManagerProps {
  onClose: () => void;
}

export default function StorageManager({ onClose }: StorageManagerProps) {
  const { usage, updateUsage } = useStorageUsage();
  const { getImageStats, clearImages } = usePersistedImages();
  const [showImportExport, setShowImportExport] = useState(false);
  const [importData, setImportData] = useState('');
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const imageStats = getImageStats();

  // 格式化文件大小
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 导出数据
  const handleExport = () => {
    try {
      const data = exportAllData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `vr720-backup-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);
      
      setMessage({ type: 'success', text: '数据导出成功！' });
    } catch (error) {
      console.error('Export failed:', error);
      setMessage({ type: 'error', text: '导出失败，请重试。' });
    }
  };

  // 导入数据
  const handleImport = () => {
    try {
      const success = importAllData(importData);
      if (success) {
        setMessage({ type: 'success', text: '数据导入成功！页面将刷新以加载新数据。' });
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        setMessage({ type: 'error', text: '导入失败，请检查数据格式。' });
      }
    } catch (error) {
      console.error('Import failed:', error);
      setMessage({ type: 'error', text: '导入失败，数据格式不正确。' });
    }
  };

  // 清空所有数据
  const handleClearAll = () => {
    if (window.confirm('确定要清空所有应用数据吗？此操作不可恢复，包括所有上传的图片和设置。')) {
      try {
        const success = clearAllAppData();
        if (success) {
          setMessage({ type: 'success', text: '所有数据已清空！页面将刷新。' });
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          setMessage({ type: 'error', text: '清空失败，请重试。' });
        }
      } catch (error) {
        console.error('Clear all failed:', error);
        setMessage({ type: 'error', text: '清空失败，请重试。' });
      }
    }
  };

  // 清空图片
  const handleClearImages = () => {
    if (window.confirm('确定要清空所有上传的图片吗？此操作不可恢复。')) {
      const success = clearImages();
      if (success) {
        setMessage({ type: 'success', text: '图片已清空！' });
        updateUsage();
      } else {
        setMessage({ type: 'error', text: '清空图片失败，请重试。' });
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <HardDrive size={24} className="mr-3" />
              <div>
                <h2 className="text-xl font-bold">存储管理</h2>
                <p className="text-blue-100 text-sm">管理您的本地存储和数据</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl font-bold"
            >
              <X size={24} />
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[70vh]">
          {/* 消息提示 */}
          {message && (
            <div className={`mb-6 p-4 rounded-lg border ${
              message.type === 'success' 
                ? 'bg-green-50 border-green-200 text-green-800' 
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center">
                {message.type === 'success' ? (
                  <CheckCircle size={20} className="mr-2" />
                ) : (
                  <AlertTriangle size={20} className="mr-2" />
                )}
                {message.text}
              </div>
            </div>
          )}

          {/* 存储使用情况 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">存储使用情况</h3>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">已使用空间</span>
                <span className="text-sm font-medium text-gray-800">
                  {formatBytes(usage.used)} / {formatBytes(usage.total)}
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    usage.percentage > 80 ? 'bg-red-500' :
                    usage.percentage > 60 ? 'bg-yellow-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${Math.min(usage.percentage, 100)}%` }}
                />
              </div>
              
              <div className="text-xs text-gray-500">
                使用率: {usage.percentage.toFixed(1)}%
                {usage.percentage > 80 && (
                  <span className="text-red-600 ml-2">⚠️ 存储空间不足</span>
                )}
              </div>
            </div>
          </div>

          {/* 数据统计 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">数据统计</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-blue-600">{imageStats.total}</div>
                <div className="text-sm text-blue-800">总图片数</div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-green-600">{imageStats.panoramic}</div>
                <div className="text-sm text-green-800">全景图片</div>
              </div>
              
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-purple-600">{imageStats.regular}</div>
                <div className="text-sm text-purple-800">普通图片</div>
              </div>
              
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="text-2xl font-bold text-orange-600">
                  {formatBytes(imageStats.totalSize)}
                </div>
                <div className="text-sm text-orange-800">图片总大小</div>
              </div>
            </div>
          </div>

          {/* 数据管理操作 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">数据管理</h3>
            
            <div className="space-y-3">
              <button
                onClick={() => setShowImportExport(!showImportExport)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <Upload size={20} className="mr-2" />
                数据备份与恢复
              </button>
              
              <button
                onClick={handleClearImages}
                className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
                disabled={imageStats.total === 0}
              >
                <Trash2 size={20} className="mr-2" />
                清空所有图片
              </button>
              
              <button
                onClick={handleClearAll}
                className="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg transition-colors flex items-center justify-center"
              >
                <AlertTriangle size={20} className="mr-2" />
                清空所有数据
              </button>
            </div>
          </div>

          {/* 导入导出面板 */}
          {showImportExport && (
            <div className="border-t pt-6">
              <h4 className="text-md font-semibold text-gray-800 mb-4">数据备份与恢复</h4>
              
              <div className="space-y-4">
                {/* 导出 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    导出数据
                  </label>
                  <button
                    onClick={handleExport}
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                  >
                    <Download size={16} className="mr-2" />
                    下载备份文件
                  </button>
                  <p className="text-xs text-gray-500 mt-1">
                    将所有数据导出为JSON文件，可用于备份或迁移
                  </p>
                </div>
                
                {/* 导入 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    导入数据
                  </label>
                  <textarea
                    value={importData}
                    onChange={(e) => setImportData(e.target.value)}
                    placeholder="粘贴备份文件的JSON内容..."
                    className="w-full h-32 p-3 border border-gray-300 rounded-lg text-sm"
                  />
                  <button
                    onClick={handleImport}
                    disabled={!importData.trim()}
                    className="w-full mt-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                  >
                    <Upload size={16} className="mr-2" />
                    导入数据
                  </button>
                  <p className="text-xs text-gray-500 mt-1">
                    ⚠️ 导入将覆盖现有数据，请确保已备份当前数据
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
