'use client';

import React, { useEffect, useRef, useState } from 'react';

interface AFrameViewerProps {
  panoramaUrl: string;
  onBack: () => void;
  onReset: () => void;
  hotspots?: Array<{
    id: string;
    position: { x: number; y: number; z: number };
    label: string;
    onClick: () => void;
  }>;
  roomInfo?: {
    name: string;
    type: string;
    description?: string;
  };
}

export default function AFrameViewer({
  panoramaUrl,
  onBack,
  onReset,
  hotspots = [],
  roomInfo
}: AFrameViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (typeof window !== 'undefined' && panoramaUrl && containerRef.current) {
      setIsLoading(true);
      // 创建A-Frame场景
      const sceneHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
          <style>
            body { 
              margin: 0; 
              overflow: hidden; 
              background: #000;
            }
            #vr-scene { 
              width: 100vw; 
              height: 100vh; 
            }
            .controls {
              position: fixed;
              top: 20px;
              left: 20px;
              z-index: 1000;
              display: flex;
              gap: 10px;
            }
            .control-btn {
              background: rgba(0,0,0,0.7);
              color: white;
              border: none;
              padding: 10px;
              border-radius: 50%;
              cursor: pointer;
              backdrop-filter: blur(10px);
            }
            .control-btn:hover {
              background: rgba(0,0,0,0.9);
            }
            .info {
              position: fixed;
              bottom: 20px;
              left: 50%;
              transform: translateX(-50%);
              background: rgba(0,0,0,0.7);
              color: white;
              padding: 15px 25px;
              border-radius: 25px;
              text-align: center;
              backdrop-filter: blur(10px);
              z-index: 1000;
            }
          </style>
        </head>
        <body>
          <div class="controls">
            <button class="control-btn" onclick="window.parent.postMessage('back', '*')" title="返回">
              ←
            </button>
            <button class="control-btn" onclick="window.parent.postMessage('reset', '*')" title="重新开始">
              ↻
            </button>
            <button class="control-btn" onclick="toggleFullscreen()" title="全屏">
              ⛶
            </button>
          </div>
          
          <a-scene 
            id="vr-scene" 
            embedded 
            style="height: 100vh; width: 100vw;"
            vr-mode-ui="enabled: true"
            background="color: #000"
          >
            <a-assets>
              <img id="panorama" src="${panoramaUrl}" crossorigin="anonymous">
            </a-assets>
            
            <a-sky 
              src="#panorama" 
              rotation="0 -130 0"
              animation="property: rotation; to: 0 230 0; loop: true; dur: 60000; easing: linear"
            ></a-sky>
            
            <a-camera 
              look-controls="enabled: true; reverseMouseDrag: false"
              wasd-controls="enabled: false"
              position="0 1.6 0"
            >
              <a-cursor 
                color="white" 
                opacity="0.5"
                raycaster="objects: .clickable"
                geometry="primitive: ring; radiusInner: 0.02; radiusOuter: 0.03"
                material="color: white; shader: flat"
              ></a-cursor>
            </a-camera>
            
            <!-- 环境光 -->
            <a-light type="ambient" color="#404040"></a-light>
            
            <!-- 房间信息显示 -->
            ${roomInfo ? `
            <a-text
              value="${roomInfo.name}"
              position="0 3.5 -4"
              align="center"
              color="white"
              opacity="0.9"
              font="size: 24"
            ></a-text>
            <a-text
              value="${roomInfo.description || roomInfo.type}"
              position="0 3 -4"
              align="center"
              color="#cccccc"
              opacity="0.7"
              font="size: 16"
            ></a-text>
            ` : `
            <a-text
              value="VR720 全景查看器"
              position="0 3 -4"
              align="center"
              color="white"
              opacity="0.8"
            ></a-text>
            `}

            <!-- 导航热点 -->
            ${hotspots.map((hotspot, index) => `
            <a-sphere
              id="hotspot-${hotspot.id}"
              position="${hotspot.position.x} ${hotspot.position.y} ${hotspot.position.z}"
              radius="0.2"
              color="#FFD700"
              opacity="0.8"
              class="clickable hotspot"
              animation="property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 1000"
              data-hotspot-id="${hotspot.id}"
            >
              <a-text
                value="${hotspot.label}"
                position="0 0.5 0"
                align="center"
                color="white"
                background="color: rgba(0,0,0,0.7); padding: 0.1 0.2"
                font="size: 12"
              ></a-text>
            </a-sphere>
            `).join('')}
          </a-scene>
          
          <div class="info">
            <div style="font-size: 14px; margin-bottom: 5px;">VR720 全景模式</div>
            <div style="font-size: 12px; opacity: 0.8;">拖拽查看360度全景 • 点击VR图标进入VR模式</div>
          </div>

          <script>
            function toggleFullscreen() {
              if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
              } else {
                document.exitFullscreen();
              }
            }
            
            // 监听父窗口消息
            window.addEventListener('message', function(event) {
              if (event.data === 'back') {
                window.parent.postMessage('back', '*');
              } else if (event.data === 'reset') {
                window.parent.postMessage('reset', '*');
              }
            });

            // 热点点击事件
            document.addEventListener('click', function(event) {
              const hotspot = event.target.closest('.hotspot');
              if (hotspot) {
                const hotspotId = hotspot.getAttribute('data-hotspot-id');
                window.parent.postMessage({type: 'hotspot-click', hotspotId: hotspotId}, '*');
              }
            });
            
            // 自动旋转控制
            let autoRotate = true;
            document.addEventListener('click', function() {
              const sky = document.querySelector('a-sky');
              if (autoRotate) {
                sky.removeAttribute('animation');
                autoRotate = false;
              }
            });
            
            // 键盘控制
            document.addEventListener('keydown', function(event) {
              const camera = document.querySelector('a-camera');
              const currentRotation = camera.getAttribute('rotation');
              
              switch(event.key) {
                case 'ArrowLeft':
                  camera.setAttribute('rotation', {
                    x: currentRotation.x,
                    y: currentRotation.y - 5,
                    z: currentRotation.z
                  });
                  break;
                case 'ArrowRight':
                  camera.setAttribute('rotation', {
                    x: currentRotation.x,
                    y: currentRotation.y + 5,
                    z: currentRotation.z
                  });
                  break;
                case 'ArrowUp':
                  camera.setAttribute('rotation', {
                    x: Math.max(currentRotation.x - 5, -90),
                    y: currentRotation.y,
                    z: currentRotation.z
                  });
                  break;
                case 'ArrowDown':
                  camera.setAttribute('rotation', {
                    x: Math.min(currentRotation.x + 5, 90),
                    y: currentRotation.y,
                    z: currentRotation.z
                  });
                  break;
                case 'r':
                  // 重置视角
                  camera.setAttribute('rotation', '0 0 0');
                  break;
              }
            });
          </script>
        </body>
        </html>
      `;

      // 创建iframe
      const iframe = document.createElement('iframe');
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
      iframe.srcdoc = sceneHTML;

      // 监听iframe加载完成
      iframe.onload = () => {
        setTimeout(() => {
          setIsLoading(false);
        }, 2000); // 给A-Frame一些时间来初始化
      };
      
      // 监听iframe消息
      const handleMessage = (event: MessageEvent) => {
        if (event.data === 'back') {
          onBack();
        } else if (event.data === 'reset') {
          onReset();
        } else if (typeof event.data === 'object' && event.data.type === 'hotspot-click') {
          const hotspot = hotspots.find(h => h.id === event.data.hotspotId);
          if (hotspot) {
            hotspot.onClick();
          }
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      containerRef.current.appendChild(iframe);
      
      return () => {
        window.removeEventListener('message', handleMessage);
        if (containerRef.current && iframe) {
          containerRef.current.removeChild(iframe);
        }
      };
    }
  }, [panoramaUrl, onBack, onReset]);

  return (
    <div className="w-full h-screen bg-black relative overflow-hidden">
      <div ref={containerRef} className="w-full h-full" />

      {/* 加载指示器 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/90 z-50">
          <div className="text-white text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-lg font-semibold">正在加载VR全景...</p>
            <p className="text-sm text-gray-400 mt-2">请稍候，正在初始化VR环境</p>
            <div className="mt-4 flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
