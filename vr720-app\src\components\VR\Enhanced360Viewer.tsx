'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  ArrowLeft, 
  Share2, 
  Download, 
  RotateCcw, 
  RotateCw, 
  ZoomIn, 
  ZoomOut,
  Play,
  Pause,
  Eye,
  Info,
  Move,
  Maximize
} from 'lucide-react';

interface Enhanced360ViewerProps {
  panoramaUrl: string;
  title?: string;
  description?: string;
  onClose: () => void;
}

export const Enhanced360Viewer: React.FC<Enhanced360ViewerProps> = ({
  panoramaUrl,
  title = "360° VR Tour",
  description,
  onClose
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [showInfo, setShowInfo] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-hide controls after 3 seconds
  useEffect(() => {
    if (showControls && !isDragging) {
      const timer = setTimeout(() => {
        setShowControls(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showControls, isDragging]);

  // Auto rotation
  useEffect(() => {
    if (isPlaying && !isDragging) {
      const interval = setInterval(() => {
        setRotation(prev => (prev + 0.5) % 360);
      }, 50);
      return () => clearInterval(interval);
    }
  }, [isPlaying, isDragging]);

  // Show controls on interaction
  const handleInteraction = useCallback(() => {
    setShowControls(true);
  }, []);

  // Handle mouse/touch drag for 360° rotation
  const handleDragStart = useCallback((clientX: number, clientY: number) => {
    setIsDragging(true);
    setDragStart({ x: clientX, y: clientY });
    setIsPlaying(false); // Stop auto-play when dragging
    handleInteraction();
  }, [handleInteraction]);

  const handleDragMove = useCallback((clientX: number, clientY: number) => {
    if (!isDragging) return;
    
    const deltaX = clientX - dragStart.x;
    const sensitivity = 0.5;
    const newRotation = rotation + (deltaX * sensitivity);
    
    setRotation(newRotation);
    setDragStart({ x: clientX, y: dragStart.y });
  }, [isDragging, dragStart, rotation]);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Mouse events
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    handleDragStart(e.clientX, e.clientY);
  }, [handleDragStart]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    handleDragMove(e.clientX, e.clientY);
  }, [handleDragMove]);

  const handleMouseUp = useCallback(() => {
    handleDragEnd();
  }, [handleDragEnd]);

  // Touch events
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    handleDragStart(touch.clientX, touch.clientY);
  }, [handleDragStart]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    handleDragMove(touch.clientX, touch.clientY);
  }, [handleDragMove]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    handleDragEnd();
  }, [handleDragEnd]);

  // Handle rotation buttons
  const handleRotateLeft = useCallback(() => {
    setRotation(prev => prev - 15);
    handleInteraction();
  }, [handleInteraction]);

  const handleRotateRight = useCallback(() => {
    setRotation(prev => prev + 15);
    handleInteraction();
  }, [handleInteraction]);

  // Handle zoom
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 0.2, 3));
    handleInteraction();
  }, [handleInteraction]);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 0.2, 0.5));
    handleInteraction();
  }, [handleInteraction]);

  // Toggle auto-play
  const togglePlay = useCallback(() => {
    setIsPlaying(prev => !prev);
    handleInteraction();
  }, [handleInteraction]);

  // Download image
  const handleDownload = useCallback(() => {
    const link = document.createElement('a');
    link.download = `360-tour-${Date.now()}.jpg`;
    link.href = panoramaUrl;
    link.click();
  }, [panoramaUrl]);

  // Share functionality
  const handleShare = useCallback(async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: description ?? 'Check out this 360° VR tour!',
          url: window.location.href
        });
      } catch (error) {
        console.log('Share failed:', error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      } catch (error) {
        console.log('Copy failed:', error);
      }
    }
  }, [title, description]);

  // Handle image load
  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  return (
    <div className="fixed inset-0 bg-black z-50" ref={containerRef}>
      {/* Header Controls */}
      <div 
        className={`absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/80 to-transparent p-4 transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <div className="flex items-center justify-between text-white">
          <button
            onClick={onClose}
            className="flex items-center space-x-2 hover:text-gray-300 transition-colors px-3 py-2 rounded"
          >
            <ArrowLeft size={24} />
            <span>Back</span>
          </button>
          
          <div className="text-center">
            <h2 className="text-lg font-medium">{title}</h2>
            {description && (
              <p className="text-sm text-gray-300">{description}</p>
            )}
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => setShowInfo(!showInfo)}
              className="p-2 hover:text-gray-300 transition-colors rounded"
            >
              <Info size={20} />
            </button>
            <button
              onClick={handleShare}
              className="p-2 hover:text-gray-300 transition-colors rounded"
            >
              <Share2 size={20} />
            </button>
            <button
              onClick={handleDownload}
              className="p-2 hover:text-gray-300 transition-colors rounded"
            >
              <Download size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* 360° Panorama Content */}
      <div 
        className={`w-full h-full relative overflow-hidden ${
          isDragging ? 'cursor-grabbing' : 'cursor-grab'
        }`}
        onClick={handleInteraction}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* 360° Panorama Image */}
        <div 
          className="w-full h-full relative"
          style={{
            transform: `rotate(${rotation}deg) scale(${zoom})`,
            transformOrigin: 'center center',
            transition: isDragging ? 'none' : 'transform 0.1s ease-out'
          }}
        >
          <img
            ref={imageRef}
            src={panoramaUrl}
            alt="360° Panorama"
            className="w-full h-full object-cover"
            onLoad={handleImageLoad}
            draggable={false}
            style={{
              // Create panoramic effect by repeating the image
              backgroundImage: `url(${panoramaUrl})`,
              backgroundRepeat: 'repeat-x',
              backgroundSize: 'auto 100%'
            }}
          />
          
          {/* Panoramic overlay for seamless 360° effect */}
          <div 
            className="absolute inset-0 opacity-30"
            style={{
              backgroundImage: `url(${panoramaUrl})`,
              backgroundRepeat: 'repeat-x',
              backgroundSize: 'auto 100%',
              transform: 'translateX(100%)'
            }}
          />
        </div>
        
        {/* Loading overlay */}
        {!imageLoaded && (
          <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
            <div className="text-white text-center">
              <Eye size={48} className="mx-auto mb-4 animate-pulse" />
              <p className="text-lg">Loading 360° Tour...</p>
              <p className="text-sm text-gray-300 mt-2">Please wait...</p>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Controls */}
      <div 
        className={`absolute bottom-0 left-0 right-0 z-20 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0'
        }`}
      >
        {/* Main Controls */}
        <div className="flex items-center justify-center space-x-6 text-white mb-4">
          <button
            onClick={handleRotateLeft}
            className="p-3 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
            title="Rotate Left"
          >
            <RotateCcw size={20} />
          </button>
          
          <button
            onClick={togglePlay}
            className="p-4 bg-blue-600 rounded-full hover:bg-blue-700 transition-colors"
            title={isPlaying ? "Pause Auto-Rotation" : "Start Auto-Rotation"}
          >
            {isPlaying ? <Pause size={24} /> : <Play size={24} />}
          </button>
          
          <button
            onClick={handleRotateRight}
            className="p-3 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
            title="Rotate Right"
          >
            <RotateCw size={20} />
          </button>
        </div>

        {/* Secondary Controls */}
        <div className="flex items-center justify-center space-x-4 text-white mb-2">
          <button
            onClick={handleZoomOut}
            className="p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
            title="Zoom Out"
          >
            <ZoomOut size={16} />
          </button>
          
          <div className="flex items-center space-x-2 text-sm bg-black/30 px-3 py-1 rounded-full">
            <span>Zoom: {Math.round(zoom * 100)}%</span>
          </div>
          
          <button
            onClick={handleZoomIn}
            className="p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
            title="Zoom In"
          >
            <ZoomIn size={16} />
          </button>
        </div>

        {/* Instructions */}
        <div className="text-center text-white/70 text-sm">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-1">
              <Move size={14} />
              <span>Drag to rotate</span>
            </div>
            <div className="flex items-center space-x-1">
              <Maximize size={14} />
              <span>Pinch to zoom</span>
            </div>
            <div className="flex items-center space-x-1">
              <Eye size={14} />
              <span>Tap to show controls</span>
            </div>
          </div>
        </div>
      </div>

      {/* Info Panel */}
      {showInfo && (
        <div className="absolute top-20 right-4 bg-black/90 text-white p-4 rounded-lg max-w-xs z-30">
          <h3 className="font-medium mb-2">360° VR Tour</h3>
          <div className="text-sm space-y-1">
            <p><strong>Title:</strong> {title}</p>
            {description && <p><strong>Description:</strong> {description}</p>}
            <p><strong>Controls:</strong></p>
            <ul className="text-xs text-gray-300 ml-2 space-y-1">
              <li>• Drag horizontally to rotate 360°</li>
              <li>• Use rotation buttons for precise control</li>
              <li>• Play button for auto-rotation</li>
              <li>• Zoom in/out for detail viewing</li>
              <li>• Share or download the tour</li>
              <li>• Tap anywhere to show/hide controls</li>
            </ul>
          </div>
          <button
            onClick={() => setShowInfo(false)}
            className="mt-3 text-blue-400 text-sm hover:text-blue-300"
          >
            Close
          </button>
        </div>
      )}

      {/* Status indicator */}
      <div className="absolute top-1/2 left-4 transform -translate-y-1/2 text-white/50 text-xs pointer-events-none">
        <div className="bg-black/30 px-2 py-1 rounded">
          {Math.round(rotation % 360)}°
        </div>
      </div>
    </div>
  );
};
