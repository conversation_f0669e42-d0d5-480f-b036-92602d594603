'use client';

import React, { useEffect, useRef, useState } from 'react';
import { ArrowLeft, Navigation, MapPin, Play, Pause, SkipForward, Home, Info } from 'lucide-react';

interface VRScene {
  id: string;
  name: string;
  panoramaUrl: string;
  description: string;
  hotspots: VRHotspot[];
  position: { x: number; y: number };
}

interface VRHotspot {
  id: string;
  type: 'navigation' | 'info' | 'room';
  position: { x: number; y: number; z: number };
  targetSceneId?: string;
  title: string;
  description: string;
  icon: string;
}

interface GuidedVRViewerProps {
  scenes: VRScene[];
  initialSceneId?: string;
  title?: string;
  onClose: () => void;
}

// Sample VR tour data
export const createSampleVRTour = (): VRScene[] => [
  {
    id: 'entrance',
    name: 'Main Entrance',
    panoramaUrl: '/panoramas/living-room-modern-1.jpg',
    description: 'Welcome to this beautiful modern home. Start your guided tour here.',
    position: { x: 0, y: 0 },
    hotspots: [
      {
        id: 'to-living',
        type: 'navigation',
        position: { x: 2, y: 0, z: -3 },
        targetSceneId: 'living-room',
        title: 'Living Room',
        description: 'Enter the spacious living area',
        icon: '🛋️'
      },
      {
        id: 'entrance-info',
        type: 'info',
        position: { x: -2, y: 1, z: -2 },
        title: 'Entrance Features',
        description: 'High ceilings and natural lighting',
        icon: 'ℹ️'
      }
    ]
  },
  {
    id: 'living-room',
    name: 'Living Room',
    panoramaUrl: '/panoramas/living-room-luxury-2.jpg',
    description: 'Spacious living area with modern furnishings and panoramic windows.',
    position: { x: 1, y: 0 },
    hotspots: [
      {
        id: 'to-kitchen',
        type: 'navigation',
        position: { x: 3, y: 0, z: -2 },
        targetSceneId: 'kitchen',
        title: 'Kitchen',
        description: 'Modern kitchen with island',
        icon: '🍳'
      },
      {
        id: 'to-bedroom',
        type: 'navigation',
        position: { x: -3, y: 0, z: -1 },
        targetSceneId: 'bedroom',
        title: 'Master Bedroom',
        description: 'Luxurious master suite',
        icon: '🛏️'
      },
      {
        id: 'living-features',
        type: 'info',
        position: { x: 0, y: 1.5, z: -4 },
        title: 'Living Room Features',
        description: 'Smart home integration, premium sound system',
        icon: '🏠'
      }
    ]
  },
  {
    id: 'kitchen',
    name: 'Modern Kitchen',
    panoramaUrl: '/panoramas/kitchen-modern-1.jpg',
    description: 'State-of-the-art kitchen with premium appliances and granite countertops.',
    position: { x: 2, y: 0 },
    hotspots: [
      {
        id: 'to-living-from-kitchen',
        type: 'navigation',
        position: { x: -3, y: 0, z: -2 },
        targetSceneId: 'living-room',
        title: 'Back to Living Room',
        description: 'Return to living area',
        icon: '↩️'
      },
      {
        id: 'kitchen-appliances',
        type: 'info',
        position: { x: 2, y: 0.5, z: -2 },
        title: 'Premium Appliances',
        description: 'Stainless steel appliances, induction cooktop',
        icon: '⚡'
      }
    ]
  },
  {
    id: 'bedroom',
    name: 'Master Bedroom',
    panoramaUrl: '/panoramas/bedroom-master-1.jpg',
    description: 'Elegant master bedroom with walk-in closet and en-suite bathroom.',
    position: { x: 0, y: 1 },
    hotspots: [
      {
        id: 'to-bathroom',
        type: 'navigation',
        position: { x: 2, y: 0, z: -3 },
        targetSceneId: 'bathroom',
        title: 'En-suite Bathroom',
        description: 'Luxury bathroom with spa features',
        icon: '🛁'
      },
      {
        id: 'to-living-from-bedroom',
        type: 'navigation',
        position: { x: -3, y: 0, z: -1 },
        targetSceneId: 'living-room',
        title: 'Back to Living Room',
        description: 'Return to main area',
        icon: '↩️'
      },
      {
        id: 'bedroom-features',
        type: 'info',
        position: { x: 0, y: 1, z: -4 },
        title: 'Bedroom Features',
        description: 'Walk-in closet, blackout curtains, climate control',
        icon: '🌙'
      }
    ]
  },
  {
    id: 'bathroom',
    name: 'Master Bathroom',
    panoramaUrl: '/panoramas/bathroom-master-1.jpg',
    description: 'Spa-like bathroom with premium fixtures and natural stone finishes.',
    position: { x: 1, y: 1 },
    hotspots: [
      {
        id: 'to-bedroom-from-bathroom',
        type: 'navigation',
        position: { x: -2, y: 0, z: -3 },
        targetSceneId: 'bedroom',
        title: 'Back to Bedroom',
        description: 'Return to master bedroom',
        icon: '↩️'
      },
      {
        id: 'bathroom-features',
        type: 'info',
        position: { x: 1, y: 1, z: -2 },
        title: 'Spa Features',
        description: 'Rainfall shower, heated floors, double vanity',
        icon: '💎'
      }
    ]
  }
];

export const GuidedVRViewer: React.FC<GuidedVRViewerProps> = ({
  scenes,
  initialSceneId,
  title = "Guided VR Property Tour",
  onClose
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentSceneId, setCurrentSceneId] = useState(initialSceneId || scenes[0]?.id);
  const [showMiniMap, setShowMiniMap] = useState(false);
  const [showTourGuide, setShowTourGuide] = useState(true);
  const [isAutoTour, setIsAutoTour] = useState(false);
  const [tourProgress, setTourProgress] = useState(0);

  const currentScene = scenes.find(scene => scene.id === currentSceneId);

  // Auto tour functionality
  useEffect(() => {
    if (isAutoTour && scenes.length > 1) {
      const currentIndex = scenes.findIndex(scene => scene.id === currentSceneId);
      const nextIndex = (currentIndex + 1) % scenes.length;
      
      const timer = setTimeout(() => {
        setCurrentSceneId(scenes[nextIndex].id);
        setTourProgress(((nextIndex + 1) / scenes.length) * 100);
      }, 8000); // 8 seconds per scene

      return () => clearTimeout(timer);
    }
  }, [isAutoTour, currentSceneId, scenes]);

  // A-Frame VR Scene with guided tour features
  useEffect(() => {
    if (typeof window !== 'undefined' && currentScene && containerRef.current) {
      setIsLoading(true);
      
      const sceneHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
          <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
          <style>
            body { 
              margin: 0; 
              overflow: hidden; 
              background: #000;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            #vr-scene { 
              width: 100vw; 
              height: 100vh; 
            }
            .tour-controls {
              position: fixed;
              top: 20px;
              left: 20px;
              right: 20px;
              z-index: 1000;
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
            }
            .control-panel {
              background: rgba(0,0,0,0.8);
              backdrop-filter: blur(10px);
              border-radius: 12px;
              padding: 12px;
              display: flex;
              gap: 8px;
            }
            .control-btn {
              background: rgba(255,255,255,0.1);
              border: 1px solid rgba(255,255,255,0.2);
              color: white;
              padding: 8px;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.2s;
              font-size: 14px;
              min-width: 36px;
              height: 36px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .control-btn:hover {
              background: rgba(255,255,255,0.2);
              transform: scale(1.05);
            }
            .control-btn.active {
              background: #4f46e5;
              border-color: #4f46e5;
            }
            .scene-info {
              position: fixed;
              bottom: 20px;
              left: 20px;
              right: 20px;
              background: rgba(0,0,0,0.9);
              backdrop-filter: blur(10px);
              border-radius: 12px;
              padding: 16px;
              color: white;
              z-index: 1000;
              transform: translateY(100%);
              transition: transform 0.3s ease;
            }
            .scene-info.visible {
              transform: translateY(0);
            }
            .hotspot-indicator {
              position: absolute;
              width: 40px;
              height: 40px;
              background: rgba(79, 70, 229, 0.9);
              border: 2px solid white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              animation: pulse 2s infinite;
              font-size: 18px;
              color: white;
              z-index: 500;
            }
            @keyframes pulse {
              0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7); }
              70% { transform: scale(1.1); box-shadow: 0 0 0 10px rgba(79, 70, 229, 0); }
              100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(79, 70, 229, 0); }
            }
            .minimap {
              position: fixed;
              top: 80px;
              right: 20px;
              width: 200px;
              height: 150px;
              background: rgba(0,0,0,0.8);
              border-radius: 8px;
              border: 1px solid rgba(255,255,255,0.2);
              z-index: 1000;
              padding: 10px;
              color: white;
              font-size: 12px;
            }
            .tour-progress {
              position: fixed;
              top: 70px;
              left: 20px;
              right: 20px;
              height: 4px;
              background: rgba(255,255,255,0.2);
              border-radius: 2px;
              z-index: 1000;
            }
            .progress-bar {
              height: 100%;
              background: linear-gradient(90deg, #4f46e5, #06b6d4);
              border-radius: 2px;
              transition: width 0.3s ease;
            }
          </style>
        </head>
        <body>
          <!-- Tour Controls -->
          <div class="tour-controls">
            <div class="control-panel">
              <button class="control-btn" onclick="window.parent.postMessage('back', '*')" title="Back">
                ←
              </button>
              <button class="control-btn" onclick="toggleAutoTour()" id="auto-tour-btn" title="Auto Tour">
                ▶️
              </button>
              <button class="control-btn" onclick="toggleMiniMap()" title="Mini Map">
                🗺️
              </button>
              <button class="control-btn" onclick="toggleInfo()" title="Scene Info">
                ℹ️
              </button>
            </div>
            <div class="control-panel">
              <button class="control-btn" onclick="previousScene()" title="Previous Scene">
                ⏮️
              </button>
              <button class="control-btn" onclick="nextScene()" title="Next Scene">
                ⏭️
              </button>
            </div>
          </div>

          <!-- Tour Progress -->
          <div class="tour-progress">
            <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
          </div>

          <!-- Mini Map -->
          <div class="minimap" id="minimap" style="display: none;">
            <div style="font-weight: bold; margin-bottom: 8px;">Property Map</div>
            <div style="position: relative; width: 100%; height: 100px; background: rgba(255,255,255,0.1); border-radius: 4px;">
              <!-- Map content will be generated -->
            </div>
          </div>

          <!-- A-Frame VR Scene -->
          <a-scene 
            id="vr-scene" 
            embedded 
            vr-mode-ui="enabled: true"
            background="color: #000"
          >
            <a-assets>
              <img id="panorama" src="${currentScene.panoramaUrl}" crossorigin="anonymous">
            </a-assets>
            
            <!-- 360° Sky -->
            <a-sky 
              src="#panorama" 
              rotation="0 -90 0"
            ></a-sky>
            
            <!-- VR Camera with look controls -->
            <a-camera 
              look-controls="enabled: true; touchEnabled: true"
              wasd-controls="enabled: false"
              position="0 1.6 0"
              fov="75"
            >
              <a-cursor 
                color="#4f46e5" 
                opacity="0.8"
                geometry="primitive: ring; radiusInner: 0.01; radiusOuter: 0.02"
                material="color: #4f46e5; shader: flat"
                animation="property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 1000"
              ></a-cursor>
            </a-camera>
            
            <!-- Hotspots -->
            ${currentScene.hotspots.map(hotspot => `
              <a-entity
                geometry="primitive: sphere; radius: 0.1"
                material="color: #4f46e5; opacity: 0.8; transparent: true"
                position="${hotspot.position.x} ${hotspot.position.y} ${hotspot.position.z}"
                animation="property: rotation; to: 0 360 0; loop: true; dur: 4000"
                class="hotspot"
                data-hotspot-id="${hotspot.id}"
                data-target-scene="${hotspot.targetSceneId || ''}"
                onclick="handleHotspotClick('${hotspot.id}', '${hotspot.targetSceneId || ''}', '${hotspot.title}')"
              >
                <a-text
                  value="${hotspot.title}"
                  position="0 0.3 0"
                  align="center"
                  color="white"
                  font="size: 16"
                  billboard="true"
                ></a-text>
              </a-entity>
            `).join('')}
            
            <!-- Scene Title -->
            <a-text
              value="${currentScene.name}"
              position="0 3 -4"
              align="center"
              color="#4f46e5"
              font="size: 32; weight: bold"
              opacity="0.9"
            ></a-text>
            
            <!-- Scene Description -->
            <a-text
              value="${currentScene.description}"
              position="0 2.5 -4"
              align="center"
              color="white"
              font="size: 18"
              opacity="0.8"
            ></a-text>
            
            <!-- Ambient lighting -->
            <a-light type="ambient" color="#404040"></a-light>
            <a-light type="directional" position="1 1 1" color="#ffffff" intensity="0.3"></a-light>
          </a-scene>

          <!-- Scene Info Panel -->
          <div class="scene-info" id="scene-info">
            <div style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">${currentScene.name}</div>
            <div style="font-size: 14px; opacity: 0.9; margin-bottom: 12px;">${currentScene.description}</div>
            <div style="font-size: 12px; opacity: 0.7;">
              Scene ${scenes.findIndex(s => s.id === currentSceneId) + 1} of ${scenes.length} • 
              ${currentScene.hotspots.length} interactive points
            </div>
          </div>

          <script>
            let isAutoTour = false;
            let showMiniMap = false;
            let showInfo = true;

            function toggleAutoTour() {
              isAutoTour = !isAutoTour;
              const btn = document.getElementById('auto-tour-btn');
              btn.textContent = isAutoTour ? '⏸️' : '▶️';
              btn.classList.toggle('active', isAutoTour);
              window.parent.postMessage({type: 'autoTour', enabled: isAutoTour}, '*');
            }

            function toggleMiniMap() {
              showMiniMap = !showMiniMap;
              document.getElementById('minimap').style.display = showMiniMap ? 'block' : 'none';
            }

            function toggleInfo() {
              showInfo = !showInfo;
              document.getElementById('scene-info').classList.toggle('visible', showInfo);
            }

            function previousScene() {
              window.parent.postMessage({type: 'navigate', direction: 'previous'}, '*');
            }

            function nextScene() {
              window.parent.postMessage({type: 'navigate', direction: 'next'}, '*');
            }

            function handleHotspotClick(hotspotId, targetSceneId, title) {
              if (targetSceneId) {
                window.parent.postMessage({type: 'navigateToScene', sceneId: targetSceneId}, '*');
              } else {
                window.parent.postMessage({type: 'hotspotInfo', hotspotId: hotspotId, title: title}, '*');
              }
            }

            // Initialize
            setTimeout(() => {
              document.getElementById('scene-info').classList.add('visible');
              setTimeout(() => {
                document.getElementById('scene-info').classList.remove('visible');
              }, 5000);
            }, 1000);

            // Listen for messages from parent
            window.addEventListener('message', function(event) {
              if (event.data.type === 'updateProgress') {
                document.getElementById('progress-bar').style.width = event.data.progress + '%';
              }
            });
          </script>
        </body>
        </html>
      `;

      // Create iframe
      const iframe = document.createElement('iframe');
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
      iframe.style.position = 'fixed';
      iframe.style.top = '0';
      iframe.style.left = '0';
      iframe.style.zIndex = '100';
      iframe.srcdoc = sceneHTML;

      // Handle iframe messages
      const handleMessage = (event: MessageEvent) => {
        if (event.data === 'back') {
          onClose();
        } else if (event.data.type === 'autoTour') {
          setIsAutoTour(event.data.enabled);
        } else if (event.data.type === 'navigate') {
          const currentIndex = scenes.findIndex(scene => scene.id === currentSceneId);
          if (event.data.direction === 'next' && currentIndex < scenes.length - 1) {
            setCurrentSceneId(scenes[currentIndex + 1].id);
          } else if (event.data.direction === 'previous' && currentIndex > 0) {
            setCurrentSceneId(scenes[currentIndex - 1].id);
          }
        } else if (event.data.type === 'navigateToScene') {
          setCurrentSceneId(event.data.sceneId);
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      iframe.onload = () => {
        setTimeout(() => setIsLoading(false), 1500);
      };
      
      containerRef.current.appendChild(iframe);
      
      return () => {
        window.removeEventListener('message', handleMessage);
        if (containerRef.current && iframe) {
          containerRef.current.removeChild(iframe);
        }
      };
    }
  }, [currentScene, scenes, currentSceneId, onClose]);

  // Update progress
  useEffect(() => {
    const currentIndex = scenes.findIndex(scene => scene.id === currentSceneId);
    const progress = ((currentIndex + 1) / scenes.length) * 100;
    setTourProgress(progress);
    
    // Send progress to iframe
    const iframe = containerRef.current?.querySelector('iframe');
    if (iframe?.contentWindow) {
      iframe.contentWindow.postMessage({type: 'updateProgress', progress}, '*');
    }
  }, [currentSceneId, scenes]);

  return (
    <div className="fixed inset-0 bg-black z-50 overflow-hidden">
      <div ref={containerRef} className="w-full h-full" />

      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black z-50">
          <div className="text-white text-center">
            <div className="relative mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-500 mx-auto"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Navigation size={24} className="text-blue-500" />
              </div>
            </div>
            <p className="text-xl font-bold bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent">
              Loading Guided VR Tour
            </p>
            <p className="text-sm text-gray-400 mt-2">Preparing immersive experience...</p>
            <div className="mt-4 text-xs text-gray-500">
              {currentScene?.name} • Scene {scenes.findIndex(s => s.id === currentSceneId) + 1} of {scenes.length}
            </div>
          </div>
        </div>
      )}

      {/* External controls overlay */}
      <div className="absolute top-4 left-4 z-50 flex items-center space-x-2">
        <button
          onClick={onClose}
          className="bg-black/80 text-white p-3 rounded-full backdrop-blur-sm hover:bg-black/90 transition-all"
        >
          <ArrowLeft size={20} />
        </button>
        <div className="bg-black/80 text-white px-4 py-2 rounded-full backdrop-blur-sm text-sm">
          {title}
        </div>
      </div>
    </div>
  );
};
