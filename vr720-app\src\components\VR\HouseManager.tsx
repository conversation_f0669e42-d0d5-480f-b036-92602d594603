'use client';

import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Home,
  MapPin,
  Eye,
  Settings,
  Upload,
  Camera,
  Navigation
} from 'lucide-react';
import { House, Room, HotSpot } from './HouseVRTour';

interface HouseManagerProps {
  onClose: () => void;
  onHouseCreated: (house: House) => void;
  existingHouse?: House;
}

const HouseManager: React.FC<HouseManagerProps> = ({ 
  onClose, 
  onHouseCreated, 
  existingHouse 
}) => {
  const [house, setHouse] = useState<House>(
    existingHouse || {
      id: Date.now().toString(),
      name: '',
      address: '',
      description: '',
      rooms: [],
      startRoomId: ''
    }
  );

  const [currentStep, setCurrentStep] = useState<'basic' | 'rooms' | 'connections'>('basic');
  const [selectedRoomId, setSelectedRoomId] = useState<string>('');
  const [isAddingHotSpot, setIsAddingHotSpot] = useState(false);
  const [hotSpotPosition, setHotSpotPosition] = useState<{ x: number; y: number }>({ x: 0.5, y: 0.5 });

  // 房间类型选项
  const roomTypes: Array<{ value: Room['type']; label: string; icon: string }> = [
    { value: 'living_room', label: '客厅', icon: '🛋️' },
    { value: 'bedroom', label: '卧室', icon: '🛏️' },
    { value: 'kitchen', label: '厨房', icon: '🍳' },
    { value: 'bathroom', label: '浴室', icon: '🚿' },
    { value: 'dining_room', label: '餐厅', icon: '🍽️' },
    { value: 'office', label: '书房', icon: '💼' },
    { value: 'other', label: '其他', icon: '🏠' }
  ];

  // 添加房间
  const addRoom = () => {
    const newRoom: Room = {
      id: Date.now().toString(),
      name: `房间 ${house.rooms.length + 1}`,
      type: 'other',
      panoramaUrl: '',
      hotSpots: [],
      description: ''
    };

    setHouse(prev => ({
      ...prev,
      rooms: [...prev.rooms, newRoom]
    }));
  };

  // 更新房间
  const updateRoom = (roomId: string, updates: Partial<Room>) => {
    setHouse(prev => ({
      ...prev,
      rooms: prev.rooms.map(room => 
        room.id === roomId ? { ...room, ...updates } : room
      )
    }));
  };

  // 删除房间
  const deleteRoom = (roomId: string) => {
    setHouse(prev => ({
      ...prev,
      rooms: prev.rooms.filter(room => room.id !== roomId),
      startRoomId: prev.startRoomId === roomId ? '' : prev.startRoomId
    }));

    // 同时删除其他房间中指向该房间的连接点
    setHouse(prev => ({
      ...prev,
      rooms: prev.rooms.map(room => ({
        ...room,
        hotSpots: room.hotSpots.filter(hotSpot => hotSpot.targetRoomId !== roomId)
      }))
    }));
  };

  // 添加连接点
  const addHotSpot = (roomId: string, targetRoomId: string) => {
    const targetRoom = house.rooms.find(room => room.id === targetRoomId);
    if (!targetRoom) return;

    const newHotSpot: HotSpot = {
      id: Date.now().toString(),
      x: hotSpotPosition.x,
      y: hotSpotPosition.y,
      targetRoomId,
      title: `前往${targetRoom.name}`,
      description: `从当前房间前往${targetRoom.name}`
    };

    updateRoom(roomId, {
      hotSpots: [...(house.rooms.find(room => room.id === roomId)?.hotSpots || []), newHotSpot]
    });

    setIsAddingHotSpot(false);
  };

  // 删除连接点
  const deleteHotSpot = (roomId: string, hotSpotId: string) => {
    const room = house.rooms.find(room => room.id === roomId);
    if (!room) return;

    updateRoom(roomId, {
      hotSpots: room.hotSpots.filter(hotSpot => hotSpot.id !== hotSpotId)
    });
  };

  // 处理全景图上传
  const handlePanoramaUpload = (roomId: string, file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const dataUrl = e.target?.result as string;
      updateRoom(roomId, { panoramaUrl: dataUrl });
    };
    reader.readAsDataURL(file);
  };

  // 保存房屋
  const saveHouse = () => {
    if (!house.name.trim()) {
      alert('请输入房屋名称');
      return;
    }

    if (house.rooms.length === 0) {
      alert('请至少添加一个房间');
      return;
    }

    const roomsWithoutPanorama = house.rooms.filter(room => !room.panoramaUrl);
    if (roomsWithoutPanorama.length > 0) {
      alert('请为所有房间上传全景图');
      return;
    }

    // 如果没有设置起始房间，默认使用第一个房间
    if (!house.startRoomId && house.rooms.length > 0) {
      setHouse(prev => ({ ...prev, startRoomId: prev.rooms[0].id }));
    }

    onHouseCreated(house);
    onClose();
  };

  const selectedRoom = house.rooms.find(room => room.id === selectedRoomId);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              {existingHouse ? '编辑' : '创建'}全屋漫游
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              设置房间和连接点，创建沉浸式VR体验
            </p>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center"
          >
            <X size={20} />
          </button>
        </div>

        {/* 步骤导航 */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setCurrentStep('basic')}
            className={`flex-1 py-3 px-4 text-sm font-medium ${
              currentStep === 'basic'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            基本信息
          </button>
          <button
            onClick={() => setCurrentStep('rooms')}
            className={`flex-1 py-3 px-4 text-sm font-medium ${
              currentStep === 'rooms'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            房间设置
          </button>
          <button
            onClick={() => setCurrentStep('connections')}
            className={`flex-1 py-3 px-4 text-sm font-medium ${
              currentStep === 'connections'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            连接点设置
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {currentStep === 'basic' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  房屋名称 *
                </label>
                <input
                  type="text"
                  value={house.name}
                  onChange={(e) => setHouse(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="例如：豪华别墅、现代公寓等"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  地址
                </label>
                <input
                  type="text"
                  value={house.address}
                  onChange={(e) => setHouse(prev => ({ ...prev, address: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="房屋地址"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  描述
                </label>
                <textarea
                  value={house.description}
                  onChange={(e) => setHouse(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="房屋描述信息"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  起始房间
                </label>
                <select
                  value={house.startRoomId}
                  onChange={(e) => setHouse(prev => ({ ...prev, startRoomId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">选择起始房间</option>
                  {house.rooms.map(room => (
                    <option key={room.id} value={room.id}>
                      {room.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}

          {currentStep === 'rooms' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">房间管理</h3>
                <button
                  onClick={addRoom}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
                >
                  <Plus size={16} className="mr-2" />
                  添加房间
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {house.rooms.map((room) => (
                  <div key={room.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <input
                        type="text"
                        value={room.name}
                        onChange={(e) => updateRoom(room.id, { name: e.target.value })}
                        className="font-medium text-gray-900 bg-transparent border-none outline-none"
                      />
                      <button
                        onClick={() => deleteRoom(room.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          房间类型
                        </label>
                        <select
                          value={room.type}
                          onChange={(e) => updateRoom(room.id, { type: e.target.value as Room['type'] })}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                        >
                          {roomTypes.map(type => (
                            <option key={type.value} value={type.value}>
                              {type.icon} {type.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          全景图 *
                        </label>
                        {room.panoramaUrl ? (
                          <div className="relative">
                            <img
                              src={room.panoramaUrl}
                              alt={room.name}
                              className="w-full h-20 object-cover rounded"
                            />
                            <button
                              onClick={() => updateRoom(room.id, { panoramaUrl: '' })}
                              className="absolute top-1 right-1 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-xs"
                            >
                              <X size={12} />
                            </button>
                          </div>
                        ) : (
                          <label className="block w-full h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-blue-500">
                            <input
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) handlePanoramaUpload(room.id, file);
                              }}
                            />
                            <div className="text-center">
                              <Upload size={20} className="mx-auto text-gray-400 mb-1" />
                              <p className="text-xs text-gray-600">上传全景图</p>
                            </div>
                          </label>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {currentStep === 'connections' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">连接点设置</h3>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 房间列表 */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">选择房间</h4>
                  <div className="space-y-2">
                    {house.rooms.map((room) => (
                      <button
                        key={room.id}
                        onClick={() => setSelectedRoomId(room.id)}
                        className={`w-full text-left p-3 rounded-lg border ${
                          selectedRoomId === room.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{room.name}</span>
                          <span className="text-sm text-gray-500">
                            {room.hotSpots.length} 个连接点
                          </span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* 连接点编辑 */}
                {selectedRoom && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">
                      {selectedRoom.name} - 连接点
                    </h4>
                    
                    {selectedRoom.panoramaUrl ? (
                      <div className="relative">
                        <img
                          src={selectedRoom.panoramaUrl}
                          alt={selectedRoom.name}
                          className="w-full h-48 object-cover rounded-lg cursor-crosshair"
                          onClick={(e) => {
                            if (isAddingHotSpot) {
                              const rect = e.currentTarget.getBoundingClientRect();
                              const x = (e.clientX - rect.left) / rect.width;
                              const y = (e.clientY - rect.top) / rect.height;
                              setHotSpotPosition({ x, y });
                            }
                          }}
                        />
                        
                        {/* 显示现有连接点 */}
                        {selectedRoom.hotSpots.map((hotSpot) => (
                          <button
                            key={hotSpot.id}
                            className="absolute w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs transform -translate-x-1/2 -translate-y-1/2"
                            style={{
                              left: `${hotSpot.x * 100}%`,
                              top: `${hotSpot.y * 100}%`
                            }}
                            onClick={() => deleteHotSpot(selectedRoom.id, hotSpot.id)}
                            title={`删除连接点: ${hotSpot.title}`}
                          >
                            <Navigation size={12} />
                          </button>
                        ))}
                        
                        {/* 新连接点预览 */}
                        {isAddingHotSpot && (
                          <div
                            className="absolute w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-xs transform -translate-x-1/2 -translate-y-1/2 animate-pulse"
                            style={{
                              left: `${hotSpotPosition.x * 100}%`,
                              top: `${hotSpotPosition.y * 100}%`
                            }}
                          >
                            <Plus size={12} />
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                        <p className="text-gray-500">请先上传全景图</p>
                      </div>
                    )}

                    {/* 连接点操作 */}
                    <div className="mt-4 space-y-3">
                      {!isAddingHotSpot ? (
                        <button
                          onClick={() => setIsAddingHotSpot(true)}
                          disabled={!selectedRoom.panoramaUrl}
                          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg flex items-center justify-center"
                        >
                          <Plus size={16} className="mr-2" />
                          添加连接点
                        </button>
                      ) : (
                        <div className="space-y-2">
                          <p className="text-sm text-gray-600">
                            点击全景图选择连接点位置，然后选择目标房间
                          </p>
                          <div className="flex space-x-2">
                            {house.rooms
                              .filter(room => room.id !== selectedRoom.id)
                              .map(room => (
                                <button
                                  key={room.id}
                                  onClick={() => addHotSpot(selectedRoom.id, room.id)}
                                  className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm"
                                >
                                  前往{room.name}
                                </button>
                              ))}
                          </div>
                          <button
                            onClick={() => setIsAddingHotSpot(false)}
                            className="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg"
                          >
                            取消
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 底部操作栏 */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            取消
          </button>
          
          <div className="flex space-x-3">
            {currentStep !== 'basic' && (
              <button
                onClick={() => {
                  if (currentStep === 'rooms') setCurrentStep('basic');
                  if (currentStep === 'connections') setCurrentStep('rooms');
                }}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                上一步
              </button>
            )}
            
            {currentStep !== 'connections' ? (
              <button
                onClick={() => {
                  if (currentStep === 'basic') setCurrentStep('rooms');
                  if (currentStep === 'rooms') setCurrentStep('connections');
                }}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
              >
                下一步
              </button>
            ) : (
              <button
                onClick={saveHouse}
                className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center"
              >
                <Save size={16} className="mr-2" />
                保存房屋
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HouseManager;
