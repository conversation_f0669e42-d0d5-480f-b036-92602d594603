'use client';

import React, { useState, useEffect, useRef } from 'react';
import {
  X,
  Home,
  ArrowLeft,
  ArrowRight,
  MapPin,
  Eye,
  Navigation,
  Maximize2,
  Minimize2,
  RotateCcw,
  Info,
  Map,
  Play,
  Pause
} from 'lucide-react';

// 房间连接点接口
export interface HotSpot {
  id: string;
  x: number; // 在全景图中的x坐标 (0-1)
  y: number; // 在全景图中的y坐标 (0-1)
  targetRoomId: string; // 目标房间ID
  title: string; // 连接点标题
  description?: string; // 连接点描述
  icon?: string; // 图标类型
}

// 房间接口
export interface Room {
  id: string;
  name: string;
  type: 'living_room' | 'bedroom' | 'kitchen' | 'bathroom' | 'dining_room' | 'office' | 'other';
  panoramaUrl: string;
  thumbnail?: string;
  hotSpots: HotSpot[]; // 该房间的连接点
  description?: string;
  area?: number; // 面积（平方米）
}

// 房屋接口
export interface House {
  id: string;
  name: string;
  address: string;
  description?: string;
  rooms: Room[];
  startRoomId?: string; // 默认起始房间
}

interface HouseVRTourProps {
  house: House;
  onClose: () => void;
  startRoomId?: string;
}

const HouseVRTour: React.FC<HouseVRTourProps> = ({
  house,
  onClose,
  startRoomId
}) => {
  const [currentRoomId, setCurrentRoomId] = useState<string>(
    startRoomId || house.startRoomId || house.rooms[0]?.id || ''
  );
  const [showRoomList, setShowRoomList] = useState(false);
  const [showFloorPlan, setShowFloorPlan] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [is720Mode, setIs720Mode] = useState(true);

  const containerRef = useRef<HTMLDivElement>(null);
  const currentRoom = house.rooms.find(room => room.id === currentRoomId);

  // 房间类型图标映射
  const getRoomIcon = (type: Room['type']) => {
    const icons = {
      living_room: '🛋️',
      bedroom: '🛏️',
      kitchen: '🍳',
      bathroom: '🚿',
      dining_room: '🍽️',
      office: '💼',
      other: '🏠'
    };
    return icons[type] || '🏠';
  };

  // 房间类型中文名称
  const getRoomTypeName = (type: Room['type']) => {
    const names = {
      living_room: '客厅',
      bedroom: '卧室',
      kitchen: '厨房',
      bathroom: '浴室',
      dining_room: '餐厅',
      office: '书房',
      other: '其他'
    };
    return names[type] || '房间';
  };

  // 处理房间切换
  const navigateToRoom = (roomId: string) => {
    if (roomId === currentRoomId) return;

    setIsLoading(true);
    setCurrentRoomId(roomId);

    // 重新创建VR场景
    setTimeout(() => {
      setIsLoading(false);
    }, 800);
  };

  // 检测设备性能
  const detectDevicePerformance = () => {
    const userAgent = navigator.userAgent;
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isLowEnd = /Android.*4\.|iPhone.*OS [5-9]_|iPad.*OS [5-9]_/i.test(userAgent);

    // 检测GPU性息
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    const debugInfo = gl?.getExtension('WEBGL_debug_renderer_info');
    const renderer = debugInfo ? gl?.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : '';
    const isLowEndGPU = /Adreno [0-4]|Mali-[0-4]|PowerVR SGX/i.test(renderer || '');

    return {
      isMobile,
      isLowEnd: isLowEnd || isLowEndGPU,
      maxTextureSize: gl?.getParameter(gl.MAX_TEXTURE_SIZE) || 2048
    };
  };

  // 创建VR720场景
  useEffect(() => {
    if (typeof window !== 'undefined' && currentRoom && containerRef.current) {
      setIsLoading(true);

      const deviceInfo = detectDevicePerformance();

      // 根据设备性能调整设置
      const performanceSettings = {
        // 移动端优化设置
        antialias: !deviceInfo.isMobile,
        logarithmicDepthBuffer: !deviceInfo.isLowEnd,
        precision: deviceInfo.isLowEnd ? 'mediump' : 'highp',
        maxCanvasWidth: deviceInfo.isLowEnd ? 1024 : 1920,
        maxCanvasHeight: deviceInfo.isLowEnd ? 1024 : 1920,
        // 动画设置
        animationDuration: deviceInfo.isLowEnd ?
          (is720Mode ? 200000 : 120000) : // 低端设备更慢的动画
          (is720Mode ? 150000 : 80000),   // 正常设备
        // 帧率设置
        targetFrameRate: deviceInfo.isLowEnd ? 30 : 60
      };

      // 生成连接点的A-Frame元素
      const generateHotSpots = () => {
        return currentRoom.hotSpots.map((hotSpot, index) => {
          const targetRoom = house.rooms.find(room => room.id === hotSpot.targetRoomId);
          if (!targetRoom) return '';

          // 将2D坐标转换为3D球面坐标
          const phi = (hotSpot.x - 0.5) * Math.PI * 2; // 水平角度
          const theta = (0.5 - hotSpot.y) * Math.PI; // 垂直角度
          const radius = 8; // 球面半径

          const x = radius * Math.sin(theta) * Math.cos(phi);
          const y = radius * Math.cos(theta);
          const z = radius * Math.sin(theta) * Math.sin(phi);

          return `
            <!-- 连接点 ${index + 1}: ${hotSpot.title} -->
            <a-entity
              id="hotspot-${hotSpot.id}"
              position="${x.toFixed(2)} ${y.toFixed(2)} ${z.toFixed(2)}"
              class="hotspot"
              data-target-room="${hotSpot.targetRoomId}"
              data-title="${hotSpot.title}"
            >
              <!-- 连接点球体 -->
              <a-sphere
                radius="0.3"
                color="#4ecdc4"
                opacity="0.9"
                animation="${deviceInfo.isLowEnd ?
                  'property: scale; to: 1.1 1.1 1.1; loop: true; dir: alternate; dur: 3000' :
                  'property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 2000'}"
                material="shader: flat"
              ></a-sphere>

              ${!deviceInfo.isLowEnd ? `
              <!-- 连接点光环 (仅高性能设备) -->
              <a-ring
                radius-inner="0.4"
                radius-outer="0.6"
                color="#ff6b6b"
                opacity="0.5"
                animation="property: rotation; to: 0 0 360; loop: true; dur: 6000; easing: linear"
                position="0 0 0.1"
              ></a-ring>
              ` : ''}

              <!-- 连接点图标 -->
              <a-text
                value="🚪"
                position="0 0 0.35"
                align="center"
                color="#ffffff"
                font="size: 8"
                look-at="[camera]"
              ></a-text>

              <!-- 连接点标签 -->
              <a-text
                value="${hotSpot.title}"
                position="0 -0.8 0"
                align="center"
                color="#ffffff"
                font="size: 4; weight: bold"
                look-at="[camera]"
                material="shader: msdf"
                geometry="primitive: plane; width: auto; height: auto"
                background="color: rgba(0,0,0,0.8); opacity: 0.8"
              ></a-text>
            </a-entity>
          `;
        }).join('');
      };

      // 创建VR720场景HTML
      const sceneHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
          <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
          <style>
            body {
              margin: 0;
              overflow: hidden;
              background: #000;
              touch-action: none;
              -webkit-user-select: none;
              user-select: none;
            }
            #vr-scene {
              width: 100vw;
              height: 100vh;
              position: fixed;
              top: 0;
              left: 0;
            }
            .loading-overlay {
              position: fixed;
              top: 0;
              left: 0;
              width: 100vw;
              height: 100vh;
              background: #000;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 2000;
              flex-direction: column;
            }
            .spinner {
              width: 40px;
              height: 40px;
              border: 3px solid rgba(255,255,255,0.3);
              border-top: 3px solid #4ecdc4;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-bottom: 20px;
            }
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        </head>
        <body>
          <a-scene
            id="vr-scene"
            embedded
            style="height: 100vh; width: 100vw;"
            vr-mode-ui="enabled: true"
            background="color: #000"
            device-orientation-permission-ui="enabled: false"
            cursor="rayOrigin: mouse"
            renderer="antialias: ${performanceSettings.antialias};
                     logarithmicDepthBuffer: ${performanceSettings.logarithmicDepthBuffer};
                     precision: ${performanceSettings.precision};
                     maxCanvasWidth: ${performanceSettings.maxCanvasWidth};
                     maxCanvasHeight: ${performanceSettings.maxCanvasHeight};
                     colorManagement: true;
                     sortObjects: true;
                     physicallyCorrectLights: false"
            stats="false"
          >
            <a-assets>
              <img id="panorama" src="${currentRoom.panoramaUrl}" crossorigin="anonymous">
            </a-assets>

            <!-- VR720天空球 -->
            <a-sky
              id="room-sky"
              src="#panorama"
              rotation="0 -90 0"
              animation="property: rotation; to: 0 ${is720Mode ? '630' : '270'} 0; loop: true; dur: ${performanceSettings.animationDuration}; easing: linear"
              material="side: back; npot: true"
            ></a-sky>

            <!-- VR相机 -->
            <a-camera
              id="main-camera"
              look-controls="enabled: true;
                           reverseMouseDrag: false;
                           touchEnabled: true;
                           magicWindowTrackingEnabled: true;
                           pointerLockEnabled: false;
                           mouseSensitivity: ${deviceInfo.isLowEnd ? 0.3 : 0.5};
                           touchSensitivity: ${deviceInfo.isLowEnd ? 8 : 15}"
              wasd-controls="enabled: false"
              position="0 1.6 0"
              fov="${deviceInfo.isMobile ? '75' : '80'}"
            >
              ${!deviceInfo.isLowEnd ? `
              <a-cursor
                color="#4ecdc4"
                opacity="0.6"
                geometry="primitive: ring; radiusInner: 0.015; radiusOuter: 0.025"
                material="color: #4ecdc4; shader: flat"
                animation="property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 2000"
              ></a-cursor>
              ` : ''}
            </a-camera>

            <!-- 环境光 -->
            <a-light type="ambient" color="#606060"></a-light>
            ${!deviceInfo.isLowEnd ? '<a-light type="directional" position="1 1 1" color="#ffffff" intensity="0.2"></a-light>' : ''}

            <!-- 房间标题 -->
            <a-text
              value="${currentRoom.name} - ${house.name}"
              position="0 3.5 -4"
              align="center"
              color="#4ecdc4"
              opacity="0.9"
              font="size: ${deviceInfo.isMobile ? '16' : '20'}; weight: bold"
              ${!deviceInfo.isLowEnd ? 'animation="property: opacity; to: 0.7; loop: true; dir: alternate; dur: 5000"' : ''}
            ></a-text>

            ${currentRoom.description && !deviceInfo.isLowEnd ? `
            <!-- 房间信息 -->
            <a-text
              value="${currentRoom.description}"
              position="0 3 -4"
              align="center"
              color="#ffffff"
              opacity="0.7"
              font="size: ${deviceInfo.isMobile ? '10' : '12'}"
            ></a-text>
            ` : ''}

            ${!deviceInfo.isLowEnd ? `
            <!-- VR720指示器 -->
            <a-ring
              position="0 -2 -3"
              color="#ff6b6b"
              radius-inner="0.3"
              radius-outer="0.4"
              opacity="0.6"
              animation="property: rotation; to: 0 0 ${is720Mode ? '720' : '360'}; loop: true; dur: ${is720Mode ? '15000' : '10000'}; easing: linear"
            ></a-ring>

            <a-text
              value="${is720Mode ? 'VR720°' : 'VR360°'} House Tour"
              position="0 -2.5 -3"
              align="center"
              color="#ff6b6b"
              font="size: 8; weight: bold"
              opacity="0.8"
            ></a-text>
            ` : ''}

            <!-- 连接点 -->
            ${generateHotSpots()}
          </a-scene>

          <div class="loading-overlay" id="loading" style="display: flex;">
            <div class="spinner"></div>
            <div style="color: white; text-align: center;">
              <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px;">Loading ${currentRoom.name}</div>
              <div style="font-size: 12px; opacity: 0.8;">${house.name} - VR720° House Tour</div>
            </div>
          </div>

          <script>
            // 设备性能检测
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isLowEnd = ${deviceInfo.isLowEnd};

            // 性能监控
            let frameCount = 0;
            let lastTime = performance.now();
            let fps = 60;

            function monitorPerformance() {
              frameCount++;
              const currentTime = performance.now();

              if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                frameCount = 0;
                lastTime = currentTime;

                // 如果帧率过低，自动降低质量
                if (fps < 20 && !isLowEnd) {
                  console.log('Low FPS detected, reducing quality...');
                  const scene = document.querySelector('a-scene');
                  if (scene) {
                    scene.setAttribute('renderer', 'antialias: false; precision: mediump; maxCanvasWidth: 1024; maxCanvasHeight: 1024');
                  }
                }
              }

              if (!isLowEnd) {
                requestAnimationFrame(monitorPerformance);
              }
            }

            // 连接点点击处理
            document.addEventListener('DOMContentLoaded', function() {
              // 启动性能监控
              if (!isLowEnd) {
                monitorPerformance();
              }

              const hotspots = document.querySelectorAll('.hotspot');

              hotspots.forEach(hotspot => {
                hotspot.addEventListener('click', function() {
                  const targetRoomId = this.getAttribute('data-target-room');
                  const title = this.getAttribute('data-title');

                  // 发送房间切换消息到父窗口
                  window.parent.postMessage({
                    type: 'navigate',
                    roomId: targetRoomId,
                    title: title
                  }, '*');
                });

                // 鼠标悬停效果（仅高性能设备）
                if (!isLowEnd) {
                  hotspot.addEventListener('mouseenter', function() {
                    const sphere = this.querySelector('a-sphere');
                    if (sphere) {
                      sphere.setAttribute('animation', 'property: scale; to: 1.5 1.5 1.5; dur: 300');
                      sphere.setAttribute('color', '#ff6b6b');
                    }
                  });

                  hotspot.addEventListener('mouseleave', function() {
                    const sphere = this.querySelector('a-sphere');
                    if (sphere) {
                      sphere.setAttribute('animation', 'property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 2000');
                      sphere.setAttribute('color', '#4ecdc4');
                    }
                  });
                }
              });
            });

            // 隐藏加载界面
            const hideLoading = () => {
              const loadingEl = document.querySelector('#loading');
              if (loadingEl) {
                loadingEl.style.display = 'none';
              }
            };

            // 等待场景完全加载
            const scene = document.querySelector('a-scene');
            if (scene) {
              scene.addEventListener('loaded', () => {
                setTimeout(hideLoading, isLowEnd ? 1000 : 500);
              });
            } else {
              setTimeout(hideLoading, isLowEnd ? 3000 : 2000);
            }

            // 移动端特定优化
            if (isMobile) {
              // 禁用右键菜单
              document.addEventListener('contextmenu', e => e.preventDefault());

              // 防止页面滚动
              document.addEventListener('touchmove', e => e.preventDefault(), { passive: false });

              // 优化触摸响应
              document.addEventListener('touchstart', e => e.preventDefault(), { passive: false });
            }

            // 监听父窗口消息
            window.addEventListener('message', function(event) {
              if (event.data === 'toggle720') {
                const sky = document.querySelector('#room-sky');
                const ring = document.querySelector('a-ring');
                const text = document.querySelector('a-text[value*="VR"]');

                const currentAnimation = sky.getAttribute('animation');
                const isCurrently720 = currentAnimation.to.includes('630');

                // 根据设备性能调整动画时长
                const duration720 = isLowEnd ? 200000 : 150000;
                const duration360 = isLowEnd ? 120000 : 80000;
                const ringDuration720 = isLowEnd ? 15000 : 12000;
                const ringDuration360 = isLowEnd ? 10000 : 8000;

                if (isCurrently720) {
                  // 切换到360°
                  sky.setAttribute('animation', \`property: rotation; to: 0 270 0; loop: true; dur: \${duration360}; easing: linear\`);
                  if (ring) {
                    ring.setAttribute('animation', \`property: rotation; to: 0 0 360; loop: true; dur: \${ringDuration360}; easing: linear\`);
                  }
                  if (text) {
                    text.setAttribute('value', 'VR360° House Tour');
                  }
                } else {
                  // 切换到720°
                  sky.setAttribute('animation', \`property: rotation; to: 0 630 0; loop: true; dur: \${duration720}; easing: linear\`);
                  if (ring) {
                    ring.setAttribute('animation', \`property: rotation; to: 0 0 720; loop: true; dur: \${ringDuration720}; easing: linear\`);
                  }
                  if (text) {
                    text.setAttribute('value', 'VR720° House Tour');
                  }
                }
              }
            });
          </script>
        </body>
        </html>
      `;

      // 创建iframe
      const iframe = document.createElement('iframe');
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
      iframe.style.position = 'absolute';
      iframe.style.top = '0';
      iframe.style.left = '0';
      iframe.srcdoc = sceneHTML;

      // 监听iframe消息
      const handleMessage = (event: MessageEvent) => {
        if (event.data?.type === 'navigate') {
          navigateToRoom(event.data.roomId);
        }
      };

      window.addEventListener('message', handleMessage);

      // 监听iframe加载完成
      iframe.onload = () => {
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
      };

      containerRef.current.appendChild(iframe);

      return () => {
        window.removeEventListener('message', handleMessage);
        if (containerRef.current && iframe) {
          containerRef.current.removeChild(iframe);
        }
      };
    }
  }, [currentRoom, house, is720Mode]);

  // 切换720/360模式
  const toggle720Mode = () => {
    setIs720Mode(!is720Mode);

    // 发送消息到iframe
    const iframe = containerRef.current?.querySelector('iframe');
    if (iframe?.contentWindow) {
      iframe.contentWindow.postMessage('toggle720', '*');
    }
  };

  // 创建平面图组件
  const FloorPlan = () => (
    <div className="absolute top-20 right-4 bg-black/90 text-white p-4 rounded-lg max-w-sm z-20">
      <h3 className="text-lg font-medium mb-3 text-center">房间平面图</h3>
      <div className="grid grid-cols-2 gap-2">
        {house.rooms.map((room, index) => (
          <button
            key={room.id}
            onClick={() => navigateToRoom(room.id)}
            className={`p-3 rounded-lg text-sm transition-all ${
              room.id === currentRoomId
                ? 'bg-blue-600 text-white scale-105'
                : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
            }`}
          >
            <div className="text-lg mb-1">{getRoomIcon(room.type)}</div>
            <div className="font-medium">{room.name}</div>
            <div className="text-xs opacity-75">
              {room.area ? `${room.area}㎡` : getRoomTypeName(room.type)}
            </div>
          </button>
        ))}
      </div>
      <div className="mt-3 text-xs text-gray-400 text-center">
        点击房间快速切换
      </div>
    </div>
  );

  if (!currentRoom) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center z-50">
        <div className="text-white text-center">
          <p className="text-xl mb-4">房间未找到</p>
          <button
            onClick={onClose}
            className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black z-50">
      {/* VR720场景容器 */}
      <div ref={containerRef} className="w-full h-full relative" />

      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/90 flex items-center justify-center z-30">
          <div className="text-white text-center">
            <div className="relative mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-gradient-to-r from-pink-500 to-blue-500 mx-auto"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-2xl">🏠</div>
              </div>
            </div>
            <p className="text-lg font-bold bg-gradient-to-r from-pink-500 to-blue-500 bg-clip-text text-transparent">
              Loading {currentRoom?.name}
            </p>
            <p className="text-sm text-gray-400 mt-2">{house.name} - VR720° House Tour</p>
            <div className="mt-4 flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
          </div>
        </div>
      )}

      {/* 顶部控制栏 */}
      <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-4 z-20 pointer-events-none">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onClose}
              className="pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95"
            >
              <X size={20} />
            </button>

            <div className="text-white">
              <h2 className="text-lg font-medium">{house.name}</h2>
              <p className="text-sm text-gray-300">
                {getRoomIcon(currentRoom?.type || 'other')} {currentRoom?.name}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowInfo(!showInfo)}
              className="pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95"
            >
              <Info size={20} />
            </button>

            <button
              onClick={() => setShowFloorPlan(!showFloorPlan)}
              className="pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95"
            >
              <Map size={20} />
            </button>

            <button
              onClick={toggle720Mode}
              className="pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95"
              title={is720Mode ? '切换到360°' : '切换到720°'}
            >
              {is720Mode ? <Pause size={20} /> : <Play size={20} />}
            </button>

            <button
              onClick={() => setShowRoomList(!showRoomList)}
              className="pointer-events-auto w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95"
            >
              <Home size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* 房间信息面板 */}
      {showInfo && currentRoom && (
        <div className="absolute top-20 left-4 bg-black/90 text-white p-4 rounded-lg max-w-sm z-20">
          <h3 className="text-lg font-medium mb-2">{currentRoom.name}</h3>
          <p className="text-sm text-gray-300 mb-2">
            类型: {getRoomTypeName(currentRoom.type)}
          </p>
          {currentRoom.area && (
            <p className="text-sm text-gray-300 mb-2">
              面积: {currentRoom.area}㎡
            </p>
          )}
          {currentRoom.description && (
            <p className="text-sm text-gray-300 mb-3">
              {currentRoom.description}
            </p>
          )}
          <div className="text-xs text-gray-400">
            <p>• 拖拽旋转视角</p>
            <p>• 点击蓝色球体切换房间</p>
            <p>• 当前模式: {is720Mode ? 'VR720°' : 'VR360°'}</p>
          </div>
        </div>
      )}

      {/* 平面图面板 */}
      {showFloorPlan && <FloorPlan />}

      {/* 房间列表面板 */}
      {showRoomList && (
        <div className="absolute top-20 right-4 bg-black/90 text-white p-4 rounded-lg max-w-xs z-20">
          <h3 className="text-lg font-medium mb-3">房间导航</h3>
          <div className="space-y-2">
            {house.rooms.map((room) => (
              <button
                key={room.id}
                onClick={() => navigateToRoom(room.id)}
                className={`w-full text-left p-3 rounded-lg transition-all ${
                  room.id === currentRoomId
                    ? 'bg-blue-600 text-white scale-105'
                    : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{getRoomIcon(room.type)}</span>
                  <div>
                    <p className="font-medium">{room.name}</p>
                    <p className="text-xs opacity-75">
                      {getRoomTypeName(room.type)}
                      {room.hotSpots.length > 0 && ` • ${room.hotSpots.length}个连接点`}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 底部导航栏 */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 z-20 pointer-events-none">
        <div className="flex items-center justify-center space-x-3 mb-4">
          {house.rooms.map((room) => (
            <button
              key={room.id}
              onClick={() => navigateToRoom(room.id)}
              className={`pointer-events-auto w-14 h-14 rounded-full flex items-center justify-center text-lg transition-all ${
                room.id === currentRoomId
                  ? 'bg-blue-600 text-white scale-110 shadow-lg'
                  : 'bg-black/70 text-gray-300 hover:bg-black/90 hover:scale-105'
              }`}
              title={room.name}
            >
              {getRoomIcon(room.type)}
            </button>
          ))}
        </div>

        {/* 状态指示器 */}
        <div className="text-center">
          <div className="inline-flex items-center bg-black/70 text-white px-4 py-2 rounded-full text-sm">
            <div className={`w-2 h-2 rounded-full mr-2 ${is720Mode ? 'bg-green-500' : 'bg-blue-500'}`}></div>
            {is720Mode ? 'VR720°' : 'VR360°'} House Tour • 拖拽旋转 • 点击球体切换房间
          </div>
        </div>
      </div>
    </div>
  );
};

export default HouseVRTour;
