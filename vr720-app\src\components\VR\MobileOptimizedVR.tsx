'use client';

import React, { useState, useEffect, useRef } from 'react';
import { X, Home, Info, Map, Play, Pause } from 'lucide-react';

interface MobileOptimizedVRProps {
  panoramaUrl: string;
  roomName: string;
  houseName: string;
  onClose: () => void;
  is720Mode?: boolean;
  onToggleMode?: () => void;
}

const MobileOptimizedVR: React.FC<MobileOptimizedVRProps> = ({
  panoramaUrl,
  roomName,
  houseName,
  onClose,
  is720Mode = true,
  onToggleMode
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [showInfo, setShowInfo] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (typeof window !== 'undefined' && containerRef.current) {
      setIsLoading(true);

      // 检测设备性能
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const isLowEnd = /Android.*4\.|iPhone.*OS [5-9]_|iPad.*OS [5-9]_/i.test(navigator.userAgent);

      // 移动端优化的VR场景
      const sceneHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
          <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
          <style>
            body { 
              margin: 0; 
              overflow: hidden; 
              background: #000;
              touch-action: none;
              -webkit-user-select: none;
              user-select: none;
              -webkit-overflow-scrolling: touch;
            }
            #vr-scene { 
              width: 100vw; 
              height: 100vh; 
              position: fixed;
              top: 0;
              left: 0;
            }
            .loading {
              position: fixed;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: white;
              font-family: Arial, sans-serif;
              text-align: center;
              z-index: 1000;
            }
            .spinner {
              width: 30px;
              height: 30px;
              border: 2px solid rgba(255,255,255,0.3);
              border-top: 2px solid #4ecdc4;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin: 0 auto 10px;
            }
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        </head>
        <body>
          <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Loading ${roomName}...</div>
          </div>
          
          <a-scene 
            id="vr-scene" 
            embedded 
            vr-mode-ui="enabled: false"
            background="color: #000"
            device-orientation-permission-ui="enabled: false"
            renderer="antialias: false; 
                     logarithmicDepthBuffer: false; 
                     precision: ${isLowEnd ? 'lowp' : 'mediump'}; 
                     maxCanvasWidth: ${isLowEnd ? 768 : 1024}; 
                     maxCanvasHeight: ${isLowEnd ? 768 : 1024};
                     colorManagement: false;
                     sortObjects: false;
                     physicallyCorrectLights: false"
            stats="false"
          >
            <a-assets>
              <img id="panorama" src="${panoramaUrl}" crossorigin="anonymous">
            </a-assets>
            
            <!-- 简化的天空球 -->
            <a-sky 
              id="room-sky"
              src="#panorama" 
              rotation="0 -90 0"
              animation="property: rotation; to: 0 ${is720Mode ? '630' : '270'} 0; loop: true; dur: ${isLowEnd ? (is720Mode ? 240000 : 150000) : (is720Mode ? 180000 : 100000)}; easing: linear"
              material="side: back; npot: true"
            ></a-sky>
            
            <!-- 优化的相机 -->
            <a-camera 
              id="main-camera"
              look-controls="enabled: true; 
                           reverseMouseDrag: false; 
                           touchEnabled: true; 
                           magicWindowTrackingEnabled: true;
                           pointerLockEnabled: false;
                           mouseSensitivity: 0.2;
                           touchSensitivity: ${isLowEnd ? 4 : 8}"
              wasd-controls="enabled: false"
              position="0 1.6 0"
              fov="75"
            ></a-camera>
            
            <!-- 简化的环境光 -->
            <a-light type="ambient" color="#666666"></a-light>
            
            <!-- 简化的房间标题 -->
            <a-text
              value="${roomName}"
              position="0 3 -4"
              align="center"
              color="#4ecdc4"
              opacity="0.8"
              font="size: 14; weight: bold"
            ></a-text>
          </a-scene>

          <script>
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            document.addEventListener('DOMContentLoaded', function() {
              // 移动端优化
              if (isMobile) {
                // 禁用右键菜单和滚动
                document.addEventListener('contextmenu', e => e.preventDefault());
                document.addEventListener('touchmove', e => e.preventDefault(), { passive: false });
                
                // 优化触摸响应
                let touchStartTime = 0;
                document.addEventListener('touchstart', e => {
                  touchStartTime = Date.now();
                }, { passive: true });
                
                document.addEventListener('touchend', e => {
                  const touchDuration = Date.now() - touchStartTime;
                  if (touchDuration < 200) {
                    // 短触摸，可以添加特定操作
                  }
                }, { passive: true });
              }
              
              // 隐藏加载界面
              const scene = document.querySelector('a-scene');
              const loading = document.querySelector('#loading');
              
              const hideLoading = () => {
                if (loading) {
                  loading.style.display = 'none';
                }
                // 通知父窗口加载完成
                window.parent.postMessage({ type: 'loaded' }, '*');
              };
              
              if (scene) {
                scene.addEventListener('loaded', () => {
                  setTimeout(hideLoading, 800);
                });
              } else {
                setTimeout(hideLoading, 2000);
              }
            });
            
            // 监听父窗口消息
            window.addEventListener('message', function(event) {
              if (event.data === 'toggle720') {
                const sky = document.querySelector('#room-sky');
                const currentAnimation = sky.getAttribute('animation');
                const isCurrently720 = currentAnimation.to.includes('630');
                
                const isLowEnd = ${isLowEnd};
                const duration720 = isLowEnd ? 240000 : 180000;
                const duration360 = isLowEnd ? 150000 : 100000;
                
                if (isCurrently720) {
                  sky.setAttribute('animation', \`property: rotation; to: 0 270 0; loop: true; dur: \${duration360}; easing: linear\`);
                } else {
                  sky.setAttribute('animation', \`property: rotation; to: 0 630 0; loop: true; dur: \${duration720}; easing: linear\`);
                }
                
                // 通知父窗口模式已切换
                window.parent.postMessage({ 
                  type: 'modeChanged', 
                  is720Mode: !isCurrently720 
                }, '*');
              }
            });
          </script>
        </body>
        </html>
      `;

      // 创建iframe
      const iframe = document.createElement('iframe');
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
      iframe.style.position = 'absolute';
      iframe.style.top = '0';
      iframe.style.left = '0';
      iframe.srcdoc = sceneHTML;

      // 监听iframe消息
      const handleMessage = (event: MessageEvent) => {
        if (event.data?.type === 'loaded') {
          setIsLoading(false);
        } else if (event.data?.type === 'modeChanged') {
          // 处理模式切换
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      containerRef.current.appendChild(iframe);
      
      return () => {
        window.removeEventListener('message', handleMessage);
        if (containerRef.current && iframe) {
          containerRef.current.removeChild(iframe);
        }
      };
    }
  }, [panoramaUrl, roomName, is720Mode]);

  // 切换720/360模式
  const handleToggleMode = () => {
    const iframe = containerRef.current?.querySelector('iframe');
    if (iframe?.contentWindow) {
      iframe.contentWindow.postMessage('toggle720', '*');
    }
    onToggleMode?.();
  };

  return (
    <div className="fixed inset-0 bg-black z-50">
      {/* VR场景容器 */}
      <div ref={containerRef} className="w-full h-full relative" />

      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/90 flex items-center justify-center z-30">
          <div className="text-white text-center">
            <div className="relative mb-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-3 border-blue-500 mx-auto"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-lg">🏠</div>
              </div>
            </div>
            <p className="text-lg font-bold text-blue-400">Loading {roomName}</p>
            <p className="text-sm text-gray-400 mt-1">{houseName} - Mobile VR</p>
          </div>
        </div>
      )}

      {/* 简化的控制栏 */}
      <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-3 z-20 pointer-events-none">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95"
            >
              <X size={18} />
            </button>
            
            <div className="text-white">
              <h2 className="text-base font-medium">{houseName}</h2>
              <p className="text-sm text-gray-300">🏠 {roomName}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowInfo(!showInfo)}
              className="pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95"
            >
              <Info size={18} />
            </button>
            
            <button
              onClick={handleToggleMode}
              className="pointer-events-auto w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all active:scale-95"
              title={is720Mode ? '切换到360°' : '切换到720°'}
            >
              {is720Mode ? <Pause size={18} /> : <Play size={18} />}
            </button>
          </div>
        </div>
      </div>

      {/* 信息面板 */}
      {showInfo && (
        <div className="absolute top-16 left-3 bg-black/90 text-white p-3 rounded-lg max-w-xs z-20">
          <h3 className="text-base font-medium mb-2">{roomName}</h3>
          <div className="text-xs text-gray-400 space-y-1">
            <p>• 拖拽旋转视角</p>
            <p>• 当前模式: {is720Mode ? 'VR720°' : 'VR360°'}</p>
            <p>• 移动端优化版本</p>
          </div>
        </div>
      )}

      {/* 底部状态指示器 */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3 z-20 pointer-events-none">
        <div className="text-center">
          <div className="inline-flex items-center bg-black/70 text-white px-3 py-1 rounded-full text-xs">
            <div className={`w-2 h-2 rounded-full mr-2 ${is720Mode ? 'bg-green-500' : 'bg-blue-500'}`}></div>
            {is720Mode ? 'VR720°' : 'VR360°'} Mobile • 拖拽旋转视角
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileOptimizedVR;

const duration720 = isLowEnd ? 360000 : 240000;
const duration360 = isLowEnd ? 240000 : 120000;
