'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  ArrowLeft,
  Share2,
  Download,
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Play,
  Pause,
  Eye,
  Settings,
  Info
} from 'lucide-react';

interface MobileVRTourViewerProps {
  panoramaUrl: string;
  title?: string;
  description?: string;
  onClose: () => void;
}

export const MobileVRTourViewer: React.FC<MobileVRTourViewerProps> = ({
  panoramaUrl,
  title = "VR Tour",
  description,
  onClose
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [showInfo, setShowInfo] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-hide controls after 3 seconds
  useEffect(() => {
    if (showControls) {
      const timer = setTimeout(() => {
        setShowControls(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showControls]);

  // Show controls on touch/click
  const handleInteraction = () => {
    setShowControls(true);
  };

  // Auto rotation
  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setRotation(prev => (prev + 1) % 360);
      }, 100);
      return () => clearInterval(interval);
    }
  }, [isPlaying]);

  // Handle rotation
  const handleRotateLeft = () => {
    setRotation(prev => prev - 15);
  };

  const handleRotateRight = () => {
    setRotation(prev => prev + 15);
  };

  // Handle zoom
  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.2, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.2, 0.5));
  };

  // Download image
  const handleDownload = () => {
    const link = document.createElement('a');
    link.download = `vr-tour-${Date.now()}.jpg`;
    link.href = panoramaUrl;
    link.click();
  };

  // Share functionality
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: description || 'Check out this VR tour!',
          url: window.location.href
        });
      } catch (error) {
        console.log('Share failed:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  return (
    <div className="fixed inset-0 bg-black z-50" ref={containerRef}>
      {/* Header Controls */}
      <div 
        className={`absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/80 to-transparent p-4 transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <div className="flex items-center justify-between text-white">
          <button
            onClick={onClose}
            className="flex items-center space-x-2 hover:text-gray-300 transition-colors"
          >
            <ArrowLeft size={24} />
            <span>Back</span>
          </button>
          
          <div className="text-center">
            <h2 className="text-lg font-medium">{title}</h2>
            {description && (
              <p className="text-sm text-gray-300">{description}</p>
            )}
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => setShowInfo(!showInfo)}
              className="p-2 hover:text-gray-300 transition-colors"
            >
              <Info size={20} />
            </button>
            <button
              onClick={handleShare}
              className="p-2 hover:text-gray-300 transition-colors"
            >
              <Share2 size={20} />
            </button>
            <button
              onClick={handleDownload}
              className="p-2 hover:text-gray-300 transition-colors"
            >
              <Download size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* VR Content */}
      <div 
        className="w-full h-full relative overflow-hidden cursor-grab active:cursor-grabbing"
        onClick={handleInteraction}
        onTouchStart={handleInteraction}
      >
        <img
          ref={imageRef}
          src={panoramaUrl}
          alt="360 Panorama"
          className="w-full h-full object-cover transition-transform duration-100 ease-out"
          style={{
            transform: `rotate(${rotation}deg) scale(${zoom})`,
            transformOrigin: 'center center'
          }}
          draggable={false}
        />
        
        {/* Loading overlay */}
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
          <div className="text-white text-center">
            <Eye size={48} className="mx-auto mb-4 animate-pulse" />
            <p className="text-lg">Loading VR Tour...</p>
            <p className="text-sm text-gray-300 mt-2">Tap to show controls</p>
          </div>
        </div>
      </div>

      {/* Bottom Controls */}
      <div 
        className={`absolute bottom-0 left-0 right-0 z-20 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300 ${
          showControls ? 'opacity-100' : 'opacity-0'
        }`}
      >
        {/* Main Controls */}
        <div className="flex items-center justify-center space-x-6 text-white mb-4">
          <button
            onClick={handleRotateLeft}
            className="p-3 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
          >
            <RotateCcw size={20} />
          </button>
          
          <button
            onClick={() => setIsPlaying(!isPlaying)}
            className="p-4 bg-blue-600 rounded-full hover:bg-blue-700 transition-colors"
          >
            {isPlaying ? <Pause size={24} /> : <Play size={24} />}
          </button>
          
          <button
            onClick={handleRotateRight}
            className="p-3 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
          >
            <RotateCw size={20} />
          </button>
        </div>

        {/* Secondary Controls */}
        <div className="flex items-center justify-center space-x-4 text-white">
          <button
            onClick={handleZoomOut}
            className="p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
          >
            <ZoomOut size={16} />
          </button>
          
          <div className="flex items-center space-x-2 text-sm">
            <span>Zoom: {Math.round(zoom * 100)}%</span>
          </div>
          
          <button
            onClick={handleZoomIn}
            className="p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
          >
            <ZoomIn size={16} />
          </button>
        </div>

        {/* Instructions */}
        <div className="text-center text-white/70 text-sm mt-4">
          <p>Drag to look around • Pinch to zoom • Tap to show/hide controls</p>
        </div>
      </div>

      {/* Info Panel */}
      {showInfo && (
        <div className="absolute top-20 right-4 bg-black/90 text-white p-4 rounded-lg max-w-xs">
          <h3 className="font-medium mb-2">VR Tour Information</h3>
          <div className="text-sm space-y-1">
            <p><strong>Title:</strong> {title}</p>
            {description && <p><strong>Description:</strong> {description}</p>}
            <p><strong>Controls:</strong></p>
            <ul className="text-xs text-gray-300 ml-2">
              <li>• Tap screen to show/hide controls</li>
              <li>• Use rotation buttons to turn view</li>
              <li>• Play button for auto-rotation</li>
              <li>• Zoom in/out with zoom buttons</li>
              <li>• Share or download the tour</li>
            </ul>
          </div>
          <button
            onClick={() => setShowInfo(false)}
            className="mt-3 text-blue-400 text-sm hover:text-blue-300"
          >
            Close
          </button>
        </div>
      )}

      {/* Gesture hints */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-white/50 text-xs text-center pointer-events-none">
        <div className="bg-black/30 px-3 py-1 rounded-full">
          Swipe to explore • Pinch to zoom
        </div>
      </div>
    </div>
  );
};
