'use client';

import React, { useEffect, useRef, useState } from 'react';
import { ArrowLeft } from 'lucide-react';

interface MobileVRViewerProps {
  panoramaUrl: string;
  title?: string;
  description?: string;
  onClose: () => void;
}

export const MobileVRViewer: React.FC<MobileVRViewerProps> = ({
  panoramaUrl,
  title = "VR720° Mobile Tour",
  description = "Mobile-optimized 720° virtual reality experience",
  onClose
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);


  // A-Frame移动端优化VR场景
  useEffect(() => {
    if (typeof window !== 'undefined' && panoramaUrl && containerRef.current) {
      setIsLoading(true);
      
      // 创建移动端优化的A-Frame VR720场景
      const sceneHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
          <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
          <style>
            body { 
              margin: 0; 
              overflow: hidden; 
              background: #000;
              touch-action: none;
              -webkit-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              user-select: none;
            }
            #vr-scene { 
              width: 100vw; 
              height: 100vh; 
              position: fixed;
              top: 0;
              left: 0;
            }
            .mobile-controls {
              position: fixed;
              top: 10px;
              left: 10px;
              right: 10px;
              z-index: 1000;
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
            .control-group {
              display: flex;
              gap: 8px;
            }
            .mobile-btn {
              background: rgba(0,0,0,0.8);
              color: white;
              border: none;
              padding: 12px;
              border-radius: 50%;
              cursor: pointer;
              backdrop-filter: blur(10px);
              font-size: 14px;
              width: 44px;
              height: 44px;
              display: flex;
              align-items: center;
              justify-content: center;
              touch-action: manipulation;
            }
            .mobile-btn:active {
              background: rgba(0,0,0,0.9);
              transform: scale(0.95);
            }
            .vr720-badge {
              position: fixed;
              bottom: 20px;
              left: 50%;
              transform: translateX(-50%);
              background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
              color: white;
              padding: 8px 20px;
              border-radius: 20px;
              font-weight: bold;
              font-size: 12px;
              z-index: 1000;
              animation: pulse 3s infinite;
            }
            @keyframes pulse {
              0% { transform: translateX(-50%) scale(1); }
              50% { transform: translateX(-50%) scale(1.05); }
              100% { transform: translateX(-50%) scale(1); }
            }
            .mobile-info {
              position: fixed;
              bottom: 60px;
              left: 50%;
              transform: translateX(-50%);
              background: rgba(0,0,0,0.8);
              color: white;
              padding: 8px 16px;
              border-radius: 15px;
              text-align: center;
              backdrop-filter: blur(10px);
              z-index: 1000;
              font-size: 11px;
              max-width: 90vw;
            }
            .loading-overlay {
              position: fixed;
              top: 0;
              left: 0;
              width: 100vw;
              height: 100vh;
              background: #000;
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 2000;
              flex-direction: column;
            }
            .spinner {
              width: 40px;
              height: 40px;
              border: 3px solid rgba(255,255,255,0.3);
              border-top: 3px solid #4ecdc4;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-bottom: 20px;
            }
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        </head>
        <body>
          <div class="mobile-controls">
            <div class="control-group">
              <button class="mobile-btn" onclick="window.parent.postMessage('back', '*')" title="返回">
                ←
              </button>
              <button class="mobile-btn" onclick="toggleInfo()" title="信息">
                ℹ️
              </button>
            </div>
            <div class="control-group">
              <button class="mobile-btn" onclick="toggle720Mode()" title="720°模式">
                🔄
              </button>
              <button class="mobile-btn" onclick="window.parent.postMessage('share', '*')" title="分享">
                📤
              </button>
            </div>
          </div>
          
          <div class="vr720-badge" id="mode-badge">
            📱 VR720° MOBILE
          </div>
          
          <a-scene 
            id="vr-scene" 
            embedded 
            style="height: 100vh; width: 100vw;"
            vr-mode-ui="enabled: true"
            background="color: #000"
            device-orientation-permission-ui="enabled: false"
          >
            <a-assets>
              <img id="panorama" src="${panoramaUrl}" crossorigin="anonymous">
            </a-assets>
            
            <!-- 移动端优化的720°天空球 -->
            <a-sky 
              id="mobile-sky"
              src="#panorama" 
              rotation="0 -90 0"
              animation="property: rotation; to: 0 630 0; loop: true; dur: 150000; easing: linear"
            ></a-sky>
            
            <!-- 移动端VR相机 -->
            <a-camera 
              id="mobile-camera"
              look-controls="enabled: true; reverseMouseDrag: false; touchEnabled: true; magicWindowTrackingEnabled: true"
              wasd-controls="enabled: false"
              position="0 1.6 0"
              fov="80"
            >
              <a-cursor 
                color="#4ecdc4" 
                opacity="0.8"
                geometry="primitive: ring; radiusInner: 0.015; radiusOuter: 0.025"
                material="color: #4ecdc4; shader: flat"
                animation="property: scale; to: 1.3 1.3 1.3; loop: true; dir: alternate; dur: 1500"
              ></a-cursor>
            </a-camera>
            
            <!-- 环境光 -->
            <a-light type="ambient" color="#505050"></a-light>
            <a-light type="directional" position="1 1 1" color="#ffffff" intensity="0.3"></a-light>
            
            <!-- 移动端标题 -->
            <a-text
              value="${title}"
              position="0 3 -3"
              align="center"
              color="#4ecdc4"
              opacity="0.9"
              font="size: 24; weight: bold"
              animation="property: opacity; to: 0.6; loop: true; dir: alternate; dur: 4000"
            ></a-text>
            
            <!-- 720°移动端指示器 -->
            <a-ring
              position="0 -1.5 -2"
              color="#ff6b6b"
              radius-inner="0.3"
              radius-outer="0.4"
              opacity="0.8"
              animation="property: rotation; to: 0 0 720; loop: true; dur: 12000; easing: linear"
            ></a-ring>
            
            <a-text
              value="720° Mobile VR"
              position="0 -2 -2"
              align="center"
              color="#ff6b6b"
              font="size: 14; weight: bold"
              opacity="0.9"
            ></a-text>
          </a-scene>
          
          <div class="mobile-info" id="info-panel" style="display: none;">
            <div style="font-weight: bold; margin-bottom: 4px;">${title}</div>
            <div style="font-size: 10px; opacity: 0.8;">${description}</div>
            <div style="font-size: 10px; margin-top: 4px; color: #4ecdc4;">
              👆 拖拽查看 • 📱 倾斜设备 • 🥽 VR模式
            </div>
          </div>

          <div class="loading-overlay" id="loading" style="display: flex;">
            <div class="spinner"></div>
            <div style="color: white; text-align: center;">
              <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px;">Loading VR720°</div>
              <div style="font-size: 12px; opacity: 0.8;">Mobile VR Experience</div>
            </div>
          </div>

          <script>
            let is720Mode = true;
            let showInfo = false;
            
            function toggle720Mode() {
              const sky = document.querySelector('#mobile-sky');
              const badge = document.querySelector('#mode-badge');
              
              if (is720Mode) {
                // 切换到360°模式
                sky.setAttribute('animation', 'property: rotation; to: 0 270 0; loop: true; dur: 80000; easing: linear');
                badge.textContent = '📱 VR360° MOBILE';
                badge.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                is720Mode = false;
              } else {
                // 切换到720°模式
                sky.setAttribute('animation', 'property: rotation; to: 0 630 0; loop: true; dur: 150000; easing: linear');
                badge.textContent = '📱 VR720° MOBILE';
                badge.style.background = 'linear-gradient(45deg, #ff6b6b, #4ecdc4)';
                is720Mode = true;
              }
            }
            
            function toggleInfo() {
              const panel = document.querySelector('#info-panel');
              showInfo = !showInfo;
              panel.style.display = showInfo ? 'block' : 'none';
            }
            
            // 监听父窗口消息
            window.addEventListener('message', function(event) {
              if (event.data === 'back') {
                window.parent.postMessage('back', '*');
              } else if (event.data === 'share') {
                window.parent.postMessage('share', '*');
              } else if (event.data === 'toggleInfo') {
                toggleInfo();
              } else if (event.data === 'toggle720') {
                toggle720Mode();
              }
            });
            
            // 移动端优化
            document.addEventListener('touchstart', function(e) {
              // 阻止默认的触摸行为
              if (e.target.closest('.mobile-btn')) {
                e.preventDefault();
              }
            }, { passive: false });
            
            // 隐藏加载界面
            setTimeout(() => {
              document.querySelector('#loading').style.display = 'none';
            }, 3000);
            
            // 设备方向变化处理
            window.addEventListener('orientationchange', function() {
              setTimeout(() => {
                const scene = document.querySelector('a-scene');
                if (scene) {
                  scene.resize();
                }
              }, 500);
            });
            
            // 移动端性能优化
            if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
              // 移动设备特定优化
              const scene = document.querySelector('a-scene');
              scene.setAttribute('renderer', 'antialias: false; colorManagement: true; sortObjects: true; physicallyCorrectLights: true; maxCanvasWidth: 1920; maxCanvasHeight: 1920');
            }
          </script>
        </body>
        </html>
      `;

      // 创建iframe - 修复按钮点击问题
      const iframe = document.createElement('iframe');
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
      iframe.style.position = 'fixed';
      iframe.style.top = '0';
      iframe.style.left = '0';
      iframe.style.zIndex = '100'; // 降低z-index，让外部按钮可以点击
      iframe.style.pointerEvents = 'auto';
      iframe.srcdoc = sceneHTML;

      // 监听iframe加载完成
      iframe.onload = () => {
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
      };
      
      // 监听iframe消息
      const handleMessage = (event: MessageEvent) => {
        if (event.data === 'back') {
          onClose();
        } else if (event.data === 'share') {
          // 移动端分享功能
          if (navigator.share) {
            navigator.share({
              title: title,
              text: description,
              url: window.location.href
            }).catch(console.error);
          } else {
            // 复制链接到剪贴板
            navigator.clipboard?.writeText(window.location.href).then(() => {
              alert('链接已复制到剪贴板！');
            }).catch(() => {
              alert('分享功能不可用');
            });
          }
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      containerRef.current.appendChild(iframe);
      
      return () => {
        window.removeEventListener('message', handleMessage);
        if (containerRef.current && iframe) {
          containerRef.current.removeChild(iframe);
        }
      };
    }
  }, [panoramaUrl, title, description, onClose]);



  return (
    <div className="fixed inset-0 bg-black z-50 overflow-hidden">
      <div ref={containerRef} className="w-full h-full" />

      {/* 移动端加载指示器 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black z-50">
          <div className="text-white text-center">
            <div className="relative mb-6">
              <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-gradient-to-r from-pink-500 to-blue-500 mx-auto"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-2xl">📱</div>
              </div>
            </div>
            <p className="text-lg font-bold bg-gradient-to-r from-pink-500 to-blue-500 bg-clip-text text-transparent">
              Loading Mobile VR720°
            </p>
            <p className="text-sm text-gray-400 mt-2">Optimized for mobile devices</p>
            <div className="mt-4 flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-pink-500 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
            <div className="mt-4 text-xs text-gray-500">
              Touch • Gyroscope • VR Ready
            </div>
          </div>
        </div>
      )}

      {/* 移动端控制按钮层 - 确保在iframe之上 */}
      <div className="absolute inset-0 pointer-events-none z-50">
        {/* 顶部控制栏 */}
        <div className="absolute top-0 left-0 right-0 p-4 pointer-events-none">
          <div className="flex justify-between items-center">
            {/* 左侧按钮组 */}
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="pointer-events-auto bg-black/80 text-white p-3 rounded-full backdrop-blur-sm shadow-lg active:scale-95 transition-transform"
                style={{ minWidth: '48px', minHeight: '48px' }}
              >
                <ArrowLeft size={20} />
              </button>
              <button
                onClick={() => {
                  // 发送信息切换消息到iframe
                  const iframe = containerRef.current?.querySelector('iframe');
                  if (iframe?.contentWindow) {
                    iframe.contentWindow.postMessage('toggleInfo', window.location.origin);
                  }
                }}
                className="pointer-events-auto bg-black/80 text-white p-3 rounded-full backdrop-blur-sm shadow-lg active:scale-95 transition-transform"
                style={{ minWidth: '48px', minHeight: '48px' }}
              >
                ℹ️
              </button>
            </div>

            {/* 右侧按钮组 */}
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  // 发送720°模式切换消息到iframe
                  const iframe = containerRef.current?.querySelector('iframe');
                  if (iframe?.contentWindow) {
                    iframe.contentWindow.postMessage('toggle720', window.location.origin);
                  }
                }}
                className="pointer-events-auto bg-black/80 text-white p-3 rounded-full backdrop-blur-sm shadow-lg active:scale-95 transition-transform"
                style={{ minWidth: '48px', minHeight: '48px' }}
              >
                🔄
              </button>
              <button
                onClick={() => {
                  // 移动端分享功能
                  if (navigator.share) {
                    navigator.share({
                      title: title,
                      text: description,
                      url: window.location.href
                    }).catch(console.error);
                  } else {
                    navigator.clipboard?.writeText(window.location.href).then(() => {
                      alert('链接已复制到剪贴板！');
                    }).catch(() => {
                      alert('分享功能不可用');
                    });
                  }
                }}
                className="pointer-events-auto bg-black/80 text-white p-3 rounded-full backdrop-blur-sm shadow-lg active:scale-95 transition-transform"
                style={{ minWidth: '48px', minHeight: '48px' }}
              >
                📤
              </button>
            </div>
          </div>
        </div>

        {/* 底部状态指示器 */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 pointer-events-none">
          <div className="bg-black/80 text-white px-4 py-2 rounded-full backdrop-blur-sm text-sm">
            📱 VR720° Mobile - 拖拽查看全景
          </div>
        </div>
      </div>
    </div>
  );
};
