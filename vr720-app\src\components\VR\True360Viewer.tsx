'use client';

import React, { useEffect, useRef, useState } from 'react';

interface True360ViewerProps {
  panoramaUrl: string;
  title?: string;
  description?: string;
  onClose: () => void;
}

export const True360Viewer: React.FC<True360ViewerProps> = ({
  panoramaUrl,
  title = "VR720° Tour",
  description = "Immersive 720° virtual reality experience",
  onClose
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);

  // A-Frame VR场景初始化
  useEffect(() => {
    if (typeof window !== 'undefined' && panoramaUrl && containerRef.current) {
      setIsLoading(true);

      // 创建A-Frame VR720场景
      const sceneHTML = `
        <!DOCTYPE html>
        <html>
        <head>
          <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
          <style>
            body {
              margin: 0;
              overflow: hidden;
              background: #000;
            }
            #vr-scene {
              width: 100vw;
              height: 100vh;
            }
            .controls {
              position: fixed;
              top: 20px;
              left: 20px;
              z-index: 1000;
              display: flex;
              gap: 10px;
            }
            .control-btn {
              background: rgba(0,0,0,0.7);
              color: white;
              border: none;
              padding: 12px;
              border-radius: 50%;
              cursor: pointer;
              backdrop-filter: blur(10px);
              font-size: 16px;
              width: 48px;
              height: 48px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .control-btn:hover {
              background: rgba(0,0,0,0.9);
              transform: scale(1.1);
            }
            .info {
              position: fixed;
              bottom: 20px;
              left: 50%;
              transform: translateX(-50%);
              background: rgba(0,0,0,0.8);
              color: white;
              padding: 15px 25px;
              border-radius: 25px;
              text-align: center;
              backdrop-filter: blur(10px);
              z-index: 1000;
            }
            .vr720-badge {
              position: fixed;
              top: 20px;
              right: 20px;
              background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
              color: white;
              padding: 8px 16px;
              border-radius: 20px;
              font-weight: bold;
              font-size: 14px;
              z-index: 1000;
              animation: pulse 2s infinite;
            }
            @keyframes pulse {
              0% { transform: scale(1); }
              50% { transform: scale(1.05); }
              100% { transform: scale(1); }
            }
          </style>
        </head>
        <body>
          <div class="controls">
            <button class="control-btn" onclick="window.parent.postMessage('back', '*')" title="返回">
              ←
            </button>
            <button class="control-btn" onclick="window.parent.postMessage('share', '*')" title="分享">
              📤
            </button>
            <button class="control-btn" onclick="toggleFullscreen()" title="全屏">
              ⛶
            </button>
            <button class="control-btn" onclick="toggle720Mode()" title="切换720°模式">
              🔄
            </button>
          </div>

          <div class="vr720-badge">
            VR720° MODE
          </div>

          <a-scene
            id="vr-scene"
            embedded
            style="height: 100vh; width: 100vw;"
            vr-mode-ui="enabled: true"
            background="color: #000"
          >
            <a-assets>
              <img id="panorama" src="${panoramaUrl}" crossorigin="anonymous">
            </a-assets>

            <!-- 720°天空球 - 支持双重360°循环 -->
            <a-sky
              id="sky720"
              src="#panorama"
              rotation="0 -130 0"
              animation="property: rotation; to: 0 590 0; loop: true; dur: 120000; easing: linear"
            ></a-sky>

            <!-- VR720相机 -->
            <a-camera
              id="camera720"
              look-controls="enabled: true; reverseMouseDrag: false; touchEnabled: true"
              wasd-controls="enabled: false"
              position="0 1.6 0"
              fov="75"
            >
              <a-cursor
                color="white"
                opacity="0.7"
                geometry="primitive: ring; radiusInner: 0.02; radiusOuter: 0.03"
                material="color: #4ecdc4; shader: flat"
                animation="property: scale; to: 1.2 1.2 1.2; loop: true; dir: alternate; dur: 1000"
              ></a-cursor>
            </a-camera>

            <!-- 环境光 -->
            <a-light type="ambient" color="#404040"></a-light>
            <a-light type="directional" position="0 1 1" color="#ffffff" intensity="0.5"></a-light>

            <!-- VR720标题 -->
            <a-text
              value="${title}"
              position="0 3.5 -4"
              align="center"
              color="#4ecdc4"
              opacity="0.9"
              font="size: 32; weight: bold"
              animation="property: opacity; to: 0.5; loop: true; dir: alternate; dur: 3000"
            ></a-text>

            <a-text
              value="${description}"
              position="0 3 -4"
              align="center"
              color="white"
              opacity="0.7"
              font="size: 18"
            ></a-text>

            <!-- 720°指示器 -->
            <a-torus
              position="0 -2 -3"
              color="#ff6b6b"
              radius="0.5"
              radius-tubular="0.05"
              animation="property: rotation; to: 0 720 0; loop: true; dur: 10000; easing: linear"
            ></a-torus>

            <a-text
              value="720° VR Experience"
              position="0 -2.5 -3"
              align="center"
              color="#ff6b6b"
              font="size: 16; weight: bold"
            ></a-text>
          </a-scene>

          <div class="info">
            <div style="font-size: 16px; margin-bottom: 5px; font-weight: bold;">🌟 VR720° 全景模式</div>
            <div style="font-size: 12px; opacity: 0.9;">拖拽查看720度全景 • 点击VR图标进入VR模式 • 支持双重360°循环</div>
          </div>

          <script>
            let is720Mode = true;
            let autoRotate = true;

            function toggleFullscreen() {
              if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
              } else {
                document.exitFullscreen();
              }
            }

            function toggle720Mode() {
              const sky = document.querySelector('#sky720');
              const badge = document.querySelector('.vr720-badge');

              if (is720Mode) {
                // 切换到360°模式
                sky.setAttribute('animation', 'property: rotation; to: 0 230 0; loop: true; dur: 60000; easing: linear');
                badge.textContent = 'VR360° MODE';
                badge.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                is720Mode = false;
              } else {
                // 切换到720°模式
                sky.setAttribute('animation', 'property: rotation; to: 0 590 0; loop: true; dur: 120000; easing: linear');
                badge.textContent = 'VR720° MODE';
                badge.style.background = 'linear-gradient(45deg, #ff6b6b, #4ecdc4)';
                is720Mode = true;
              }
            }

            // 监听父窗口消息
            window.addEventListener('message', function(event) {
              if (event.data === 'back') {
                window.parent.postMessage('back', '*');
              } else if (event.data === 'share') {
                window.parent.postMessage('share', '*');
              }
            });

            // 交互控制
            document.addEventListener('click', function() {
              const sky = document.querySelector('#sky720');
              if (autoRotate) {
                sky.removeAttribute('animation');
                autoRotate = false;
              }
            });

            // 键盘控制增强
            document.addEventListener('keydown', function(event) {
              const camera = document.querySelector('#camera720');
              const currentRotation = camera.getAttribute('rotation');

              switch(event.key) {
                case 'ArrowLeft':
                  camera.setAttribute('rotation', {
                    x: currentRotation.x,
                    y: currentRotation.y - 10,
                    z: currentRotation.z
                  });
                  break;
                case 'ArrowRight':
                  camera.setAttribute('rotation', {
                    x: currentRotation.x,
                    y: currentRotation.y + 10,
                    z: currentRotation.z
                  });
                  break;
                case 'ArrowUp':
                  camera.setAttribute('rotation', {
                    x: Math.max(currentRotation.x - 10, -90),
                    y: currentRotation.y,
                    z: currentRotation.z
                  });
                  break;
                case 'ArrowDown':
                  camera.setAttribute('rotation', {
                    x: Math.min(currentRotation.x + 10, 90),
                    y: currentRotation.y,
                    z: currentRotation.z
                  });
                  break;
                case 'r':
                  camera.setAttribute('rotation', '0 0 0');
                  break;
                case '7':
                  toggle720Mode();
                  break;
              }
            });

            // VR720特效
            setInterval(() => {
              const torus = document.querySelector('a-torus');
              if (torus && is720Mode) {
                const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#f9ca24', '#f0932b'];
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                torus.setAttribute('color', randomColor);
              }
            }, 3000);
          </script>
        </body>
        </html>
      `;

      // 创建iframe
      const iframe = document.createElement('iframe');
      iframe.style.width = '100%';
      iframe.style.height = '100%';
      iframe.style.border = 'none';
      iframe.srcdoc = sceneHTML;

      // 监听iframe加载完成
      iframe.onload = () => {
        setTimeout(() => {
          setIsLoading(false);
        }, 2000);
      };

      // 监听iframe消息
      const handleMessage = (event: MessageEvent) => {
        if (event.data === 'back') {
          onClose();
        } else if (event.data === 'share') {
          // 分享功能
          if (navigator.share) {
            navigator.share({
              title: title,
              text: description,
              url: window.location.href
            });
          }
        }
      };

      window.addEventListener('message', handleMessage);

      containerRef.current.appendChild(iframe);

      return () => {
        window.removeEventListener('message', handleMessage);
        if (containerRef.current && iframe) {
          containerRef.current.removeChild(iframe);
        }
      };
    }
  }, [panoramaUrl, title, description, onClose]);

  return (
    <div className="w-full h-screen bg-black relative overflow-hidden">
      <div ref={containerRef} className="w-full h-full" />

      {/* 加载指示器 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/90 z-50">
          <div className="text-white text-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-20 w-20 border-b-4 border-gradient-to-r from-pink-500 to-blue-500 mx-auto mb-6"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-2xl">🌟</div>
              </div>
            </div>
            <p className="text-xl font-bold bg-gradient-to-r from-pink-500 to-blue-500 bg-clip-text text-transparent">
              正在加载VR720°全景...
            </p>
            <p className="text-sm text-gray-400 mt-2">请稍候，正在初始化720°VR环境</p>
            <div className="mt-6 flex items-center justify-center space-x-2">
              <div className="w-3 h-3 bg-pink-500 rounded-full animate-pulse"></div>
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
            <div className="mt-4 text-xs text-gray-500">
              支持720°双重循环 • WebXR VR模式 • 沉浸式体验
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
