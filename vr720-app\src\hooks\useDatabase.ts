// 数据库操作的React Hook

import { useState, useEffect, useCallback } from 'react';
import { House, Image, Room } from '@/lib/database/types';

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 房屋相关Hook
export const useHouses = () => {
  const [houses, setHouses] = useState<House[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取房屋列表
  const fetchHouses = useCallback(async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    propertyType?: string;
    isPublic?: boolean;
    userId?: string;
  }) => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.set('page', params.page.toString());
      if (params?.limit) searchParams.set('limit', params.limit.toString());
      if (params?.search) searchParams.set('search', params.search);
      if (params?.status) searchParams.set('status', params.status);
      if (params?.propertyType) searchParams.set('propertyType', params.propertyType);
      if (params?.isPublic !== undefined) searchParams.set('isPublic', params.isPublic.toString());
      if (params?.userId) searchParams.set('userId', params.userId);

      const response = await fetch(`/api/houses?${searchParams}`);
      const result: ApiResponse<House[]> = await response.json();

      if (result.success && result.data) {
        setHouses(result.data);
      } else {
        setError(result.error || 'Failed to fetch houses');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取单个房屋
  const fetchHouse = useCallback(async (id: string, includeRooms = true) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (includeRooms) params.set('includeRooms', 'true');
      params.set('incrementView', 'true');

      const response = await fetch(`/api/houses/${id}?${params}`);
      const result: ApiResponse<House & { rooms?: Room[] }> = await response.json();

      if (result.success && result.data) {
        return result.data;
      } else {
        setError(result.error || 'Failed to fetch house');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // 创建房屋
  const createHouse = useCallback(async (houseData: Partial<House> & { rooms?: Partial<Room>[] }) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/houses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(houseData),
      });

      const result: ApiResponse<House> = await response.json();

      if (result.success && result.data) {
        setHouses(prev => [result.data!, ...prev]);
        return result.data;
      } else {
        setError(result.error || 'Failed to create house');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // 更新房屋
  const updateHouse = useCallback(async (id: string, updates: Partial<House> & { rooms?: Partial<Room>[] }) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/houses/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      const result: ApiResponse<House> = await response.json();

      if (result.success && result.data) {
        setHouses(prev => prev.map(house => 
          house.id === id ? result.data! : house
        ));
        return result.data;
      } else {
        setError(result.error || 'Failed to update house');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // 删除房屋
  const deleteHouse = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/houses/${id}`, {
        method: 'DELETE',
      });

      const result: ApiResponse<any> = await response.json();

      if (result.success) {
        setHouses(prev => prev.filter(house => house.id !== id));
        return true;
      } else {
        setError(result.error || 'Failed to delete house');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    houses,
    loading,
    error,
    fetchHouses,
    fetchHouse,
    createHouse,
    updateHouse,
    deleteHouse,
    clearError: () => setError(null)
  };
};

// 图片相关Hook
export const useImages = () => {
  const [images, setImages] = useState<Image[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取图片列表
  const fetchImages = useCallback(async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    tags?: string;
    mimeType?: string;
    isPublic?: boolean;
    userId?: string;
    panoramicOnly?: boolean;
  }) => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.set('page', params.page.toString());
      if (params?.limit) searchParams.set('limit', params.limit.toString());
      if (params?.search) searchParams.set('search', params.search);
      if (params?.tags) searchParams.set('tags', params.tags);
      if (params?.mimeType) searchParams.set('mimeType', params.mimeType);
      if (params?.isPublic !== undefined) searchParams.set('isPublic', params.isPublic.toString());
      if (params?.userId) searchParams.set('userId', params.userId);
      if (params?.panoramicOnly) searchParams.set('panoramicOnly', 'true');

      const response = await fetch(`/api/images?${searchParams}`);
      const result: ApiResponse<Image[]> = await response.json();

      if (result.success && result.data) {
        setImages(result.data);
        return result.data;
      } else {
        setError(result.error || 'Failed to fetch images');
        return [];
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // 上传图片
  const uploadImage = useCallback(async (file: File, options?: {
    userId?: string;
    tags?: string[];
    isPublic?: boolean;
  }) => {
    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      if (options?.userId) formData.append('userId', options.userId);
      if (options?.tags) formData.append('tags', options.tags.join(','));
      if (options?.isPublic !== undefined) formData.append('isPublic', options.isPublic.toString());

      const response = await fetch('/api/images', {
        method: 'POST',
        body: formData,
      });

      const result: ApiResponse<Image> = await response.json();

      if (result.success && result.data) {
        setImages(prev => [result.data!, ...prev]);
        return result.data;
      } else {
        setError(result.error || 'Failed to upload image');
        return null;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // 批量上传图片
  const uploadImages = useCallback(async (files: File[], options?: {
    userId?: string;
    tags?: string[];
    isPublic?: boolean;
  }) => {
    const uploadedImages: Image[] = [];
    const errors: string[] = [];

    for (const file of files) {
      try {
        const image = await uploadImage(file, options);
        if (image) {
          uploadedImages.push(image);
        }
      } catch (err) {
        errors.push(`Failed to upload ${file.name}: ${err instanceof Error ? err.message : 'Unknown error'}`);
      }
    }

    if (errors.length > 0) {
      setError(errors.join('; '));
    }

    return uploadedImages;
  }, [uploadImage]);

  // 删除图片
  const deleteImage = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/images?ids=${id}`, {
        method: 'DELETE',
      });

      const result: ApiResponse<any> = await response.json();

      if (result.success) {
        setImages(prev => prev.filter(image => image.id !== id));
        return true;
      } else {
        setError(result.error || 'Failed to delete image');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    images,
    loading,
    error,
    fetchImages,
    uploadImage,
    uploadImages,
    deleteImage,
    clearError: () => setError(null)
  };
};

// 组合Hook - 用于房屋配置管理
export const useHouseConfiguration = () => {
  const { 
    houses, 
    loading: housesLoading, 
    error: housesError, 
    createHouse, 
    updateHouse, 
    fetchHouse 
  } = useHouses();
  
  const { 
    images, 
    loading: imagesLoading, 
    error: imagesError, 
    fetchImages 
  } = useImages();

  const loading = housesLoading || imagesLoading;
  const error = housesError || imagesError;

  // 保存房屋配置
  const saveHouseConfiguration = useCallback(async (houseData: any) => {
    if (houseData.id) {
      return await updateHouse(houseData.id, houseData);
    } else {
      return await createHouse(houseData);
    }
  }, [createHouse, updateHouse]);

  // 获取用户的全景图片
  const fetchUserPanoramicImages = useCallback(async (userId?: string) => {
    return await fetchImages({
      userId,
      panoramicOnly: true,
      limit: 100
    });
  }, [fetchImages]);

  return {
    houses,
    images,
    loading,
    error,
    saveHouseConfiguration,
    fetchHouse,
    fetchUserPanoramicImages,
    clearError: () => {
      // 清除所有错误
    }
  };
};
