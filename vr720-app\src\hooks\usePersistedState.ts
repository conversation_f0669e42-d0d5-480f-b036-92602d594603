import { useState, useEffect, useCallback } from 'react';
import {
  initializeStorage
} from '@/utils/localStorage';
import {
  indexedDBStorage
} from '@/utils/indexedDBStorage';
import { autoMigrateIfNeeded } from '@/utils/storageMigration';

// 将File对象转换为base64字符串
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

// 将base64字符串转换为Blob URL
const base64ToBlobUrl = (base64: string): string => {
  try {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/jpeg' });
    
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('Failed to convert base64 to blob URL:', error);
    return base64; // 回退到原始base64
  }
};

// 上传图片的接口（与原来的UploadedImage兼容）
export interface UploadedImage {
  id: string;
  file: File;
  url: string;
  name: string;
  size: number;
  type: string;
  uploadTime: number;
  isProcessing?: boolean;
  metadata?: {
    width?: number;
    height?: number;
    aspectRatio?: number;
  };
}

// 持久化图片状态的Hook
export const usePersistedImages = () => {
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 初始化：从IndexedDB加载图片
  useEffect(() => {
    const loadStoredImages = async () => {
      try {
        setIsLoading(true);

        // 初始化存储
        initializeStorage();

        // 自动迁移localStorage数据到IndexedDB（如果需要）
        await autoMigrateIfNeeded();

        // 从IndexedDB加载图片
        const storedImages = await indexedDBStorage.getAllImages();
        const loadedImages: UploadedImage[] = [];

        for (const storedImage of storedImages) {
          try {
            // 将存储的Blob转换为URL
            const blobUrl = URL.createObjectURL(storedImage.blob);

            // 创建一个虚拟的File对象（用于兼容性）
            const virtualFile = new File([storedImage.blob], storedImage.name, {
              type: storedImage.type,
              lastModified: storedImage.uploadTime
            });

            const uploadedImage: UploadedImage = {
              id: storedImage.id,
              file: virtualFile,
              url: blobUrl,
              name: storedImage.name,
              size: storedImage.size,
              type: storedImage.type,
              uploadTime: storedImage.uploadTime,
              metadata: storedImage.metadata
            };

            loadedImages.push(uploadedImage);
          } catch (imageError) {
            console.error(`Failed to load image ${storedImage.id}:`, imageError);
          }
        }

        setImages(loadedImages);
        setError(null);
      } catch (err) {
        console.error('Failed to load stored images:', err);
        setError('Failed to load saved images');
      } finally {
        setIsLoading(false);
      }
    };

    loadStoredImages();
  }, []);

  // 添加新图片
  const addImages = useCallback(async (newImages: UploadedImage[]) => {
    try {
      const updatedImages = [...images];

      for (const newImage of newImages) {
        // 将File转换为Blob进行存储
        const blob = new Blob([newImage.file], { type: newImage.file.type });

        const storedImageData = {
          id: newImage.id,
          name: newImage.name,
          size: newImage.size,
          type: newImage.type,
          uploadTime: newImage.uploadTime,
          blob: blob,
          metadata: newImage.metadata
        };

        // 保存到IndexedDB
        const saved = await indexedDBStorage.saveImage(storedImageData);

        if (saved) {
          updatedImages.push(newImage);
        } else {
          console.error(`Failed to save image ${newImage.id} to storage`);
          setError('Failed to save image, storage may be full');
        }
      }

      setImages(updatedImages);
      return true;
    } catch (err) {
      console.error('Failed to add images:', err);
      setError('Failed to add images');
      return false;
    }
  }, [images]);

  // 删除图片
  const removeImage = useCallback(async (imageId: string) => {
    try {
      // 从IndexedDB删除
      const removed = await indexedDBStorage.removeImage(imageId);

      if (removed) {
        // 从状态中删除
        setImages(prev => {
          const imageToRemove = prev.find(img => img.id === imageId);
          if (imageToRemove) {
            // 释放Blob URL
            URL.revokeObjectURL(imageToRemove.url);
          }
          return prev.filter(img => img.id !== imageId);
        });
        return true;
      } else {
        setError('Failed to delete image');
        return false;
      }
    } catch (err) {
      console.error('Failed to remove image:', err);
      setError('Failed to delete image');
      return false;
    }
  }, []);

  // 清空所有图片
  const clearImages = useCallback(async () => {
    try {
      const cleared = await indexedDBStorage.clearAllImages();

      if (cleared) {
        // 释放所有Blob URLs
        images.forEach(img => {
          URL.revokeObjectURL(img.url);
        });

        setImages([]);
        return true;
      } else {
        setError('Failed to clear images');
        return false;
      }
    } catch (err) {
      console.error('Failed to clear images:', err);
      setError('Failed to clear images');
      return false;
    }
  }, [images]);

  // 获取图片统计信息
  const getImageStats = useCallback(() => {
    const totalSize = images.reduce((sum, img) => sum + img.size, 0);
    const panoramicCount = images.filter(img =>
      (img.metadata?.aspectRatio ?? 1) > 1.8
    ).length;
    const regularCount = images.length - panoramicCount;
    
    return {
      total: images.length,
      panoramic: panoramicCount,
      regular: regularCount,
      totalSize,
      averageSize: images.length > 0 ? totalSize / images.length : 0
    };
  }, [images]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    images,
    isLoading,
    error,
    addImages,
    removeImage,
    clearImages,
    getImageStats,
    clearError
  };
};

// 用户偏好设置的Hook
export const useUserPreferences = () => {
  const [preferences, setPreferences] = useState(() => {
    if (typeof window !== 'undefined') {
      const { getUserPreferences } = require('@/utils/localStorage');
      return getUserPreferences();
    }
    return {
      theme: 'auto' as const,
      defaultUploadQuality: 0.9,
      autoSave: true,
      showTutorials: true,
      language: 'zh-CN'
    };
  });

  const updatePreferences = useCallback((updates: Partial<typeof preferences>) => {
    const newPreferences = { ...preferences, ...updates };
    setPreferences(newPreferences);
    
    if (typeof window !== 'undefined') {
      const { saveUserPreferences } = require('@/utils/localStorage');
      saveUserPreferences(updates);
    }
  }, [preferences]);

  return {
    preferences,
    updatePreferences
  };
};

// 存储使用情况的Hook
export const useStorageUsage = () => {
  const [usage, setUsage] = useState({ used: 0, total: 0, percentage: 0 });

  const updateUsage = useCallback(() => {
    if (typeof window !== 'undefined') {
      const { getStorageUsage } = require('@/utils/localStorage');
      setUsage(getStorageUsage());
    }
  }, []);

  useEffect(() => {
    updateUsage();
    
    // 每30秒更新一次使用情况
    const interval = setInterval(updateUsage, 30000);
    
    return () => clearInterval(interval);
  }, [updateUsage]);

  return {
    usage,
    updateUsage
  };
};
