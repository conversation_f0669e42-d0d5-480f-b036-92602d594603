// Database factory - supports multiple database types

import { DatabaseService, DatabaseConfig } from './types';
import { SQLiteDatabaseService } from './sqlite/service';

// Database factory class
export class DatabaseFactory {
  private static instance: DatabaseService | null = null;
  private static config: DatabaseConfig | null = null;

  // Create database service instance
  static async create(config: DatabaseConfig): Promise<DatabaseService> {
    DatabaseFactory.config = config;

    switch (config.type) {
      case 'sqlite': {
        const sqliteService = new SQLiteDatabaseService(config);
        await sqliteService.initialize();
        return sqliteService;
      }

      case 'mysql':
        // TODO: Implement MySQL service
        throw new Error('MySQL support not implemented yet');

      case 'postgresql':
        // TODO: Implement PostgreSQL service
        throw new Error('PostgreSQL support not implemented yet');

      case 'mongodb':
        // TODO: Implement MongoDB service
        throw new Error('MongoDB support not implemented yet');

      default:
        throw new Error(`Unsupported database type: ${config.type}`);
    }
  }

  // Get singleton instance
  static async getInstance(config?: DatabaseConfig): Promise<DatabaseService> {
    if (!DatabaseFactory.instance) {
      if (!config) {
        throw new Error('Database config required for first initialization');
      }
      DatabaseFactory.instance = await DatabaseFactory.create(config);
    }
    return DatabaseFactory.instance;
  }

  // Reset instance (for testing or reconfiguration)
  static async reset(config?: DatabaseConfig): Promise<void> {
    if (DatabaseFactory.instance) {
      await DatabaseFactory.instance.close();
      DatabaseFactory.instance = null;
    }
    if (config) {
      DatabaseFactory.instance = await DatabaseFactory.create(config);
    }
  }

  // Close database connection
  static async close(): Promise<void> {
    if (DatabaseFactory.instance) {
      await DatabaseFactory.instance.close();
      DatabaseFactory.instance = null;
    }
  }

  // Get current configuration
  static getConfig(): DatabaseConfig | null {
    return DatabaseFactory.config;
  }
}

// 默认配置
export const getDefaultConfig = (): DatabaseConfig => {
  const env = process.env.NODE_ENV || 'development';
  
  return {
    type: 'sqlite',
    connectionString: process.env.DATABASE_URL || `./data/vr720_${env}.db`,
    options: {
      verbose: env === 'development' ? console.log : undefined
    }
  };
};

// 从环境变量获取配置
export const getConfigFromEnv = (): DatabaseConfig => {
  const dbType = (process.env.DB_TYPE || 'sqlite') as DatabaseConfig['type'];
  
  switch (dbType) {
    case 'sqlite':
      return {
        type: 'sqlite',
        connectionString: process.env.DATABASE_URL || getDefaultConfig().connectionString,
        options: {
          verbose: process.env.NODE_ENV === 'development' ? console.log : undefined
        }
      };

    case 'mysql':
      return {
        type: 'mysql',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '3306'),
        database: process.env.DB_NAME || 'vr720',
        username: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        options: {
          charset: 'utf8mb4',
          timezone: '+00:00'
        }
      };

    case 'postgresql':
      return {
        type: 'postgresql',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        database: process.env.DB_NAME || 'vr720',
        username: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        options: {
          ssl: process.env.DB_SSL === 'true'
        }
      };

    case 'mongodb':
      return {
        type: 'mongodb',
        connectionString: process.env.DATABASE_URL || 'mongodb://localhost:27017/vr720',
        options: {
          useNewUrlParser: true,
          useUnifiedTopology: true
        }
      };

    default:
      return getDefaultConfig();
  }
};

// 测试配置
export const getTestConfig = (): DatabaseConfig => {
  return {
    type: 'sqlite',
    connectionString: ':memory:', // 内存数据库，用于测试
    options: {
      verbose: undefined // 测试时不输出SQL
    }
  };
};

// 生产环境配置验证
export const validateProductionConfig = (config: DatabaseConfig): void => {
  if (process.env.NODE_ENV === 'production') {
    if (config.type === 'sqlite' && config.connectionString === ':memory:') {
      throw new Error('Memory database not allowed in production');
    }

    if (config.type !== 'sqlite') {
      if (!config.host || !config.database || !config.username) {
        throw new Error('Database host, database name, and username are required in production');
      }

      if (!config.password && config.type !== 'mongodb') {
        console.warn('Warning: No database password specified in production');
      }
    }
  }
};

// 数据库健康检查
export const healthCheck = async (service: DatabaseService): Promise<{
  status: 'healthy' | 'unhealthy';
  details: any;
}> => {
  try {
    // 尝试执行一个简单的查询
    const userCount = await service.users.count();
    
    // 获取数据库信息（如果是SQLite）
    let dbInfo = {};
    if ('getDatabaseInfo' in service) {
      dbInfo = (service as any).getDatabaseInfo();
    }

    return {
      status: 'healthy',
      details: {
        userCount,
        timestamp: new Date().toISOString(),
        ...dbInfo
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }
    };
  }
};

// 数据库迁移工具
export const runMigrations = async (config: DatabaseConfig): Promise<void> => {
  const service = await DatabaseFactory.create(config);
  
  try {
    console.log('Running database migrations...');
    // 迁移已经在服务初始化时运行
    console.log('Migrations completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await service.close();
  }
};

// 数据库备份工具
export const backupDatabase = async (config: DatabaseConfig, backupPath: string): Promise<void> => {
  if (config.type !== 'sqlite') {
    throw new Error('Backup currently only supported for SQLite databases');
  }

  const service = await DatabaseFactory.create(config);
  
  try {
    console.log(`Creating backup at: ${backupPath}`);
    await (service as any).backup(backupPath);
    console.log('Backup completed successfully');
  } catch (error) {
    console.error('Backup failed:', error);
    throw error;
  } finally {
    await service.close();
  }
};

// 数据库优化工具
export const optimizeDatabase = async (config: DatabaseConfig): Promise<void> => {
  const service = await DatabaseFactory.create(config);
  
  try {
    console.log('Optimizing database...');
    
    if ('vacuum' in service) {
      await (service as any).vacuum();
      console.log('VACUUM completed');
    }
    
    if ('analyze' in service) {
      await (service as any).analyze();
      console.log('ANALYZE completed');
    }
    
    console.log('Database optimization completed');
  } catch (error) {
    console.error('Optimization failed:', error);
    throw error;
  } finally {
    await service.close();
  }
};

// 导出便捷函数
export const createDatabase = DatabaseFactory.create;
export const getDatabase = DatabaseFactory.getInstance;
export const closeDatabase = DatabaseFactory.close;
