// SQLite数据库连接实现

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { DatabaseConnection, DatabaseConfig } from '../types';

export class SQLiteConnection implements DatabaseConnection {
  private db: Database.Database | null = null;
  private config: DatabaseConfig;
  private dbPath: string;

  constructor(config: DatabaseConfig) {
    this.config = config;
    // 默认数据库路径
    this.dbPath = config.connectionString || path.join(process.cwd(), 'data', 'vr720.db');
  }

  async connect(): Promise<void> {
    try {
      // 确保数据库目录存在
      const dbDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // 创建数据库连接
      this.db = new Database(this.dbPath, {
        verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
        ...this.config.options
      });

      // 启用外键约束
      this.db.pragma('foreign_keys = ON');
      
      // 设置WAL模式以提高并发性能
      this.db.pragma('journal_mode = WAL');
      
      // 设置同步模式
      this.db.pragma('synchronous = NORMAL');
      
      // 设置缓存大小
      this.db.pragma('cache_size = 10000');

      console.log(`SQLite database connected: ${this.dbPath}`);
    } catch (error) {
      console.error('Failed to connect to SQLite database:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('SQLite database disconnected');
    }
  }

  isConnected(): boolean {
    return this.db !== null && this.db.open;
  }

  async executeQuery(query: string, params: any[] = []): Promise<any> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    try {
      // 判断查询类型
      const trimmedQuery = query.trim().toLowerCase();
      
      if (trimmedQuery.startsWith('select')) {
        // SELECT查询
        const stmt = this.db.prepare(query);
        return stmt.all(params);
      } else if (trimmedQuery.startsWith('insert')) {
        // INSERT查询
        const stmt = this.db.prepare(query);
        const result = stmt.run(params);
        return {
          insertId: result.lastInsertRowid,
          changes: result.changes
        };
      } else if (trimmedQuery.startsWith('update') || trimmedQuery.startsWith('delete')) {
        // UPDATE/DELETE查询
        const stmt = this.db.prepare(query);
        const result = stmt.run(params);
        return {
          changes: result.changes
        };
      } else {
        // DDL查询（CREATE, ALTER, DROP等）
        this.db.exec(query);
        return { success: true };
      }
    } catch (error) {
      console.error('Query execution failed:', error);
      console.error('Query:', query);
      console.error('Params:', params);
      throw error;
    }
  }

  async beginTransaction(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    this.db.exec('BEGIN TRANSACTION');
  }

  async commitTransaction(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    this.db.exec('COMMIT');
  }

  async rollbackTransaction(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    this.db.exec('ROLLBACK');
  }

  // SQLite特有的方法
  backup(backupPath: string): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    
    const backup = this.db.backup(backupPath);
    backup.step(-1);
    backup.finish();
  }

  vacuum(): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    this.db.exec('VACUUM');
  }

  analyze(): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    this.db.exec('ANALYZE');
  }

  getStats(): any {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const pageCount = this.db.pragma('page_count', { simple: true });
    const pageSize = this.db.pragma('page_size', { simple: true });
    const freePages = this.db.pragma('freelist_count', { simple: true });
    
    return {
      pageCount,
      pageSize,
      freePages,
      databaseSize: pageCount * pageSize,
      freeSpace: freePages * pageSize
    };
  }

  // 批量操作支持
  prepareBatch(query: string): Database.Statement {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    return this.db.prepare(query);
  }

  executeBatch(statement: Database.Statement, dataArray: any[]): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const transaction = this.db.transaction((data: any[]) => {
      for (const item of data) {
        statement.run(item);
      }
    });

    transaction(dataArray);
  }

  // 数据库信息
  getDatabaseInfo(): any {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    return {
      path: this.dbPath,
      inTransaction: this.db.inTransaction,
      readonly: this.db.readonly,
      name: this.db.name,
      memory: this.db.memory,
      stats: this.getStats()
    };
  }

  // 表信息
  getTableInfo(tableName: string): any[] {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const stmt = this.db.prepare('PRAGMA table_info(?)');
    return stmt.all(tableName);
  }

  // 获取所有表名
  getAllTables(): string[] {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const stmt = this.db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    `);
    
    const tables = stmt.all() as { name: string }[];
    return tables.map(table => table.name);
  }

  // 检查表是否存在
  tableExists(tableName: string): boolean {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const stmt = this.db.prepare(`
      SELECT COUNT(*) as count FROM sqlite_master 
      WHERE type='table' AND name=?
    `);
    
    const result = stmt.get(tableName) as { count: number };
    return result.count > 0;
  }

  // 创建索引
  createIndex(indexName: string, tableName: string, columns: string[], unique: boolean = false): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const uniqueKeyword = unique ? 'UNIQUE' : '';
    const columnsStr = columns.join(', ');
    const query = `CREATE ${uniqueKeyword} INDEX IF NOT EXISTS ${indexName} ON ${tableName} (${columnsStr})`;
    
    this.db.exec(query);
  }

  // 删除索引
  dropIndex(indexName: string): void {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    this.db.exec(`DROP INDEX IF EXISTS ${indexName}`);
  }
}
