// SQLite数据库迁移

import { Migration } from '../types';
import { SQLiteConnection } from './connection';

export class MigrationManager {
  private connection: SQLiteConnection;

  constructor(connection: SQLiteConnection) {
    this.connection = connection;
  }

  async initializeMigrationTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    await this.connection.executeQuery(query);
  }

  async getExecutedMigrations(): Promise<string[]> {
    const query = 'SELECT version FROM migrations ORDER BY executed_at';
    const results = await this.connection.executeQuery(query);
    return results.map((row: any) => row.version);
  }

  async markMigrationExecuted(version: string, name: string): Promise<void> {
    const query = 'INSERT INTO migrations (version, name) VALUES (?, ?)';
    await this.connection.executeQuery(query, [version, name]);
  }

  async removeMigrationRecord(version: string): Promise<void> {
    const query = 'DELETE FROM migrations WHERE version = ?';
    await this.connection.executeQuery(query, [version]);
  }

  async runMigrations(migrations: Migration[]): Promise<void> {
    await this.initializeMigrationTable();
    const executedMigrations = await this.getExecutedMigrations();

    for (const migration of migrations) {
      if (!executedMigrations.includes(migration.version)) {
        console.log(`Running migration: ${migration.version} - ${migration.name}`);
        
        try {
          await this.connection.beginTransaction();
          await migration.up(this.connection);
          await this.markMigrationExecuted(migration.version, migration.name);
          await this.connection.commitTransaction();
          
          console.log(`Migration completed: ${migration.version}`);
        } catch (error) {
          await this.connection.rollbackTransaction();
          console.error(`Migration failed: ${migration.version}`, error);
          throw error;
        }
      }
    }
  }

  async rollbackMigration(migration: Migration): Promise<void> {
    console.log(`Rolling back migration: ${migration.version} - ${migration.name}`);
    
    try {
      await this.connection.beginTransaction();
      await migration.down();
      await this.removeMigrationRecord(migration.version);
      await this.connection.commitTransaction();
      
      console.log(`Migration rolled back: ${migration.version}`);
    } catch (error) {
      await this.connection.rollbackTransaction();
      console.error(`Migration rollback failed: ${migration.version}`, error);
      throw error;
    }
  }
}

// 初始数据库结构迁移
export const initialMigration: Migration = {
  version: '001',
  name: 'create_initial_tables',
  
  async up() {
    // Use the connection passed from the migration manager
    const connection = this.connection;

    // Users table
    await connection.executeQuery(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        avatar TEXT,
        role TEXT NOT NULL DEFAULT 'client',
        preferences TEXT NOT NULL DEFAULT '{}',
        is_active BOOLEAN NOT NULL DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 图片表
    await connection.executeQuery(`
      CREATE TABLE IF NOT EXISTS images (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        original_name TEXT NOT NULL,
        size INTEGER NOT NULL,
        mime_type TEXT NOT NULL,
        width INTEGER,
        height INTEGER,
        aspect_ratio REAL,
        file_path TEXT NOT NULL,
        thumbnail_path TEXT,
        metadata TEXT NOT NULL DEFAULT '{}',
        user_id TEXT,
        tags TEXT NOT NULL DEFAULT '[]',
        is_public BOOLEAN NOT NULL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    // 房屋表
    await connection.executeQuery(`
      CREATE TABLE IF NOT EXISTS houses (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        address TEXT NOT NULL,
        description TEXT,
        price REAL,
        currency TEXT NOT NULL DEFAULT 'USD',
        total_area REAL,
        bedrooms INTEGER NOT NULL DEFAULT 0,
        bathrooms INTEGER NOT NULL DEFAULT 0,
        property_type TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'draft',
        features TEXT NOT NULL DEFAULT '[]',
        user_id TEXT,
        agent_id TEXT,
        is_public BOOLEAN NOT NULL DEFAULT 0,
        view_count INTEGER NOT NULL DEFAULT 0,
        metadata TEXT NOT NULL DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (agent_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    // 房间表
    await connection.executeQuery(`
      CREATE TABLE IF NOT EXISTS rooms (
        id TEXT PRIMARY KEY,
        house_id TEXT NOT NULL,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        description TEXT,
        area REAL,
        floor INTEGER,
        position_x REAL NOT NULL,
        position_y REAL NOT NULL,
        position_z REAL DEFAULT 0,
        rotation REAL DEFAULT 0,
        panorama_image_id TEXT,
        thumbnail_image_id TEXT,
        features TEXT NOT NULL DEFAULT '[]',
        metadata TEXT NOT NULL DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (house_id) REFERENCES houses(id) ON DELETE CASCADE,
        FOREIGN KEY (panorama_image_id) REFERENCES images(id) ON DELETE SET NULL,
        FOREIGN KEY (thumbnail_image_id) REFERENCES images(id) ON DELETE SET NULL
      )
    `);

    // 热点表
    await connection.executeQuery(`
      CREATE TABLE IF NOT EXISTS hotspots (
        id TEXT PRIMARY KEY,
        room_id TEXT NOT NULL,
        target_room_id TEXT NOT NULL,
        label TEXT NOT NULL,
        description TEXT,
        position_x REAL NOT NULL,
        position_y REAL NOT NULL,
        position_z REAL NOT NULL,
        rotation_x REAL DEFAULT 0,
        rotation_y REAL DEFAULT 0,
        rotation_z REAL DEFAULT 0,
        type TEXT NOT NULL DEFAULT 'navigation',
        is_active BOOLEAN NOT NULL DEFAULT 1,
        metadata TEXT NOT NULL DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (target_room_id) REFERENCES rooms(id) ON DELETE CASCADE
      )
    `);

    // 项目表
    await connection.executeQuery(`
      CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        client_name TEXT,
        client_email TEXT,
        client_phone TEXT,
        status TEXT NOT NULL DEFAULT 'planning',
        start_date DATETIME,
        end_date DATETIME,
        budget REAL,
        currency TEXT NOT NULL DEFAULT 'USD',
        user_id TEXT,
        metadata TEXT NOT NULL DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    // 分析数据表
    await connection.executeQuery(`
      CREATE TABLE IF NOT EXISTS analytics (
        id TEXT PRIMARY KEY,
        entity_type TEXT NOT NULL,
        entity_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        user_id TEXT,
        session_id TEXT,
        ip_address TEXT,
        user_agent TEXT,
        referrer TEXT,
        duration INTEGER,
        metadata TEXT NOT NULL DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    // 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
      'CREATE INDEX IF NOT EXISTS idx_images_user_id ON images(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_images_is_public ON images(is_public)',
      'CREATE INDEX IF NOT EXISTS idx_houses_user_id ON houses(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_houses_status ON houses(status)',
      'CREATE INDEX IF NOT EXISTS idx_houses_property_type ON houses(property_type)',
      'CREATE INDEX IF NOT EXISTS idx_houses_is_public ON houses(is_public)',
      'CREATE INDEX IF NOT EXISTS idx_rooms_house_id ON rooms(house_id)',
      'CREATE INDEX IF NOT EXISTS idx_rooms_type ON rooms(type)',
      'CREATE INDEX IF NOT EXISTS idx_hotspots_room_id ON hotspots(room_id)',
      'CREATE INDEX IF NOT EXISTS idx_hotspots_target_room_id ON hotspots(target_room_id)',
      'CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_entity ON analytics(entity_type, entity_id)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_event_type ON analytics(event_type)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_user_id ON analytics(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_created_at ON analytics(created_at)'
    ];

    for (const indexQuery of indexes) {
      await connection.executeQuery(indexQuery);
    }
  },

  async down() {
    const connection = new SQLiteConnection({ type: 'sqlite' });
    
    const tables = [
      'analytics',
      'projects', 
      'hotspots',
      'rooms',
      'houses',
      'images',
      'users'
    ];

    for (const table of tables) {
      await connection.executeQuery(`DROP TABLE IF EXISTS ${table}`);
    }
  }
};

// 所有迁移列表
export const migrations: Migration[] = [
  initialMigration
];
