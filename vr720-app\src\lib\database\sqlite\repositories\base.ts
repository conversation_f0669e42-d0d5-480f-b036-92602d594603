// SQLite base repository implementation

import { randomUUID } from 'crypto';
import { DatabaseRepository, BaseEntity, QueryOptions } from '../../types';
import { SQLiteConnection } from '../connection';

export abstract class SQLiteBaseRepository<T extends BaseEntity> implements DatabaseRepository<T> {
  protected connection: SQLiteConnection;
  protected tableName: string;

  constructor(connection: SQLiteConnection, tableName: string) {
    this.connection = connection;
    this.tableName = tableName;
  }

  // Abstract methods that subclasses need to implement
  protected abstract mapRowToEntity(row: any): T;
  protected abstract mapEntityToRow(entity: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): any;

  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T> {
    const id = randomUUID();
    const now = new Date();
    const row = this.mapEntityToRow(data);
    
    // Build insert query
    const columns = ['id', 'created_at', 'updated_at', ...Object.keys(row)];
    const placeholders = columns.map(() => '?').join(', ');
    const values = [id, now.toISOString(), now.toISOString(), ...Object.values(row)];

    const query = `INSERT INTO ${this.tableName} (${columns.join(', ')}) VALUES (${placeholders})`;

    await this.connection.executeQuery(query, values);

    // Return created entity
    const created = await this.findById(id);
    if (!created) {
      throw new Error('Failed to create entity');
    }
    
    return created;
  }

  async findById(id: string): Promise<T | null> {
    const query = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    const results = await this.connection.executeQuery(query, [id]);
    
    if (results.length === 0) {
      return null;
    }
    
    return this.mapRowToEntity(results[0]);
  }

  async findMany(filter: Partial<T> = {}, options: QueryOptions = {}): Promise<T[]> {
    let query = `SELECT * FROM ${this.tableName}`;
    const params: any[] = [];
    
    // Build WHERE clause
    const whereConditions: string[] = [];
    for (const [key, value] of Object.entries(filter)) {
      if (value !== undefined) {
        whereConditions.push(`${this.camelToSnake(key)} = ?`);
        params.push(value);
      }
    }

    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    // Add sorting
    if (options.orderBy) {
      const direction = options.orderDirection || 'ASC';
      query += ` ORDER BY ${this.camelToSnake(options.orderBy)} ${direction}`;
    }

    // Add pagination
    if (options.limit) {
      query += ` LIMIT ${options.limit}`;
      if (options.offset) {
        query += ` OFFSET ${options.offset}`;
      }
    }
    
    const results = await this.connection.executeQuery(query, params);
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  async update(id: string, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<T> {
    const now = new Date();
    const updateData = { ...data, updatedAt: now };
    
    // Build update query
    const setClause: string[] = [];
    const params: any[] = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined) {
        setClause.push(`${this.camelToSnake(key)} = ?`);
        params.push(this.serializeValue(value));
      }
    }

    if (setClause.length === 0) {
      throw new Error('No fields to update');
    }

    params.push(id);
    const query = `UPDATE ${this.tableName} SET ${setClause.join(', ')} WHERE id = ?`;

    const result = await this.connection.executeQuery(query, params);

    if (result.changes === 0) {
      throw new Error('Entity not found or no changes made');
    }

    // Return updated entity
    const updated = await this.findById(id);
    if (!updated) {
      throw new Error('Failed to retrieve updated entity');
    }
    
    return updated;
  }

  async delete(id: string): Promise<boolean> {
    const query = `DELETE FROM ${this.tableName} WHERE id = ?`;
    const result = await this.connection.executeQuery(query, [id]);
    
    return result.changes > 0;
  }

  async count(filter: Partial<T> = {}): Promise<number> {
    let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const params: any[] = [];
    
    // Build WHERE clause
    const whereConditions: string[] = [];
    for (const [key, value] of Object.entries(filter)) {
      if (value !== undefined) {
        whereConditions.push(`${this.camelToSnake(key)} = ?`);
        params.push(value);
      }
    }

    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }
    
    const results = await this.connection.executeQuery(query, params);
    return results[0].count;
  }

  // Utility method: camelCase to snake_case
  protected camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  // Utility method: snake_case to camelCase
  protected snakeToCamel(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  // Utility method: serialize values (handle objects and arrays)
  protected serializeValue(value: any): any {
    if (typeof value === 'object' && value !== null) {
      return JSON.stringify(value);
    }
    return value;
  }

  // Utility method: deserialize values
  protected deserializeValue(value: any, expectedType: 'object' | 'array' | 'string' | 'number' | 'boolean' = 'string'): any {
    if (value === null || value === undefined) {
      return value;
    }

    if (expectedType === 'object' || expectedType === 'array') {
      try {
        return JSON.parse(value);
      } catch {
        return expectedType === 'object' ? {} : [];
      }
    }

    if (expectedType === 'boolean') {
      return Boolean(value);
    }

    if (expectedType === 'number') {
      return Number(value);
    }

    return value;
  }

  // Utility method: convert database row to entity common fields
  protected mapBaseFields(row: any): Pick<BaseEntity, 'id' | 'createdAt' | 'updatedAt'> {
    return {
      id: row.id,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  // 批量操作支持
  async createMany(dataArray: Omit<T, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<T[]> {
    if (dataArray.length === 0) {
      return [];
    }

    const results: T[] = [];
    
    try {
      await this.connection.beginTransaction();
      
      for (const data of dataArray) {
        const created = await this.create(data);
        results.push(created);
      }
      
      await this.connection.commitTransaction();
    } catch (error) {
      await this.connection.rollbackTransaction();
      throw error;
    }
    
    return results;
  }

  async updateMany(filter: Partial<T>, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<number> {
    const now = new Date();
    const updateData = { ...data, updatedAt: now };
    
    // 构建更新查询
    const setClause: string[] = [];
    const params: any[] = [];
    
    for (const [key, value] of Object.entries(updateData)) {
      if (value !== undefined) {
        setClause.push(`${this.camelToSnake(key)} = ?`);
        params.push(this.serializeValue(value));
      }
    }
    
    if (setClause.length === 0) {
      return 0;
    }
    
    let query = `UPDATE ${this.tableName} SET ${setClause.join(', ')}`;
    
    // 构建WHERE子句
    const whereConditions: string[] = [];
    for (const [key, value] of Object.entries(filter)) {
      if (value !== undefined) {
        whereConditions.push(`${this.camelToSnake(key)} = ?`);
        params.push(value);
      }
    }
    
    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }
    
    const result = await this.connection.executeQuery(query, params);
    return result.changes;
  }

  async deleteMany(filter: Partial<T>): Promise<number> {
    let query = `DELETE FROM ${this.tableName}`;
    const params: any[] = [];
    
    // 构建WHERE子句
    const whereConditions: string[] = [];
    for (const [key, value] of Object.entries(filter)) {
      if (value !== undefined) {
        whereConditions.push(`${this.camelToSnake(key)} = ?`);
        params.push(value);
      }
    }
    
    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    } else {
      throw new Error('Cannot delete all records without filter');
    }
    
    const result = await this.connection.executeQuery(query, params);
    return result.changes;
  }

  // 原始查询支持
  async executeRawQuery(query: string, params: any[] = []): Promise<any[]> {
    return await this.connection.executeQuery(query, params);
  }
}
