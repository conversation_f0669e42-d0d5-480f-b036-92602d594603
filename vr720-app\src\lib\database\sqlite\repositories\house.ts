// House repository implementation

import { House, Room } from '../../types';
import { SQLiteConnection } from '../connection';
import { SQLiteBaseRepository } from './base';

export class SQLiteHouseRepository extends SQLiteBaseRepository<House> {
  constructor(connection: SQLiteConnection) {
    super(connection, 'houses');
  }

  protected mapRowToEntity(row: any): House {
    return {
      ...this.mapBaseFields(row),
      name: row.name,
      address: row.address,
      description: row.description,
      price: row.price,
      currency: row.currency,
      totalArea: row.total_area,
      bedrooms: row.bedrooms,
      bathrooms: row.bathrooms,
      propertyType: row.property_type,
      status: row.status,
      features: this.deserializeValue(row.features, 'array'),
      userId: row.user_id,
      agentId: row.agent_id,
      isPublic: Boolean(row.is_public),
      viewCount: row.view_count,
      metadata: this.deserializeValue(row.metadata, 'object')
    };
  }

  protected mapEntityToRow(entity: Omit<House, 'id' | 'createdAt' | 'updatedAt'>): any {
    return {
      name: entity.name,
      address: entity.address,
      description: entity.description,
      price: entity.price,
      currency: entity.currency,
      total_area: entity.totalArea,
      bedrooms: entity.bedrooms,
      bathrooms: entity.bathrooms,
      property_type: entity.propertyType,
      status: entity.status,
      features: this.serializeValue(entity.features),
      user_id: entity.userId,
      agent_id: entity.agentId,
      is_public: entity.isPublic ? 1 : 0,
      view_count: entity.viewCount,
      metadata: this.serializeValue(entity.metadata)
    };
  }

  // Find house with its rooms
  async findHouseWithRooms(houseId: string): Promise<(House & { rooms: Room[] }) | null> {
    const house = await this.findById(houseId);
    if (!house) {
      return null;
    }

    // Query rooms
    const roomsQuery = `
      SELECT r.*, 
             pi.file_path as panorama_url,
             ti.file_path as thumbnail_url
      FROM rooms r
      LEFT JOIN images pi ON r.panorama_image_id = pi.id
      LEFT JOIN images ti ON r.thumbnail_image_id = ti.id
      WHERE r.house_id = ?
      ORDER BY r.created_at
    `;
    
    const roomRows = await this.connection.executeQuery(roomsQuery, [houseId]);
    
    const rooms: Room[] = roomRows.map((row: any) => ({
      id: row.id,
      houseId: row.house_id,
      name: row.name,
      type: row.type,
      description: row.description,
      area: row.area,
      floor: row.floor,
      position: {
        x: row.position_x,
        y: row.position_y,
        z: row.position_z || 0,
        rotation: row.rotation || 0
      },
      panoramaImageId: row.panorama_image_id,
      thumbnailImageId: row.thumbnail_image_id,
      features: this.deserializeValue(row.features, 'array'),
      metadata: this.deserializeValue(row.metadata, 'object'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      // Additional fields
      panoramaUrl: row.panorama_url,
      thumbnailUrl: row.thumbnail_url
    }));

    return {
      ...house,
      rooms
    };
  }

  // Find public houses
  async findPublicHouses(options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    return this.findMany({ isPublic: true } as Partial<House>, options);
  }

  // Search houses
  async searchHouses(query: string, options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    const searchQuery = `
      SELECT * FROM ${this.tableName}
      WHERE (name LIKE ? OR address LIKE ? OR description LIKE ?)
      AND is_public = 1
      ORDER BY view_count DESC, updated_at DESC
      ${options.limit ? `LIMIT ${options.limit}` : ''}
      ${options.offset ? `OFFSET ${options.offset}` : ''}
    `;
    
    const searchTerm = `%${query}%`;
    const results = await this.connection.executeQuery(searchQuery, [searchTerm, searchTerm, searchTerm]);
    
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  // Increment view count
  async incrementViewCount(houseId: string): Promise<void> {
    const query = `UPDATE ${this.tableName} SET view_count = view_count + 1, updated_at = ? WHERE id = ?`;
    await this.connection.executeQuery(query, [new Date().toISOString(), houseId]);
  }

  // Get house statistics
  async getHouseStats(houseId: string): Promise<{
    viewCount: number;
    roomCount: number;
    imageCount: number;
    lastViewed?: Date;
  }> {
    // Get basic statistics
    const statsQuery = `
      SELECT 
        h.view_count,
        COUNT(DISTINCT r.id) as room_count,
        COUNT(DISTINCT r.panorama_image_id) as image_count
      FROM houses h
      LEFT JOIN rooms r ON h.id = r.house_id
      WHERE h.id = ?
      GROUP BY h.id
    `;
    
    const statsResult = await this.connection.executeQuery(statsQuery, [houseId]);
    
    if (statsResult.length === 0) {
      throw new Error('House not found');
    }
    
    const stats = statsResult[0];
    
    // 获取最后浏览时间
    const lastViewedQuery = `
      SELECT MAX(created_at) as last_viewed
      FROM analytics
      WHERE entity_type = 'house' AND entity_id = ? AND event_type = 'view'
    `;
    
    const lastViewedResult = await this.connection.executeQuery(lastViewedQuery, [houseId]);
    const lastViewed = lastViewedResult[0]?.last_viewed ? new Date(lastViewedResult[0].last_viewed) : undefined;
    
    return {
      viewCount: stats.view_count,
      roomCount: stats.room_count,
      imageCount: stats.image_count,
      lastViewed
    };
  }

  // Get user's houses
  async findUserHouses(userId: string, options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    return this.findMany({ userId } as Partial<House>, options);
  }

  // Get agent's houses
  async findAgentHouses(agentId: string, options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    return this.findMany({ agentId } as Partial<House>, options);
  }

  // Find houses by status
  async findHousesByStatus(status: string, options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    return this.findMany({ status } as Partial<House>, options);
  }

  // Find houses by property type
  async findHousesByPropertyType(propertyType: string, options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    return this.findMany({ propertyType } as Partial<House>, options);
  }

  // Find houses by price range
  async findHousesByPriceRange(minPrice: number, maxPrice: number, options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE price >= ? AND price <= ?
      AND is_public = 1
      ORDER BY price ASC
      ${options.limit ? `LIMIT ${options.limit}` : ''}
      ${options.offset ? `OFFSET ${options.offset}` : ''}
    `;
    
    const results = await this.connection.executeQuery(query, [minPrice, maxPrice]);
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  // Get popular houses (by view count)
  async findPopularHouses(options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE is_public = 1
      ORDER BY view_count DESC, updated_at DESC
      ${options.limit ? `LIMIT ${options.limit}` : ''}
      ${options.offset ? `OFFSET ${options.offset}` : ''}
    `;
    
    const results = await this.connection.executeQuery(query);
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  // Get latest houses
  async findLatestHouses(options: { limit?: number; offset?: number } = {}): Promise<House[]> {
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE is_public = 1
      ORDER BY created_at DESC
      ${options.limit ? `LIMIT ${options.limit}` : ''}
      ${options.offset ? `OFFSET ${options.offset}` : ''}
    `;
    
    const results = await this.connection.executeQuery(query);
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  // Duplicate house
  async duplicateHouse(houseId: string, newName: string, userId?: string): Promise<House> {
    const originalHouse = await this.findHouseWithRooms(houseId);
    if (!originalHouse) {
      throw new Error('House not found');
    }

    try {
      await this.connection.beginTransaction();

      // Create new house
      const newHouse = await this.create({
        ...originalHouse,
        name: newName,
        userId: userId || originalHouse.userId,
        status: 'draft',
        isPublic: false,
        viewCount: 0
      });

      // Copy rooms
      for (const room of originalHouse.rooms) {
        await this.connection.executeQuery(`
          INSERT INTO rooms (
            id, house_id, name, type, description, area, floor,
            position_x, position_y, position_z, rotation,
            panorama_image_id, thumbnail_image_id, features, metadata,
            created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          require('crypto').randomUUID(),
          newHouse.id,
          room.name,
          room.type,
          room.description,
          room.area,
          room.floor,
          room.position.x,
          room.position.y,
          room.position.z,
          room.position.rotation,
          room.panoramaImageId,
          room.thumbnailImageId,
          this.serializeValue(room.features),
          this.serializeValue(room.metadata),
          new Date().toISOString(),
          new Date().toISOString()
        ]);
      }

      await this.connection.commitTransaction();
      return newHouse;
    } catch (error) {
      await this.connection.rollbackTransaction();
      throw error;
    }
  }
}
