// Image repository implementation

import { Image } from '../../types';
import { SQLiteConnection } from '../connection';
import { SQLiteBaseRepository } from './base';

export class SQLiteImageRepository extends SQLiteBaseRepository<Image> {
  constructor(connection: SQLiteConnection) {
    super(connection, 'images');
  }

  protected mapRowToEntity(row: any): Image {
    return {
      ...this.mapBaseFields(row),
      name: row.name,
      originalName: row.original_name,
      size: row.size,
      mimeType: row.mime_type,
      width: row.width,
      height: row.height,
      aspectRatio: row.aspect_ratio,
      filePath: row.file_path,
      thumbnailPath: row.thumbnail_path,
      metadata: this.deserializeValue(row.metadata, 'object'),
      userId: row.user_id,
      tags: this.deserializeValue(row.tags, 'array'),
      isPublic: Boolean(row.is_public)
    };
  }

  protected mapEntityToRow(entity: Omit<Image, 'id' | 'createdAt' | 'updatedAt'>): any {
    return {
      name: entity.name,
      original_name: entity.originalName,
      size: entity.size,
      mime_type: entity.mimeType,
      width: entity.width,
      height: entity.height,
      aspect_ratio: entity.aspectRatio,
      file_path: entity.filePath,
      thumbnail_path: entity.thumbnailPath,
      metadata: this.serializeValue(entity.metadata),
      user_id: entity.userId,
      tags: this.serializeValue(entity.tags),
      is_public: entity.isPublic ? 1 : 0
    };
  }

  // Find user's images
  async findUserImages(userId: string, options: { limit?: number; offset?: number } = {}): Promise<Image[]> {
    return this.findMany({ userId } as Partial<Image>, options);
  }

  // Find public images
  async findPublicImages(options: { limit?: number; offset?: number } = {}): Promise<Image[]> {
    return this.findMany({ isPublic: true } as Partial<Image>, options);
  }

  // Find images by tags
  async findImagesByTags(tags: string[], options: { limit?: number; offset?: number } = {}): Promise<Image[]> {
    const placeholders = tags.map(() => '?').join(',');
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE id IN (
        SELECT DISTINCT id FROM ${this.tableName}
        WHERE ${tags.map(() => 'tags LIKE ?').join(' OR ')}
      )
      ORDER BY created_at DESC
      ${options.limit ? `LIMIT ${options.limit}` : ''}
      ${options.offset ? `OFFSET ${options.offset}` : ''}
    `;
    
    const searchTerms = tags.map(tag => `%"${tag}"%`);
    const results = await this.connection.executeQuery(query, searchTerms);
    
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  // Find images by MIME type
  async findImagesByMimeType(mimeType: string, options: { limit?: number; offset?: number } = {}): Promise<Image[]> {
    return this.findMany({ mimeType } as Partial<Image>, options);
  }

  // Find panoramic images (aspect ratio > 1.8)
  async findPanoramicImages(options: { limit?: number; offset?: number } = {}): Promise<Image[]> {
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE aspect_ratio > 1.8
      ORDER BY created_at DESC
      ${options.limit ? `LIMIT ${options.limit}` : ''}
      ${options.offset ? `OFFSET ${options.offset}` : ''}
    `;
    
    const results = await this.connection.executeQuery(query);
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  // Find images by size range
  async findImagesBySizeRange(minSize: number, maxSize: number, options: { limit?: number; offset?: number } = {}): Promise<Image[]> {
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE size >= ? AND size <= ?
      ORDER BY size DESC
      ${options.limit ? `LIMIT ${options.limit}` : ''}
      ${options.offset ? `OFFSET ${options.offset}` : ''}
    `;
    
    const results = await this.connection.executeQuery(query, [minSize, maxSize]);
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  // Search images
  async searchImages(query: string, options: { limit?: number; offset?: number } = {}): Promise<Image[]> {
    const searchQuery = `
      SELECT * FROM ${this.tableName}
      WHERE (name LIKE ? OR original_name LIKE ? OR tags LIKE ?)
      ORDER BY created_at DESC
      ${options.limit ? `LIMIT ${options.limit}` : ''}
      ${options.offset ? `OFFSET ${options.offset}` : ''}
    `;
    
    const searchTerm = `%${query}%`;
    const results = await this.connection.executeQuery(searchQuery, [searchTerm, searchTerm, searchTerm]);
    
    return results.map((row: any) => this.mapRowToEntity(row));
  }

  // Get image statistics
  async getImageStats(userId?: string): Promise<{
    totalCount: number;
    totalSize: number;
    panoramicCount: number;
    averageSize: number;
    mimeTypeDistribution: { [mimeType: string]: number };
  }> {
    let whereClause = '';
    const params: any[] = [];
    
    if (userId) {
      whereClause = 'WHERE user_id = ?';
      params.push(userId);
    }

    // Basic statistics
    const basicStatsQuery = `
      SELECT 
        COUNT(*) as total_count,
        SUM(size) as total_size,
        AVG(size) as average_size,
        COUNT(CASE WHEN aspect_ratio > 1.8 THEN 1 END) as panoramic_count
      FROM ${this.tableName}
      ${whereClause}
    `;
    
    const basicStats = await this.connection.executeQuery(basicStatsQuery, params);
    
    // MIME type distribution
    const mimeTypeQuery = `
      SELECT mime_type, COUNT(*) as count
      FROM ${this.tableName}
      ${whereClause}
      GROUP BY mime_type
    `;
    
    const mimeTypeResults = await this.connection.executeQuery(mimeTypeQuery, params);
    const mimeTypeDistribution: { [mimeType: string]: number } = {};
    
    mimeTypeResults.forEach((row: any) => {
      mimeTypeDistribution[row.mime_type] = row.count;
    });

    return {
      totalCount: basicStats[0].total_count,
      totalSize: basicStats[0].total_size || 0,
      panoramicCount: basicStats[0].panoramic_count,
      averageSize: basicStats[0].average_size || 0,
      mimeTypeDistribution
    };
  }

  // Add tags to image
  async addTagsToImage(imageId: string, newTags: string[]): Promise<Image> {
    const image = await this.findById(imageId);
    if (!image) {
      throw new Error('Image not found');
    }

    const existingTags = image.tags || [];
    const uniqueTags = [...new Set([...existingTags, ...newTags])];
    
    return this.update(imageId, { tags: uniqueTags });
  }

  // Remove tags from image
  async removeTagsFromImage(imageId: string, tagsToRemove: string[]): Promise<Image> {
    const image = await this.findById(imageId);
    if (!image) {
      throw new Error('Image not found');
    }

    const filteredTags = (image.tags || []).filter(tag => !tagsToRemove.includes(tag));
    
    return this.update(imageId, { tags: filteredTags });
  }

  // Get all tags
  async getAllTags(userId?: string): Promise<string[]> {
    let whereClause = '';
    const params: any[] = [];
    
    if (userId) {
      whereClause = 'WHERE user_id = ?';
      params.push(userId);
    }

    const query = `
      SELECT DISTINCT tags
      FROM ${this.tableName}
      ${whereClause}
    `;
    
    const results = await this.connection.executeQuery(query, params);
    const allTags = new Set<string>();
    
    results.forEach((row: any) => {
      const tags = this.deserializeValue(row.tags, 'array');
      tags.forEach((tag: string) => allTags.add(tag));
    });
    
    return Array.from(allTags).sort();
  }

  // Get popular tags
  async getPopularTags(limit: number = 20, userId?: string): Promise<{ tag: string; count: number }[]> {
    let whereClause = '';
    const params: any[] = [];
    
    if (userId) {
      whereClause = 'WHERE user_id = ?';
      params.push(userId);
    }

    const query = `
      SELECT tags
      FROM ${this.tableName}
      ${whereClause}
    `;
    
    const results = await this.connection.executeQuery(query, params);
    const tagCounts = new Map<string, number>();
    
    results.forEach((row: any) => {
      const tags = this.deserializeValue(row.tags, 'array');
      tags.forEach((tag: string) => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });
    
    return Array.from(tagCounts.entries())
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  // Batch update image metadata
  async updateImageMetadata(imageId: string, metadata: Partial<Image['metadata']>): Promise<Image> {
    const image = await this.findById(imageId);
    if (!image) {
      throw new Error('Image not found');
    }

    const updatedMetadata = { ...image.metadata, ...metadata };
    return this.update(imageId, { metadata: updatedMetadata });
  }

  // Find orphaned images (images not used by any room)
  async findOrphanedImages(userId?: string): Promise<Image[]> {
    let whereClause = 'WHERE i.id NOT IN (SELECT DISTINCT panorama_image_id FROM rooms WHERE panorama_image_id IS NOT NULL UNION SELECT DISTINCT thumbnail_image_id FROM rooms WHERE thumbnail_image_id IS NOT NULL)';
    const params: any[] = [];
    
    if (userId) {
      whereClause += ' AND i.user_id = ?';
      params.push(userId);
    }

    const query = `
      SELECT i.* FROM ${this.tableName} i
      ${whereClause}
      ORDER BY i.created_at DESC
    `;
    
    const results = await this.connection.executeQuery(query, params);
    return results.map((row: any) => this.mapRowToEntity(row));
  }
}
