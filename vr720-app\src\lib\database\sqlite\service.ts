// SQLite数据库服务实现

import { DatabaseService, DatabaseConfig, User, Image, House, Room, Hotspot, Project, Analytics, QueryOptions } from '../types';
import { SQLiteConnection } from './connection';
import { MigrationManager, migrations } from './migrations';
import { SQLiteImageRepository } from './repositories/image';
import { SQLiteHouseRepository } from './repositories/house';
import { SQLiteBaseRepository } from './repositories/base';

// 简化的仓库实现（用于演示）
class SQLiteUserRepository extends SQLiteBaseRepository<User> {
  constructor(connection: SQLiteConnection) {
    super(connection, 'users');
  }

  protected mapRowToEntity(row: any): User {
    return {
      ...this.mapBaseFields(row),
      email: row.email,
      name: row.name,
      avatar: row.avatar,
      role: row.role,
      preferences: this.deserializeValue(row.preferences, 'object'),
      isActive: Boolean(row.is_active)
    };
  }

  protected mapEntityToRow(entity: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): any {
    return {
      email: entity.email,
      name: entity.name,
      avatar: entity.avatar,
      role: entity.role,
      preferences: this.serializeValue(entity.preferences),
      is_active: entity.isActive ? 1 : 0
    };
  }
}

class SQLiteRoomRepository extends SQLiteBaseRepository<Room> {
  constructor(connection: SQLiteConnection) {
    super(connection, 'rooms');
  }

  protected mapRowToEntity(row: any): Room {
    return {
      ...this.mapBaseFields(row),
      houseId: row.house_id,
      name: row.name,
      type: row.type,
      description: row.description,
      area: row.area,
      floor: row.floor,
      position: {
        x: row.position_x,
        y: row.position_y,
        z: row.position_z || 0,
        rotation: row.rotation || 0
      },
      panoramaImageId: row.panorama_image_id,
      thumbnailImageId: row.thumbnail_image_id,
      features: this.deserializeValue(row.features, 'array'),
      metadata: this.deserializeValue(row.metadata, 'object')
    };
  }

  protected mapEntityToRow(entity: Omit<Room, 'id' | 'createdAt' | 'updatedAt'>): any {
    return {
      house_id: entity.houseId,
      name: entity.name,
      type: entity.type,
      description: entity.description,
      area: entity.area,
      floor: entity.floor,
      position_x: entity.position.x,
      position_y: entity.position.y,
      position_z: entity.position.z,
      rotation: entity.position.rotation,
      panorama_image_id: entity.panoramaImageId,
      thumbnail_image_id: entity.thumbnailImageId,
      features: this.serializeValue(entity.features),
      metadata: this.serializeValue(entity.metadata)
    };
  }
}

class SQLiteHotspotRepository extends SQLiteBaseRepository<Hotspot> {
  constructor(connection: SQLiteConnection) {
    super(connection, 'hotspots');
  }

  protected mapRowToEntity(row: any): Hotspot {
    return {
      ...this.mapBaseFields(row),
      roomId: row.room_id,
      targetRoomId: row.target_room_id,
      label: row.label,
      description: row.description,
      position: {
        x: row.position_x,
        y: row.position_y,
        z: row.position_z,
        rotation: {
          x: row.rotation_x || 0,
          y: row.rotation_y || 0,
          z: row.rotation_z || 0
        }
      },
      type: row.type,
      isActive: Boolean(row.is_active),
      metadata: this.deserializeValue(row.metadata, 'object')
    };
  }

  protected mapEntityToRow(entity: Omit<Hotspot, 'id' | 'createdAt' | 'updatedAt'>): any {
    return {
      room_id: entity.roomId,
      target_room_id: entity.targetRoomId,
      label: entity.label,
      description: entity.description,
      position_x: entity.position.x,
      position_y: entity.position.y,
      position_z: entity.position.z,
      rotation_x: entity.position.rotation?.x || 0,
      rotation_y: entity.position.rotation?.y || 0,
      rotation_z: entity.position.rotation?.z || 0,
      type: entity.type,
      is_active: entity.isActive ? 1 : 0,
      metadata: this.serializeValue(entity.metadata)
    };
  }
}

class SQLiteProjectRepository extends SQLiteBaseRepository<Project> {
  constructor(connection: SQLiteConnection) {
    super(connection, 'projects');
  }

  protected mapRowToEntity(row: any): Project {
    return {
      ...this.mapBaseFields(row),
      name: row.name,
      description: row.description,
      clientName: row.client_name,
      clientEmail: row.client_email,
      clientPhone: row.client_phone,
      status: row.status,
      startDate: row.start_date ? new Date(row.start_date) : undefined,
      endDate: row.end_date ? new Date(row.end_date) : undefined,
      budget: row.budget,
      currency: row.currency,
      userId: row.user_id,
      metadata: this.deserializeValue(row.metadata, 'object')
    };
  }

  protected mapEntityToRow(entity: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): any {
    return {
      name: entity.name,
      description: entity.description,
      client_name: entity.clientName,
      client_email: entity.clientEmail,
      client_phone: entity.clientPhone,
      status: entity.status,
      start_date: entity.startDate?.toISOString(),
      end_date: entity.endDate?.toISOString(),
      budget: entity.budget,
      currency: entity.currency,
      user_id: entity.userId,
      metadata: this.serializeValue(entity.metadata)
    };
  }
}

class SQLiteAnalyticsRepository extends SQLiteBaseRepository<Analytics> {
  constructor(connection: SQLiteConnection) {
    super(connection, 'analytics');
  }

  protected mapRowToEntity(row: any): Analytics {
    return {
      ...this.mapBaseFields(row),
      entityType: row.entity_type,
      entityId: row.entity_id,
      eventType: row.event_type,
      userId: row.user_id,
      sessionId: row.session_id,
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      referrer: row.referrer,
      duration: row.duration,
      metadata: this.deserializeValue(row.metadata, 'object')
    };
  }

  protected mapEntityToRow(entity: Omit<Analytics, 'id' | 'createdAt' | 'updatedAt'>): any {
    return {
      entity_type: entity.entityType,
      entity_id: entity.entityId,
      event_type: entity.eventType,
      user_id: entity.userId,
      session_id: entity.sessionId,
      ip_address: entity.ipAddress,
      user_agent: entity.userAgent,
      referrer: entity.referrer,
      duration: entity.duration,
      metadata: this.serializeValue(entity.metadata)
    };
  }
}

export class SQLiteDatabaseService implements DatabaseService {
  private connection: SQLiteConnection;
  private migrationManager: MigrationManager;
  
  public users: SQLiteUserRepository;
  public images: SQLiteImageRepository;
  public houses: SQLiteHouseRepository;
  public rooms: SQLiteRoomRepository;
  public hotspots: SQLiteHotspotRepository;
  public projects: SQLiteProjectRepository;
  public analytics: SQLiteAnalyticsRepository;

  constructor(config: DatabaseConfig) {
    this.connection = new SQLiteConnection(config);
    this.migrationManager = new MigrationManager(this.connection);
    
    // 初始化仓库
    this.users = new SQLiteUserRepository(this.connection);
    this.images = new SQLiteImageRepository(this.connection);
    this.houses = new SQLiteHouseRepository(this.connection);
    this.rooms = new SQLiteRoomRepository(this.connection);
    this.hotspots = new SQLiteHotspotRepository(this.connection);
    this.projects = new SQLiteProjectRepository(this.connection);
    this.analytics = new SQLiteAnalyticsRepository(this.connection);
  }

  async initialize(): Promise<void> {
    await this.connection.connect();
    await this.migrationManager.runMigrations(migrations);
  }

  async close(): Promise<void> {
    await this.connection.disconnect();
  }

  // 特殊查询方法实现
  async findHouseWithRooms(houseId: string): Promise<(House & { rooms: Room[] }) | null> {
    return this.houses.findHouseWithRooms(houseId);
  }

  async findRoomWithHotspots(roomId: string): Promise<(Room & { hotspots: Hotspot[] }) | null> {
    const room = await this.rooms.findById(roomId);
    if (!room) {
      return null;
    }

    const hotspots = await this.hotspots.findMany({ roomId } as Partial<Hotspot>);
    
    return {
      ...room,
      hotspots
    };
  }

  async findUserImages(userId: string, options?: QueryOptions): Promise<Image[]> {
    return this.images.findUserImages(userId, options);
  }

  async findPublicHouses(options?: QueryOptions): Promise<House[]> {
    return this.houses.findPublicHouses(options);
  }

  async searchHouses(query: string, options?: QueryOptions): Promise<House[]> {
    return this.houses.searchHouses(query, options);
  }

  // 统计方法实现
  async getHouseStats(houseId: string): Promise<{
    viewCount: number;
    roomCount: number;
    imageCount: number;
    lastViewed?: Date;
  }> {
    return this.houses.getHouseStats(houseId);
  }

  async getUserStats(userId: string): Promise<{
    houseCount: number;
    imageCount: number;
    projectCount: number;
    totalViews: number;
  }> {
    const houseCount = await this.houses.count({ userId } as Partial<House>);
    const imageCount = await this.images.count({ userId } as Partial<Image>);
    const projectCount = await this.projects.count({ userId } as Partial<Project>);
    
    // 计算总浏览次数
    const viewsQuery = `
      SELECT SUM(view_count) as total_views
      FROM houses
      WHERE user_id = ?
    `;
    
    const viewsResult = await this.connection.executeQuery(viewsQuery, [userId]);
    const totalViews = viewsResult[0]?.total_views || 0;

    return {
      houseCount,
      imageCount,
      projectCount,
      totalViews
    };
  }

  // 数据库管理方法
  async backup(backupPath: string): Promise<void> {
    this.connection.backup(backupPath);
  }

  async vacuum(): Promise<void> {
    this.connection.vacuum();
  }

  async analyze(): Promise<void> {
    this.connection.analyze();
  }

  getDatabaseInfo(): any {
    return this.connection.getDatabaseInfo();
  }

  getStats(): any {
    return this.connection.getStats();
  }
}
