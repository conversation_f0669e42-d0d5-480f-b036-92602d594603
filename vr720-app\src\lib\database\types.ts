// 数据库类型定义

export interface DatabaseConfig {
  type: 'sqlite' | 'mysql' | 'postgresql' | 'mongodb';
  connectionString?: string;
  host?: string;
  port?: number;
  database?: string;
  username?: string;
  password?: string;
  options?: Record<string, any>;
}

// 基础实体接口
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// 用户实体
export interface User extends BaseEntity {
  email: string;
  name: string;
  avatar?: string;
  role: 'admin' | 'agent' | 'client';
  preferences: UserPreferences;
  isActive: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: boolean;
  autoSave: boolean;
  defaultUploadQuality: number;
}

// 图片实体
export interface Image extends BaseEntity {
  name: string;
  originalName: string;
  size: number;
  mimeType: string;
  width?: number;
  height?: number;
  aspectRatio?: number;
  filePath: string;
  thumbnailPath?: string;
  metadata: ImageMetadata;
  userId?: string;
  tags: string[];
  isPublic: boolean;
}

export interface ImageMetadata {
  camera?: string;
  lens?: string;
  iso?: number;
  aperture?: string;
  shutterSpeed?: string;
  focalLength?: number;
  gps?: {
    latitude: number;
    longitude: number;
    altitude?: number;
  };
  captureDate?: Date;
  processingInfo?: {
    stitched: boolean;
    enhanced: boolean;
    compressed: boolean;
  };
}

// House entity
export interface House extends BaseEntity {
  name: string;
  address: string;
  description?: string;
  price?: number;
  currency: string;
  totalArea?: number;
  bedrooms: number;
  bathrooms: number;
  propertyType: 'single-family' | 'condo' | 'townhouse' | 'mansion' | 'studio';
  status: 'draft' | 'published' | 'sold' | 'archived';
  features: string[];
  userId?: string;
  agentId?: string;
  isPublic: boolean;
  viewCount: number;
  metadata: HouseMetadata;
}

export interface HouseMetadata {
  yearBuilt?: number;
  lotSize?: number;
  parkingSpaces?: number;
  hasGarage: boolean;
  hasPool: boolean;
  hasGarden: boolean;
  heatingType?: string;
  coolingType?: string;
  floorCount?: number;
  mlsNumber?: string;
  virtualTourUrl?: string;
}

// 房间实体
export interface Room extends BaseEntity {
  houseId: string;
  name: string;
  type: string;
  description?: string;
  area?: number;
  floor?: number;
  position: RoomPosition;
  panoramaImageId?: string;
  thumbnailImageId?: string;
  features: string[];
  metadata: RoomMetadata;
}

export interface RoomPosition {
  x: number;
  y: number;
  z?: number;
  rotation?: number;
}

export interface RoomMetadata {
  ceilingHeight?: number;
  windowCount?: number;
  hasNaturalLight: boolean;
  floorMaterial?: string;
  wallMaterial?: string;
  ceilingMaterial?: string;
  electricalOutlets?: number;
  hasAirConditioning: boolean;
  hasHeating: boolean;
}

// 热点实体
export interface Hotspot extends BaseEntity {
  roomId: string;
  targetRoomId: string;
  label: string;
  description?: string;
  position: HotspotPosition;
  type: 'navigation' | 'info' | 'media' | 'external';
  isActive: boolean;
  metadata: HotspotMetadata;
}

export interface HotspotPosition {
  x: number;
  y: number;
  z: number;
  rotation?: {
    x: number;
    y: number;
    z: number;
  };
}

export interface HotspotMetadata {
  icon?: string;
  color?: string;
  size?: number;
  animation?: string;
  clickAction?: string;
  hoverText?: string;
  mediaUrl?: string;
  externalUrl?: string;
}

// 项目实体（用于组织多个房屋）
export interface Project extends BaseEntity {
  name: string;
  description?: string;
  clientName?: string;
  clientEmail?: string;
  clientPhone?: string;
  status: 'planning' | 'in-progress' | 'review' | 'completed' | 'cancelled';
  startDate?: Date;
  endDate?: Date;
  budget?: number;
  currency: string;
  userId?: string;
  metadata: ProjectMetadata;
}

export interface ProjectMetadata {
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'residential' | 'commercial' | 'industrial' | 'mixed-use';
  deliverables: string[];
  notes?: string;
  contractUrl?: string;
  invoiceUrl?: string;
}

// 分析数据实体
export interface Analytics extends BaseEntity {
  entityType: 'house' | 'room' | 'image' | 'project';
  entityId: string;
  eventType: 'view' | 'share' | 'download' | 'favorite' | 'inquiry';
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  referrer?: string;
  duration?: number;
  metadata: AnalyticsMetadata;
}

export interface AnalyticsMetadata {
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  browser?: string;
  os?: string;
  country?: string;
  city?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  customData?: Record<string, any>;
}

// 数据库操作接口
export interface DatabaseRepository<T extends BaseEntity> {
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  findById(id: string): Promise<T | null>;
  findMany(filter?: Partial<T>, options?: QueryOptions): Promise<T[]>;
  update(id: string, data: Partial<Omit<T, 'id' | 'createdAt'>>): Promise<T>;
  delete(id: string): Promise<boolean>;
  count(filter?: Partial<T>): Promise<number>;
}

export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
  include?: string[];
}

// 数据库连接接口
export interface DatabaseConnection {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  isConnected(): boolean;
  executeQuery(query: string, params?: any[]): Promise<any>;
  beginTransaction(): Promise<void>;
  commitTransaction(): Promise<void>;
  rollbackTransaction(): Promise<void>;
}

// 数据库迁移接口
export interface Migration {
  version: string;
  name: string;
  up(): Promise<void>;
  down(): Promise<void>;
}

// 数据库服务接口
export interface DatabaseService {
  users: DatabaseRepository<User>;
  images: DatabaseRepository<Image>;
  houses: DatabaseRepository<House>;
  rooms: DatabaseRepository<Room>;
  hotspots: DatabaseRepository<Hotspot>;
  projects: DatabaseRepository<Project>;
  analytics: DatabaseRepository<Analytics>;
  
  // 特殊查询方法
  findHouseWithRooms(houseId: string): Promise<(House & { rooms: Room[] }) | null>;
  findRoomWithHotspots(roomId: string): Promise<(Room & { hotspots: Hotspot[] }) | null>;
  findUserImages(userId: string, options?: QueryOptions): Promise<Image[]>;
  findPublicHouses(options?: QueryOptions): Promise<House[]>;
  searchHouses(query: string, options?: QueryOptions): Promise<House[]>;
  
  // 统计方法
  getHouseStats(houseId: string): Promise<{
    viewCount: number;
    roomCount: number;
    imageCount: number;
    lastViewed?: Date;
  }>;
  
  getUserStats(userId: string): Promise<{
    houseCount: number;
    imageCount: number;
    projectCount: number;
    totalViews: number;
  }>;
}
