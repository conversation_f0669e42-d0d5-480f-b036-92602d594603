// IndexedDB storage for large image files
// This provides much larger storage capacity than localStorage

export interface StoredImageData {
  id: string;
  name: string;
  size: number;
  type: string;
  uploadTime: number;
  blob: Blob; // Store as Blob instead of base64 for better performance
  metadata?: {
    width?: number;
    height?: number;
    aspectRatio?: number;
  };
}

export interface StoredHouseData {
  id: string;
  name: string;
  address: string;
  rooms: any[];
  metadata: any;
  lastModified: number;
}

const DB_NAME = 'VR720_Storage';
const DB_VERSION = 1;
const IMAGES_STORE = 'images';
const HOUSES_STORE = 'houses';

class IndexedDBStorage {
  private db: IDBDatabase | null = null;
  private initPromise: Promise<void> | null = null;

  constructor() {
    // Only initialize if we're in the browser
    if (typeof window !== 'undefined' && 'indexedDB' in window) {
      this.initPromise = this.initDB();
    }
  }

  private async initDB(): Promise<void> {
    // Check if IndexedDB is available
    if (typeof window === 'undefined' || !('indexedDB' in window)) {
      throw new Error('IndexedDB is not available');
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        console.error('Failed to open IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('IndexedDB initialized successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create images store
        if (!db.objectStoreNames.contains(IMAGES_STORE)) {
          const imagesStore = db.createObjectStore(IMAGES_STORE, { keyPath: 'id' });
          imagesStore.createIndex('uploadTime', 'uploadTime', { unique: false });
          imagesStore.createIndex('name', 'name', { unique: false });
        }

        // Create houses store
        if (!db.objectStoreNames.contains(HOUSES_STORE)) {
          const housesStore = db.createObjectStore(HOUSES_STORE, { keyPath: 'id' });
          housesStore.createIndex('lastModified', 'lastModified', { unique: false });
          housesStore.createIndex('name', 'name', { unique: false });
        }
      };
    });
  }

  private async ensureDB(): Promise<IDBDatabase> {
    // Check if we're in the browser
    if (typeof window === 'undefined' || !('indexedDB' in window)) {
      throw new Error('IndexedDB is not available in this environment');
    }

    if (this.initPromise) {
      await this.initPromise;
    }
    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }
    return this.db;
  }

  // Image storage methods
  async saveImage(imageData: StoredImageData): Promise<boolean> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([IMAGES_STORE], 'readwrite');
      const store = transaction.objectStore(IMAGES_STORE);
      
      await new Promise<void>((resolve, reject) => {
        const request = store.put(imageData);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('Failed to save image to IndexedDB:', error);
      return false;
    }
  }

  async getImage(imageId: string): Promise<StoredImageData | null> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([IMAGES_STORE], 'readonly');
      const store = transaction.objectStore(IMAGES_STORE);
      
      return new Promise<StoredImageData | null>((resolve, reject) => {
        const request = store.get(imageId);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Failed to get image from IndexedDB:', error);
      return null;
    }
  }

  async getAllImages(): Promise<StoredImageData[]> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([IMAGES_STORE], 'readonly');
      const store = transaction.objectStore(IMAGES_STORE);
      
      return new Promise<StoredImageData[]>((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result || []);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Failed to get all images from IndexedDB:', error);
      return [];
    }
  }

  async removeImage(imageId: string): Promise<boolean> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([IMAGES_STORE], 'readwrite');
      const store = transaction.objectStore(IMAGES_STORE);
      
      await new Promise<void>((resolve, reject) => {
        const request = store.delete(imageId);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('Failed to remove image from IndexedDB:', error);
      return false;
    }
  }

  async clearAllImages(): Promise<boolean> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([IMAGES_STORE], 'readwrite');
      const store = transaction.objectStore(IMAGES_STORE);
      
      await new Promise<void>((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('Failed to clear images from IndexedDB:', error);
      return false;
    }
  }

  // House storage methods
  async saveHouse(houseData: StoredHouseData): Promise<boolean> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([HOUSES_STORE], 'readwrite');
      const store = transaction.objectStore(HOUSES_STORE);
      
      await new Promise<void>((resolve, reject) => {
        const request = store.put({ ...houseData, lastModified: Date.now() });
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('Failed to save house to IndexedDB:', error);
      return false;
    }
  }

  async getAllHouses(): Promise<StoredHouseData[]> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([HOUSES_STORE], 'readonly');
      const store = transaction.objectStore(HOUSES_STORE);
      
      return new Promise<StoredHouseData[]>((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result || []);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Failed to get all houses from IndexedDB:', error);
      return [];
    }
  }

  async removeHouse(houseId: string): Promise<boolean> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([HOUSES_STORE], 'readwrite');
      const store = transaction.objectStore(HOUSES_STORE);
      
      await new Promise<void>((resolve, reject) => {
        const request = store.delete(houseId);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      return true;
    } catch (error) {
      console.error('Failed to remove house from IndexedDB:', error);
      return false;
    }
  }

  // Cleanup old data
  async cleanupOldImages(daysOld: number = 7): Promise<number> {
    try {
      const db = await this.ensureDB();
      const transaction = db.transaction([IMAGES_STORE], 'readwrite');
      const store = transaction.objectStore(IMAGES_STORE);
      const index = store.index('uploadTime');
      
      const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
      const range = IDBKeyRange.upperBound(cutoffTime);
      
      return new Promise<number>((resolve, reject) => {
        let deletedCount = 0;
        const request = index.openCursor(range);
        
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            cursor.delete();
            deletedCount++;
            cursor.continue();
          } else {
            resolve(deletedCount);
          }
        };
        
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.error('Failed to cleanup old images:', error);
      return 0;
    }
  }

  // Get storage usage estimate
  async getStorageEstimate(): Promise<{ usage: number; quota: number }> {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        return {
          usage: estimate.usage || 0,
          quota: estimate.quota || 0
        };
      }
    } catch (error) {
      console.error('Failed to get storage estimate:', error);
    }
    
    return { usage: 0, quota: 0 };
  }
}

// Create singleton instance lazily
let indexedDBStorageInstance: IndexedDBStorage | null = null;

export const getIndexedDBStorage = (): IndexedDBStorage => {
  if (!indexedDBStorageInstance) {
    indexedDBStorageInstance = new IndexedDBStorage();
  }
  return indexedDBStorageInstance;
};

// For backward compatibility
export const indexedDBStorage = {
  get saveImage() { return getIndexedDBStorage().saveImage.bind(getIndexedDBStorage()); },
  get getImage() { return getIndexedDBStorage().getImage.bind(getIndexedDBStorage()); },
  get getAllImages() { return getIndexedDBStorage().getAllImages.bind(getIndexedDBStorage()); },
  get removeImage() { return getIndexedDBStorage().removeImage.bind(getIndexedDBStorage()); },
  get clearAllImages() { return getIndexedDBStorage().clearAllImages.bind(getIndexedDBStorage()); },
  get saveHouse() { return getIndexedDBStorage().saveHouse.bind(getIndexedDBStorage()); },
  get getAllHouses() { return getIndexedDBStorage().getAllHouses.bind(getIndexedDBStorage()); },
  get removeHouse() { return getIndexedDBStorage().removeHouse.bind(getIndexedDBStorage()); },
  get cleanupOldImages() { return getIndexedDBStorage().cleanupOldImages.bind(getIndexedDBStorage()); },
  get getStorageEstimate() { return getIndexedDBStorage().getStorageEstimate.bind(getIndexedDBStorage()); }
};

// Helper function to convert File to Blob
export const fileToBlob = (file: File): Blob => {
  return new Blob([file], { type: file.type });
};

// Helper function to convert Blob to data URL for display
export const blobToDataURL = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};
