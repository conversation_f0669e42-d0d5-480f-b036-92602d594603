/**
 * Local Image Manager for VR720 Panoramic Images
 * Handles local panoramic images from the s1234全景效果图 folder
 */

export interface PanoramicImage {
  id: string;
  name: string;
  category: string;
  description: string;
  localPath: string;
  publicPath: string;
  thumbnail?: string;
  metadata: {
    width: number;
    height: number;
    fileSize: string;
    format: string;
    isEquirectangular: boolean;
  };
}

/**
 * Sample panoramic images from the s1234全景效果图 collection
 * These would be copied to the public folder for web access
 */
export const PANORAMIC_IMAGES: PanoramicImage[] = [
  // Living Rooms / 客厅
  {
    id: 'living-room-1',
    name: 'Modern Living Room',
    category: 'Living Room',
    description: 'Spacious modern living room with contemporary design',
    localPath: 'fanli/客厅/淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg',
    publicPath: '/panoramas/living-room-modern-1.jpg',
    thumbnail: '/thumbnails/living-room-modern-1-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.5MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },
  {
    id: 'living-room-2',
    name: 'Luxury Living Room',
    category: 'Living Room',
    description: 'Elegant luxury living room with premium furnishing',
    localPath: 'fanli/客厅/淘宝店铺：三老爹设计素材基地-淘宝网 (5).jpg',
    publicPath: '/panoramas/living-room-luxury-2.jpg',
    thumbnail: '/thumbnails/living-room-luxury-2-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '3.1MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },
  {
    id: 'living-room-3',
    name: 'Cozy Living Room',
    category: 'Living Room',
    description: 'Warm and cozy living room with comfortable seating',
    localPath: 'fanli/客厅/淘宝店铺：三老爹设计素材基地-淘宝网 (10).jpg',
    publicPath: '/panoramas/living-room-cozy-3.jpg',
    thumbnail: '/thumbnails/living-room-cozy-3-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.8MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },
  
  // Bedrooms / 卧室
  {
    id: 'bedroom-1',
    name: 'Master Bedroom',
    category: 'Bedroom',
    description: 'Spacious master bedroom with elegant design',
    localPath: 'fanli/卧室/淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg',
    publicPath: '/panoramas/bedroom-master-1.jpg',
    thumbnail: '/thumbnails/bedroom-master-1-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.8MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },
  {
    id: 'bedroom-2',
    name: 'Guest Bedroom',
    category: 'Bedroom',
    description: 'Comfortable guest bedroom with natural lighting',
    localPath: 'fanli/卧室/淘宝店铺：三老爹设计素材基地-淘宝网 (5).jpg',
    publicPath: '/panoramas/bedroom-guest-2.jpg',
    thumbnail: '/thumbnails/bedroom-guest-2-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.3MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },

  // Kitchen / 厨房
  {
    id: 'kitchen-1',
    name: 'Modern Kitchen',
    category: 'Kitchen',
    description: 'Contemporary kitchen with premium appliances',
    localPath: 'fanli/厨房/淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg',
    publicPath: '/panoramas/kitchen-modern-1.jpg',
    thumbnail: '/thumbnails/kitchen-modern-1-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.7MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },
  {
    id: 'kitchen-2',
    name: 'Open Kitchen',
    category: 'Kitchen',
    description: 'Open concept kitchen with dining area',
    localPath: 'fanli/厨房/淘宝店铺：三老爹设计素材基地-淘宝网 (3).jpg',
    publicPath: '/panoramas/kitchen-open-2.jpg',
    thumbnail: '/thumbnails/kitchen-open-2-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.9MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },

  // Bathroom / 浴室
  {
    id: 'bathroom-1',
    name: 'Master Bathroom',
    category: 'Bathroom',
    description: 'Luxurious master bathroom with modern fixtures',
    localPath: 'fanli/卫浴/淘宝店铺：三老爹设计素材基地-淘宝网 (1).jpg',
    publicPath: '/panoramas/bathroom-master-1.jpg',
    thumbnail: '/thumbnails/bathroom-master-1-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.4MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },
  {
    id: 'bathroom-2',
    name: 'Guest Bathroom',
    category: 'Bathroom',
    description: 'Elegant guest bathroom with contemporary design',
    localPath: 'fanli/卫浴/淘宝店铺：三老爹设计素材基地-淘宝网 (3).jpg',
    publicPath: '/panoramas/bathroom-guest-2.jpg',
    thumbnail: '/thumbnails/bathroom-guest-2-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.2MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },

  // Dining Room / 餐厅
  {
    id: 'dining-1',
    name: 'Formal Dining Room',
    category: 'Dining Room',
    description: 'Elegant formal dining room with chandelier',
    localPath: 'F:/BaiduNetdiskDownload/s1234全景效果图/餐厅/正式餐厅1.jpg',
    publicPath: '/panoramas/dining-formal-1.jpg',
    thumbnail: '/thumbnails/dining-formal-1-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.6MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },

  // Office / 书房
  {
    id: 'office-1',
    name: 'Home Office',
    category: 'Office',
    description: 'Modern home office with built-in shelving',
    localPath: 'F:/BaiduNetdiskDownload/s1234全景效果图/书房/家庭办公室1.jpg',
    publicPath: '/panoramas/office-home-1.jpg',
    thumbnail: '/thumbnails/office-home-1-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '2.2MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },

  // Exterior / 外景
  {
    id: 'exterior-1',
    name: 'Front Entrance',
    category: 'Exterior',
    description: 'Grand front entrance with landscaping',
    localPath: 'F:/BaiduNetdiskDownload/s1234全景效果图/外景/前门入口1.jpg',
    publicPath: '/panoramas/exterior-entrance-1.jpg',
    thumbnail: '/thumbnails/exterior-entrance-1-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '3.2MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  },
  {
    id: 'exterior-2',
    name: 'Backyard Garden',
    category: 'Exterior',
    description: 'Beautiful backyard with garden and patio',
    localPath: 'F:/BaiduNetdiskDownload/s1234全景效果图/外景/后院花园2.jpg',
    publicPath: '/panoramas/exterior-garden-2.jpg',
    thumbnail: '/thumbnails/exterior-garden-2-thumb.jpg',
    metadata: {
      width: 4096,
      height: 2048,
      fileSize: '3.5MB',
      format: 'JPEG',
      isEquirectangular: true
    }
  }
];

/**
 * Get panoramic images by category
 */
export function getPanoramicImagesByCategory(category: string): PanoramicImage[] {
  return PANORAMIC_IMAGES.filter(img => img.category === category);
}

/**
 * Get all available categories
 */
export function getPanoramicCategories(): string[] {
  const categories = new Set(PANORAMIC_IMAGES.map(img => img.category));
  return Array.from(categories);
}

/**
 * Get random panoramic images for demo
 */
export function getRandomPanoramicImages(count: number = 4): PanoramicImage[] {
  const shuffled = [...PANORAMIC_IMAGES].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

/**
 * Instructions for setting up local panoramic images
 */
export const SETUP_INSTRUCTIONS = {
  title: "Setting Up Local Panoramic Images",
  steps: [
    {
      step: 1,
      title: "Create Public Directory",
      description: "Create 'panoramas' and 'thumbnails' folders in the public directory",
      command: "mkdir public/panoramas public/thumbnails"
    },
    {
      step: 2,
      title: "Copy Panoramic Images",
      description: "Copy selected panoramic images from your local folder to public/panoramas/",
      example: "Copy F:/BaiduNetdiskDownload/s1234全景效果图/客厅/现代客厅1.jpg to public/panoramas/living-room-modern-1.jpg"
    },
    {
      step: 3,
      title: "Generate Thumbnails",
      description: "Create thumbnail versions (300x150) of each panoramic image",
      note: "You can use image editing software or online tools to resize images"
    },
    {
      step: 4,
      title: "Update Image Paths",
      description: "Ensure all publicPath values in PANORAMIC_IMAGES match your copied files"
    }
  ],
  notes: [
    "Panoramic images should be in equirectangular format (2:1 aspect ratio)",
    "Recommended resolution: 4096x2048 or higher for best VR experience",
    "File formats: JPEG, PNG, or WebP",
    "Keep file sizes reasonable for web loading (under 5MB per image)"
  ]
};

/**
 * Validate if an image is suitable for panoramic viewing
 */
export function validatePanoramicImage(width: number, height: number): {
  isValid: boolean;
  aspectRatio: number;
  recommendation: string;
} {
  const aspectRatio = width / height;
  const isEquirectangular = Math.abs(aspectRatio - 2) < 0.1; // Allow 10% tolerance
  
  return {
    isValid: isEquirectangular,
    aspectRatio,
    recommendation: isEquirectangular 
      ? "Perfect for 360° VR viewing"
      : `Aspect ratio is ${aspectRatio.toFixed(2)}:1. For best VR experience, use 2:1 ratio images.`
  };
}
