// Local storage management utilities

export interface StoredImageData {
  id: string;
  name: string;
  size: number;
  type: string;
  uploadTime: number;
  dataUrl: string; // Base64 encoded image data
  metadata?: {
    width?: number;
    height?: number;
    aspectRatio?: number;
  };
}

export interface StoredHouseData {
  id: string;
  name: string;
  address: string;
  rooms: any[];
  metadata: any;
  lastModified: number;
}

const STORAGE_KEYS = {
  UPLOADED_IMAGES: 'vr720_uploaded_images',
  HOUSES: 'vr720_houses',
  USER_PREFERENCES: 'vr720_preferences',
  APP_VERSION: 'vr720_app_version'
} as const;

const CURRENT_VERSION = '1.0.0';

// 检查localStorage是否可用
const isLocalStorageAvailable = (): boolean => {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
};

// 安全的JSON解析
const safeJsonParse = <T>(jsonString: string | null, defaultValue: T): T => {
  if (!jsonString) return defaultValue;
  
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('Failed to parse JSON from localStorage:', error);
    return defaultValue;
  }
};

// 安全的localStorage操作
const safeSetItem = (key: string, value: any): boolean => {
  if (!isLocalStorageAvailable()) return false;
  
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error('Failed to save to localStorage:', error);
    // 如果存储失败，可能是空间不足，尝试清理旧数据
    if (error instanceof DOMException && error.code === 22) {
      console.warn('localStorage quota exceeded, attempting cleanup...');
      cleanupOldData();
      try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch {
        return false;
      }
    }
    return false;
  }
};

const safeGetItem = <T>(key: string, defaultValue: T): T => {
  if (!isLocalStorageAvailable()) return defaultValue;
  
  try {
    const item = localStorage.getItem(key);
    return safeJsonParse(item, defaultValue);
  } catch (error) {
    console.error('Failed to read from localStorage:', error);
    return defaultValue;
  }
};

// 清理旧数据
const cleanupOldData = (): void => {
  try {
    const images = getStoredImages();
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    // 删除一周前的图片
    const recentImages = images.filter(img => img.uploadTime > oneWeekAgo);
    
    if (recentImages.length < images.length) {
      console.log(`Cleaned up ${images.length - recentImages.length} old images`);
      safeSetItem(STORAGE_KEYS.UPLOADED_IMAGES, recentImages);
    }
  } catch (error) {
    console.error('Failed to cleanup old data:', error);
  }
};

// 图片存储相关函数
export const saveImageToStorage = (imageData: StoredImageData): boolean => {
  const existingImages = getStoredImages();
  
  // 检查是否已存在相同ID的图片
  const existingIndex = existingImages.findIndex(img => img.id === imageData.id);
  
  if (existingIndex >= 0) {
    // 更新现有图片
    existingImages[existingIndex] = imageData;
  } else {
    // 添加新图片
    existingImages.push(imageData);
  }
  
  return safeSetItem(STORAGE_KEYS.UPLOADED_IMAGES, existingImages);
};

export const getStoredImages = (): StoredImageData[] => {
  return safeGetItem(STORAGE_KEYS.UPLOADED_IMAGES, []);
};

export const removeImageFromStorage = (imageId: string): boolean => {
  const existingImages = getStoredImages();
  const filteredImages = existingImages.filter(img => img.id !== imageId);
  
  return safeSetItem(STORAGE_KEYS.UPLOADED_IMAGES, filteredImages);
};

export const clearAllImages = (): boolean => {
  return safeSetItem(STORAGE_KEYS.UPLOADED_IMAGES, []);
};

// 房屋数据存储相关函数
export const saveHouseToStorage = (houseData: StoredHouseData): boolean => {
  const existingHouses = getStoredHouses();
  
  const existingIndex = existingHouses.findIndex(house => house.id === houseData.id);
  
  if (existingIndex >= 0) {
    existingHouses[existingIndex] = { ...houseData, lastModified: Date.now() };
  } else {
    existingHouses.push({ ...houseData, lastModified: Date.now() });
  }
  
  return safeSetItem(STORAGE_KEYS.HOUSES, existingHouses);
};

export const getStoredHouses = (): StoredHouseData[] => {
  return safeGetItem(STORAGE_KEYS.HOUSES, []);
};

export const removeHouseFromStorage = (houseId: string): boolean => {
  const existingHouses = getStoredHouses();
  const filteredHouses = existingHouses.filter(house => house.id !== houseId);
  
  return safeSetItem(STORAGE_KEYS.HOUSES, filteredHouses);
};

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  defaultUploadQuality: number;
  autoSave: boolean;
  showTutorials: boolean;
  language: string;
}

const DEFAULT_PREFERENCES: UserPreferences = {
  theme: 'auto',
  defaultUploadQuality: 0.9,
  autoSave: true,
  showTutorials: true,
  language: 'zh-CN'
};

export const saveUserPreferences = (preferences: Partial<UserPreferences>): boolean => {
  const currentPreferences = getUserPreferences();
  const updatedPreferences = { ...currentPreferences, ...preferences };
  
  return safeSetItem(STORAGE_KEYS.USER_PREFERENCES, updatedPreferences);
};

export const getUserPreferences = (): UserPreferences => {
  return safeGetItem(STORAGE_KEYS.USER_PREFERENCES, DEFAULT_PREFERENCES);
};

// 应用版本管理
export const checkAppVersion = (): { isNewVersion: boolean; previousVersion?: string } => {
  const storedVersion = safeGetItem(STORAGE_KEYS.APP_VERSION, null);
  
  if (!storedVersion || storedVersion !== CURRENT_VERSION) {
    safeSetItem(STORAGE_KEYS.APP_VERSION, CURRENT_VERSION);
    return {
      isNewVersion: true,
      previousVersion: storedVersion
    };
  }
  
  return { isNewVersion: false };
};

// 获取存储使用情况
export const getStorageUsage = (): { used: number; total: number; percentage: number } => {
  if (!isLocalStorageAvailable()) {
    return { used: 0, total: 0, percentage: 0 };
  }
  
  try {
    let used = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        used += localStorage[key].length + key.length;
      }
    }
    
    // localStorage通常限制为5-10MB，这里假设5MB
    const total = 5 * 1024 * 1024; // 5MB in bytes
    const percentage = (used / total) * 100;
    
    return { used, total, percentage };
  } catch (error) {
    console.error('Failed to calculate storage usage:', error);
    return { used: 0, total: 0, percentage: 0 };
  }
};

// 导出所有数据（用于备份）
export const exportAllData = (): string => {
  const data = {
    images: getStoredImages(),
    houses: getStoredHouses(),
    preferences: getUserPreferences(),
    version: CURRENT_VERSION,
    exportTime: Date.now()
  };
  
  return JSON.stringify(data, null, 2);
};

// 导入数据（用于恢复）
export const importAllData = (jsonData: string): boolean => {
  try {
    const data = JSON.parse(jsonData);
    
    if (data.images) {
      safeSetItem(STORAGE_KEYS.UPLOADED_IMAGES, data.images);
    }
    
    if (data.houses) {
      safeSetItem(STORAGE_KEYS.HOUSES, data.houses);
    }
    
    if (data.preferences) {
      safeSetItem(STORAGE_KEYS.USER_PREFERENCES, data.preferences);
    }
    
    return true;
  } catch (error) {
    console.error('Failed to import data:', error);
    return false;
  }
};

// 清理所有应用数据
export const clearAllAppData = (): boolean => {
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    return true;
  } catch (error) {
    console.error('Failed to clear app data:', error);
    return false;
  }
};

// 初始化存储（应用启动时调用）
export const initializeStorage = (): void => {
  const versionCheck = checkAppVersion();
  
  if (versionCheck.isNewVersion) {
    console.log(`VR720 app updated to version ${CURRENT_VERSION}`);
    
    // 如果是新版本，可以在这里执行数据迁移逻辑
    if (versionCheck.previousVersion) {
      console.log(`Migrating from version ${versionCheck.previousVersion}`);
      // 执行数据迁移...
    }
  }
  
  // 检查存储使用情况
  const usage = getStorageUsage();
  if (usage.percentage > 80) {
    console.warn(`localStorage usage is high: ${usage.percentage.toFixed(1)}%`);
    cleanupOldData();
  }
};
