/**
 * Panorama Stitching Utilities
 * This is a simplified implementation for demonstration purposes.
 * In a real application, you would use more sophisticated computer vision algorithms.
 */

export interface StitchingOptions {
  outputWidth?: number;
  outputHeight?: number;
  blendMode?: 'linear' | 'multiband';
  seamFinding?: boolean;
}

export interface StitchingResult {
  panoramaUrl: string;
  success: boolean;
  error?: string;
  metadata: {
    inputImages: number;
    outputDimensions: { width: number; height: number };
    processingTime: number;
  };
}

/**
 * Create a panorama from multiple images
 * This is a simplified version that creates a horizontal panorama
 */
export async function stitchImagesToPanorama(
  imageUrls: string[],
  options: StitchingOptions = {}
): Promise<StitchingResult> {
  const startTime = Date.now();
  
  try {
    if (imageUrls.length === 0) {
      throw new Error('No images provided for stitching');
    }

    // If only one image, convert it to panoramic format
    if (imageUrls.length === 1) {
      return createSingleImagePanorama(imageUrls[0], options);
    }

    // Load all images
    const images = await Promise.all(
      imageUrls.map(url => loadImage(url))
    );

    // Calculate output dimensions
    const outputWidth = options.outputWidth || 2048;
    const outputHeight = options.outputHeight || 1024;

    // Create canvas for stitching
    const canvas = document.createElement('canvas');
    canvas.width = outputWidth;
    canvas.height = outputHeight;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Failed to create canvas context');
    }

    // Clear canvas with black background
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, outputWidth, outputHeight);

    // Simple horizontal stitching
    await stitchImagesHorizontally(ctx, images, outputWidth, outputHeight);

    // Convert to data URL
    const panoramaUrl = canvas.toDataURL('image/jpeg', 0.9);

    const processingTime = Date.now() - startTime;

    return {
      panoramaUrl,
      success: true,
      metadata: {
        inputImages: imageUrls.length,
        outputDimensions: { width: outputWidth, height: outputHeight },
        processingTime
      }
    };

  } catch (error) {
    return {
      panoramaUrl: '',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        inputImages: imageUrls.length,
        outputDimensions: { width: 0, height: 0 },
        processingTime: Date.now() - startTime
      }
    };
  }
}

/**
 * Convert a single image to panoramic format
 */
async function createSingleImagePanorama(
  imageUrl: string,
  options: StitchingOptions
): Promise<StitchingResult> {
  const startTime = Date.now();
  
  try {
    const image = await loadImage(imageUrl);
    
    // Calculate panoramic dimensions (2:1 aspect ratio)
    const outputWidth = options.outputWidth || 2048;
    const outputHeight = options.outputHeight || outputWidth / 2;

    const canvas = document.createElement('canvas');
    canvas.width = outputWidth;
    canvas.height = outputHeight;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Failed to create canvas context');
    }

    // Fill with black background
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, outputWidth, outputHeight);

    // Calculate scaling to fit height and center horizontally
    const scale = outputHeight / image.height;
    const scaledWidth = image.width * scale;
    const x = (outputWidth - scaledWidth) / 2;

    // Draw the image centered
    ctx.drawImage(image, x, 0, scaledWidth, outputHeight);

    // If the image is narrower than the canvas, repeat it to fill
    if (scaledWidth < outputWidth) {
      // Repeat on the left
      let leftX = x - scaledWidth;
      while (leftX > -scaledWidth) {
        ctx.drawImage(image, leftX, 0, scaledWidth, outputHeight);
        leftX -= scaledWidth;
      }

      // Repeat on the right
      let rightX = x + scaledWidth;
      while (rightX < outputWidth) {
        ctx.drawImage(image, rightX, 0, scaledWidth, outputHeight);
        rightX += scaledWidth;
      }
    }

    const panoramaUrl = canvas.toDataURL('image/jpeg', 0.9);
    const processingTime = Date.now() - startTime;

    return {
      panoramaUrl,
      success: true,
      metadata: {
        inputImages: 1,
        outputDimensions: { width: outputWidth, height: outputHeight },
        processingTime
      }
    };

  } catch (error) {
    return {
      panoramaUrl: imageUrl, // Fallback to original image
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        inputImages: 1,
        outputDimensions: { width: 0, height: 0 },
        processingTime: Date.now() - startTime
      }
    };
  }
}

/**
 * Stitch images horizontally with blending
 */
async function stitchImagesHorizontally(
  ctx: CanvasRenderingContext2D,
  images: HTMLImageElement[],
  outputWidth: number,
  outputHeight: number
): Promise<void> {
  const imageWidth = outputWidth / images.length;
  const overlapWidth = imageWidth * 0.1; // 10% overlap

  for (let i = 0; i < images.length; i++) {
    const image = images[i];
    const x = i * (imageWidth - overlapWidth);
    
    // Scale image to fit
    const scale = outputHeight / image.height;
    const scaledWidth = Math.min(image.width * scale, imageWidth + overlapWidth);

    // Draw image
    ctx.drawImage(image, x, 0, scaledWidth, outputHeight);

    // Apply blending for overlap regions
    if (i > 0) {
      applyBlending(ctx, x, 0, overlapWidth, outputHeight);
    }
  }
}

/**
 * Apply simple linear blending for overlap regions
 */
function applyBlending(
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number
): void {
  const imageData = ctx.getImageData(x, y, width, height);
  const data = imageData.data;

  for (let i = 0; i < width; i++) {
    const alpha = i / width; // Linear blend factor
    
    for (let j = 0; j < height; j++) {
      const pixelIndex = (j * width + i) * 4;
      
      // Apply alpha blending
      data[pixelIndex + 3] = Math.round(data[pixelIndex + 3] * alpha);
    }
  }

  ctx.putImageData(imageData, x, y);
}

/**
 * Load an image from URL
 */
function loadImage(url: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
    
    img.src = url;
  });
}

/**
 * Validate if images are suitable for panorama stitching
 */
export function validateImagesForStitching(imageUrls: string[]): {
  valid: boolean;
  warnings: string[];
  recommendations: string[];
} {
  const warnings: string[] = [];
  const recommendations: string[] = [];

  if (imageUrls.length === 0) {
    warnings.push('No images provided');
    return { valid: false, warnings, recommendations };
  }

  if (imageUrls.length === 1) {
    recommendations.push('Single image will be converted to panoramic format');
    recommendations.push('For better results, capture multiple overlapping photos');
  }

  if (imageUrls.length > 10) {
    warnings.push('Large number of images may result in slower processing');
    recommendations.push('Consider using 3-8 images for optimal results');
  }

  recommendations.push('Ensure images have 30-50% overlap for best stitching');
  recommendations.push('Use consistent lighting and camera settings');
  recommendations.push('Keep the camera level during capture');

  return {
    valid: true,
    warnings,
    recommendations
  };
}

/**
 * Get optimal panorama dimensions based on input images
 */
export function getOptimalPanoramaDimensions(imageCount: number): {
  width: number;
  height: number;
} {
  // Standard panoramic aspect ratios
  const aspectRatio = 2; // 2:1 for equirectangular panoramas
  
  let baseWidth: number;
  
  if (imageCount === 1) {
    baseWidth = 2048;
  } else if (imageCount <= 3) {
    baseWidth = 3072;
  } else if (imageCount <= 6) {
    baseWidth = 4096;
  } else {
    baseWidth = 6144;
  }

  return {
    width: baseWidth,
    height: baseWidth / aspectRatio
  };
}
