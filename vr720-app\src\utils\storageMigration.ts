// Storage migration utility to move data from localStorage to IndexedDB
import { getStoredImages, clearAllImages } from './localStorage';
import { indexedDBStorage } from './indexedDBStorage';

export interface MigrationResult {
  success: boolean;
  migratedCount: number;
  failedCount: number;
  errors: string[];
}

// Convert base64 data URL to Blob
const dataURLToBlob = (dataURL: string): Blob => {
  const arr = dataURL.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new Blob([u8arr], { type: mime });
};

// Migrate images from localStorage to IndexedDB
export const migrateImagesToIndexedDB = async (): Promise<MigrationResult> => {
  const result: MigrationResult = {
    success: false,
    migratedCount: 0,
    failedCount: 0,
    errors: []
  };

  try {
    console.log('Starting migration from localStorage to IndexedDB...');
    
    // Get all images from localStorage
    const localStorageImages = getStoredImages();
    
    if (localStorageImages.length === 0) {
      console.log('No images found in localStorage to migrate');
      result.success = true;
      return result;
    }

    console.log(`Found ${localStorageImages.length} images in localStorage`);

    // Migrate each image
    for (const localImage of localStorageImages) {
      try {
        // Convert base64 dataURL to Blob
        const blob = dataURLToBlob(localImage.dataUrl);
        
        // Create IndexedDB compatible image data
        const indexedDBImage = {
          id: localImage.id,
          name: localImage.name,
          size: localImage.size,
          type: localImage.type,
          uploadTime: localImage.uploadTime,
          blob: blob,
          metadata: localImage.metadata
        };

        // Save to IndexedDB
        const saved = await indexedDBStorage.saveImage(indexedDBImage);
        
        if (saved) {
          result.migratedCount++;
          console.log(`Successfully migrated image: ${localImage.name}`);
        } else {
          result.failedCount++;
          result.errors.push(`Failed to save image: ${localImage.name}`);
        }
      } catch (error) {
        result.failedCount++;
        const errorMsg = `Failed to migrate image ${localImage.name}: ${error}`;
        result.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    // If migration was successful, clear localStorage
    if (result.migratedCount > 0 && result.failedCount === 0) {
      const cleared = clearAllImages();
      if (cleared) {
        console.log('Successfully cleared localStorage after migration');
        result.success = true;
      } else {
        result.errors.push('Failed to clear localStorage after migration');
      }
    } else if (result.migratedCount > 0) {
      // Partial success - some images migrated
      console.warn(`Partial migration: ${result.migratedCount} succeeded, ${result.failedCount} failed`);
      result.success = true; // Consider partial success as success
    }

    console.log(`Migration completed: ${result.migratedCount} migrated, ${result.failedCount} failed`);
    
  } catch (error) {
    result.errors.push(`Migration failed: ${error}`);
    console.error('Migration failed:', error);
  }

  return result;
};

// Check if migration is needed
export const isMigrationNeeded = (): boolean => {
  try {
    const localStorageImages = getStoredImages();
    return localStorageImages.length > 0;
  } catch (error) {
    console.error('Failed to check if migration is needed:', error);
    return false;
  }
};

// Get storage usage information
export const getStorageInfo = async () => {
  try {
    // Get localStorage usage
    let localStorageUsed = 0;
    try {
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          localStorageUsed += localStorage[key].length + key.length;
        }
      }
    } catch (error) {
      console.warn('Could not calculate localStorage usage:', error);
    }

    // Get IndexedDB usage estimate
    let indexedDBUsage = { usage: 0, quota: 0 };
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        indexedDBUsage = {
          usage: estimate.usage || 0,
          quota: estimate.quota || 0
        };
      }
    } catch (error) {
      console.warn('Could not get IndexedDB storage estimate:', error);
    }

    // Get image counts
    const localStorageImageCount = getStoredImages().length;
    const indexedDBImages = await indexedDBStorage.getAllImages();
    const indexedDBImageCount = indexedDBImages.length;

    return {
      localStorage: {
        used: localStorageUsed,
        imageCount: localStorageImageCount,
        quota: 5 * 1024 * 1024 // Assume 5MB limit
      },
      indexedDB: {
        used: indexedDBUsage.usage,
        quota: indexedDBUsage.quota,
        imageCount: indexedDBImageCount
      }
    };
  } catch (error) {
    console.error('Failed to get storage info:', error);
    return null;
  }
};

// Auto-migration function that can be called on app startup
export const autoMigrateIfNeeded = async (): Promise<boolean> => {
  try {
    if (isMigrationNeeded()) {
      console.log('Auto-migration needed, starting migration...');
      const result = await migrateImagesToIndexedDB();
      
      if (result.success) {
        console.log(`Auto-migration completed successfully: ${result.migratedCount} images migrated`);
        return true;
      } else {
        console.error('Auto-migration failed:', result.errors);
        return false;
      }
    } else {
      console.log('No migration needed');
      return true;
    }
  } catch (error) {
    console.error('Auto-migration failed:', error);
    return false;
  }
};
